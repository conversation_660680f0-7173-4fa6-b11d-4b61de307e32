﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XECOM_Main_LocalServer.Models
{
    public class Product
    {
        [Key]
        public long? ID { get; set; }

        public string? Category { get; set; }
        public string? Assortment_type { get; set; }
        public string? Name { get; set; }
        public string? Stock_Code { get; set; }
        public string? Brand { get; set; }
        public string? SKU_ID { get; set; }
        public string? Model_ID { get; set; }
        public string? Target_Age_Groups { get; set; }
        public string? Target_Genders { get; set; }
        public string? Season { get; set; }
        public string? Description { get; set; }
        public int? Material_Upper_Material_Clothing1_Ratio { get; set; }
        public string? Material_Upper_Material_Clothing1 { get; set; }
        public int? Material_Upper_Material_Clothing2_Ratio { get; set; }
        public string? Material_Upper_Material_Clothing2 { get; set; }
        public int? Material_Upper_Material_Clothing3_Ratio { get; set; }
        public string? Material_Upper_Material_Clothing3 { get; set; }
        public int? Material_Upper_Material_Clothing4_Ratio { get; set; }
        public string? Material_Upper_Material_Clothing4 { get; set; }
        public int? Material_Upper_Material_Clothing5_Ratio { get; set; }
        public string? Material_Upper_Material_Clothing5 { get; set; }
        public string? Media1_URL { get; set; }
        public string? Media2_URL { get; set; }
        public string? Media3_URL { get; set; }
        public string? Media4_URL { get; set; }
        public string? Media5_URL { get; set; }
        public string? Color_Code_Primary { get; set; }
        public string? Supplier_Color { get; set; }
        public string? Size { get; set; }
        public string? Size1 { get; set; }
        public string? EAN { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal? Stock_Quantity { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal? Regular_Price { get; set; }

        [Column(TypeName = "decimal(18,8)")]
        public decimal? Discount_Price { get; set; }

        public string? Currency { get; set; }

        [Column("stok_last_update")]
        public DateTime? Stock_Last_Update { get; set; }
    }
}
