2024-10-23 14:24:52.892 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 14:24:53.635 +03:00 [WRN] No store type was specified for the decimal property 'Discount_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:24:53.640 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:24:53.641 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:24:53.643 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:24:53.644 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:24:53.645 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:24:53.646 +03:00 [WRN] No store type was specified for the decimal property 'Regular_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:24:53.648 +03:00 [WRN] No store type was specified for the decimal property 'Stock_Quantity' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:24:54.551 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 14:24:56.175 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 14:24:56.349 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 14:25:21.422 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 14:25:21.882 +03:00 [WRN] No store type was specified for the decimal property 'Discount_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:25:21.886 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:25:21.888 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:25:21.889 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:25:21.890 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:25:21.892 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:25:21.893 +03:00 [WRN] No store type was specified for the decimal property 'Regular_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:25:21.895 +03:00 [WRN] No store type was specified for the decimal property 'Stock_Quantity' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 14:25:22.336 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 14:25:25.593 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 14:25:25.724 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 14:25:32.309 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method28(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method28(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 14:25:32.378 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method28(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-23 14:30:25.752 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 14:30:26.589 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 14:30:26.680 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 14:35:26.722 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 14:35:27.589 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 14:35:27.679 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 14:40:27.708 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 14:40:28.562 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 14:40:28.650 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 14:45:28.684 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 14:45:29.517 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 14:45:29.602 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 14:50:29.649 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 14:50:30.490 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 14:50:30.576 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 14:55:30.597 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 14:55:31.437 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 14:55:31.527 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:00:31.567 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:00:36.723 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:00:36.808 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:05:36.826 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:05:37.620 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:05:37.712 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:10:37.740 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:10:38.572 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:10:38.661 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:15:38.702 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:15:39.520 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:15:39.605 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:20:39.638 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:20:40.432 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:20:40.517 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:25:40.538 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:25:41.330 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:25:41.424 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:30:41.448 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:30:42.295 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:30:42.382 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:35:42.417 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:35:43.260 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:35:43.357 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:40:43.386 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:40:44.269 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:40:44.363 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:45:44.393 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:46:05.817 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:46:05.903 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:51:05.934 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:51:06.927 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:51:07.038 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 15:56:07.134 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 15:56:09.258 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 15:56:09.720 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:01:10.121 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:01:11.942 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:01:12.183 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:06:12.261 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:06:14.117 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:06:14.351 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:11:14.405 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:11:16.168 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:11:16.407 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:16:16.460 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:16:17.414 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:16:17.525 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:21:17.564 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:21:18.462 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:21:18.553 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:26:18.597 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:26:19.514 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:26:19.602 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:31:19.631 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:31:20.478 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:31:20.575 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:36:20.616 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:36:21.462 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:36:21.550 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:41:21.575 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:41:22.423 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:41:22.511 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:46:22.547 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:46:23.384 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:46:23.471 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:51:23.509 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:51:24.376 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:51:24.477 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 16:56:24.504 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 16:56:25.881 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 16:56:25.980 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:01:26.024 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:01:26.904 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:01:26.995 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:06:27.032 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:06:27.924 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:06:28.008 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:11:28.051 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:11:28.921 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:11:29.004 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:16:29.026 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:16:29.892 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:16:29.986 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:21:30.018 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:21:31.007 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:21:31.122 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:26:31.170 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:26:32.121 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:26:32.211 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:31:32.241 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:31:33.126 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:31:33.210 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:36:33.249 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:36:34.173 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:36:34.268 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:41:34.309 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:41:38.227 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:41:38.315 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:46:38.351 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:46:39.328 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:46:39.420 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 17:51:39.450 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-23 17:51:40.376 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-23 17:51:40.462 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 125
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 57
2024-10-23 19:17:20.775 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 19:37:14.550 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 19:42:19.307 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 19:42:19.765 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:42:19.770 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:42:19.772 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:42:19.773 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:42:19.775 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:44:29.603 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 19:44:30.077 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:44:30.082 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:44:30.083 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:44:30.085 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:44:30.086 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:58:07.921 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 19:58:38.912 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:58:38.916 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:58:38.917 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:58:38.918 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 19:58:38.920 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:12:47.930 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 20:13:22.535 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:13:22.539 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:13:22.541 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:13:22.542 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:13:22.543 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:23:59.769 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 20:24:11.104 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:24:11.108 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:24:11.109 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:24:11.110 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:24:11.111 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:30:23.689 +03:00 [INF] Uygulama başlatılıyor
2024-10-23 20:30:50.158 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:30:50.162 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:30:50.164 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:30:50.165 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-23 20:30:50.166 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
