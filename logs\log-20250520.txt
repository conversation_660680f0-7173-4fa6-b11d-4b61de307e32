2025-05-20 17:41:19.108 +03:00 [INF] Netsis API yapılandırması: URL=http://localhost:7070/, Username=NETSIS, DbName=AKAL2024-04
2025-05-20 17:41:19.236 +03:00 [INF] Netsis API yapılandırması başarıyla yüklendi.
2025-05-20 17:41:19.238 +03:00 [INF] Uygulama başlatılıyor
2025-05-20 17:41:19.263 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2025-05-20 17:41:21.156 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 17:41:21.257 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2025-05-20 17:41:21.259 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2025-05-20 17:41:22.444 +03:00 [INF] Toplam ürün sayısı: 687
2025-05-20 17:41:22.510 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2025-05-20 17:41:50.385 +03:00 [INF] ============ ReceiveOrders STARTED ============
2025-05-20 17:41:50.389 +03:00 [INF] ReceiveOrders is called with encrypted data length: 4736
2025-05-20 17:41:50.391 +03:00 [INF] Decryption and cleaning successful, length: 3536, first 100 chars: [{"Id":1142,"OrderNumber":"21085","OrderedAt":"2025-05-19T16:42:16.295","Deleted":false,"LineItems":
2025-05-20 17:41:50.465 +03:00 [INF] Deserialization successful, orders count: 13
2025-05-20 17:41:50.467 +03:00 [INF] ============ ERP SYNC STARTED ============
2025-05-20 17:41:50.469 +03:00 [INF] ===== ERPService INITIALIZATION =====
2025-05-20 17:41:50.470 +03:00 [INF] Loaded configuration:
2025-05-20 17:41:50.471 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:41:50.473 +03:00 [INF]   Username: NETSIS
2025-05-20 17:41:50.474 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:41:50.476 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:41:50.477 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:41:50.478 +03:00 [INF]   BranchCode: 203
2025-05-20 17:41:50.479 +03:00 [INF]   Password Set: true
2025-05-20 17:41:50.481 +03:00 [INF]   DbPassword Set: true
2025-05-20 17:41:50.482 +03:00 [INF] ERPService initialized for Netsis REST API
2025-05-20 17:41:50.484 +03:00 [INF] ===== ERPService INITIALIZATION COMPLETE =====
2025-05-20 17:41:50.550 +03:00 [INF] Sipariş ERP'ye gönderiliyor: 21085
2025-05-20 17:41:50.619 +03:00 [INF] ============ CreateNetsisOrderAsync STARTED ============
2025-05-20 17:41:50.621 +03:00 [INF] CreateNetsisOrderAsync başladı. XmainOrderId=0, OrderNumber=21085
2025-05-20 17:41:50.626 +03:00 [INF] Sipariş zaten var mı kontrol ediliyor: 21085
2025-05-20 17:41:50.631 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:41:50.633 +03:00 [INF] Checking current token...
2025-05-20 17:41:50.635 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:41:50.638 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:41:50.640 +03:00 [INF] Token parameters:
2025-05-20 17:41:50.642 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:41:50.644 +03:00 [INF]   Username: NETSIS
2025-05-20 17:41:50.645 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:41:50.647 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:41:50.648 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:41:50.650 +03:00 [INF]   BranchCode: 203
2025-05-20 17:41:50.651 +03:00 [INF]   Password Length: 8
2025-05-20 17:41:50.653 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:41:50.655 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:41:54.736 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:41:54.747 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:41:54.750 +03:00 [ERR] Sipariş varlığı kontrolü sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.IsOrderAlreadyExistsAsync(String orderNumber) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 215
2025-05-20 17:41:54.758 +03:00 [INF] Sipariş için email bilgisi alınıyor. OrderNumber=21085
2025-05-20 17:41:54.760 +03:00 [WRN] Sipariş için email bilgisi bulunamadı. OrderNumber: 21085
2025-05-20 17:41:54.761 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Uyarısı - Sipariş için email bilgisi bulunamadı. OrderNumber: 21085 Email bilgisi olmadan işleme devam ediliyor. 
2025-05-20 17:41:54.764 +03:00 [INF] Cari hesap aranıyor: Email=, Phone=null
2025-05-20 17:41:54.765 +03:00 [WRN] Cari hesap araması için email veya telefon bilgisi bulunamadı
2025-05-20 17:41:54.767 +03:00 [INF] Cari hesap bulunamadı, yeni oluşturuluyor...
2025-05-20 17:41:54.770 +03:00 [INF] CreateCustomerWithRestApi başladı. OrderId=0
2025-05-20 17:41:54.782 +03:00 [INF] Cari hesap oluşturma isteği JSON (ilk 500 karakter): {"CariTemelBilgi":{"Sube_Kodu":1,"ISLETME_KODU":1,"CARI_KOD":"M21085","CARI_TEL":"","CARI_IL":"","ULKE_KODU":"TR","CARI_ISIM":"Web Sipari\u015Fi 21085","CARI_TIP":"A","CARI_ADRES":" ","CARI_ILCE":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","POSTAKODU":"","CM_RAP_TARIH":"2025-05-20T17:41:54.7721578+03:00","HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","UPDATE_KODU":"X","EMAIL":"","GSM1":"","KayitYapanKul":"WEB","KayitTarihi":"2025-05-20T17:41:54.7721603+03:00","OnayTipi":"A","MUSTERIBAZIKDV":"H"},"CariEkBil
2025-05-20 17:41:54.783 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:41:54.784 +03:00 [INF] Checking current token...
2025-05-20 17:41:54.785 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:41:54.787 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:41:54.788 +03:00 [INF] Token parameters:
2025-05-20 17:41:54.789 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:41:54.790 +03:00 [INF]   Username: NETSIS
2025-05-20 17:41:54.791 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:41:54.792 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:41:54.793 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:41:54.794 +03:00 [INF]   BranchCode: 203
2025-05-20 17:41:54.795 +03:00 [INF]   Password Length: 8
2025-05-20 17:41:54.796 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:41:54.797 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:41:58.886 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:41:58.897 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:41:58.899 +03:00 [ERR] Cari hesap oluşturma sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427
2025-05-20 17:41:58.907 +03:00 [WRN] BİLDİRİM: Cari Hesap Oluşturma Hatası - Sipariş No: 21085 Hata: No connection could be made because the target machine actively refused it. (localhost:7070) Stack Trace:    at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427 
2025-05-20 17:41:58.909 +03:00 [ERR] Cari hesap oluşturulamadı. OrderNumber: 21085
2025-05-20 17:41:58.910 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Hatası - Cari hesap oluşturulamadı. OrderNumber: 21085 
2025-05-20 17:41:58.912 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH ERROR ============
2025-05-20 17:41:58.914 +03:00 [ERR] CreateNetsisOrderAsync hata: Cari hesap oluşturulamadı
System.Exception: Cari hesap oluşturulamadı
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719
2025-05-20 17:41:58.917 +03:00 [WRN] BİLDİRİM: Sipariş Aktarım Hatası - Sipariş No: 21085 Hata: Cari hesap oluşturulamadı Stack Trace:    at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719 
2025-05-20 17:41:58.919 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH EXCEPTION ============
2025-05-20 17:41:58.928 +03:00 [ERR] Sipariş ERP'ye gönderilemedi: 21085
2025-05-20 17:41:58.930 +03:00 [INF] Sipariş ERP'ye gönderiliyor: 21087
2025-05-20 17:41:58.932 +03:00 [INF] ============ CreateNetsisOrderAsync STARTED ============
2025-05-20 17:41:58.933 +03:00 [INF] CreateNetsisOrderAsync başladı. XmainOrderId=0, OrderNumber=21087
2025-05-20 17:41:58.935 +03:00 [INF] Sipariş zaten var mı kontrol ediliyor: 21087
2025-05-20 17:41:58.936 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:41:58.937 +03:00 [INF] Checking current token...
2025-05-20 17:41:58.939 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:41:58.940 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:41:58.941 +03:00 [INF] Token parameters:
2025-05-20 17:41:58.942 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:41:58.944 +03:00 [INF]   Username: NETSIS
2025-05-20 17:41:58.945 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:41:58.946 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:41:58.947 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:41:58.948 +03:00 [INF]   BranchCode: 203
2025-05-20 17:41:58.949 +03:00 [INF]   Password Length: 8
2025-05-20 17:41:58.950 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:41:58.952 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:03.033 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:03.041 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:03.043 +03:00 [ERR] Sipariş varlığı kontrolü sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.IsOrderAlreadyExistsAsync(String orderNumber) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 215
2025-05-20 17:42:03.054 +03:00 [INF] Sipariş için email bilgisi alınıyor. OrderNumber=21087
2025-05-20 17:42:03.055 +03:00 [WRN] Sipariş için email bilgisi bulunamadı. OrderNumber: 21087
2025-05-20 17:42:03.056 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Uyarısı - Sipariş için email bilgisi bulunamadı. OrderNumber: 21087 Email bilgisi olmadan işleme devam ediliyor. 
2025-05-20 17:42:03.058 +03:00 [INF] Cari hesap aranıyor: Email=, Phone=null
2025-05-20 17:42:03.060 +03:00 [WRN] Cari hesap araması için email veya telefon bilgisi bulunamadı
2025-05-20 17:42:03.062 +03:00 [INF] Cari hesap bulunamadı, yeni oluşturuluyor...
2025-05-20 17:42:03.063 +03:00 [INF] CreateCustomerWithRestApi başladı. OrderId=0
2025-05-20 17:42:03.065 +03:00 [INF] Cari hesap oluşturma isteği JSON (ilk 500 karakter): {"CariTemelBilgi":{"Sube_Kodu":1,"ISLETME_KODU":1,"CARI_KOD":"M21087","CARI_TEL":"","CARI_IL":"","ULKE_KODU":"TR","CARI_ISIM":"Web Sipari\u015Fi 21087","CARI_TIP":"A","CARI_ADRES":" ","CARI_ILCE":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","POSTAKODU":"","CM_RAP_TARIH":"2025-05-20T17:42:03.0652918+03:00","HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","UPDATE_KODU":"X","EMAIL":"","GSM1":"","KayitYapanKul":"WEB","KayitTarihi":"2025-05-20T17:42:03.0652927+03:00","OnayTipi":"A","MUSTERIBAZIKDV":"H"},"CariEkBil
2025-05-20 17:42:03.067 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:03.068 +03:00 [INF] Checking current token...
2025-05-20 17:42:03.069 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:03.071 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:03.072 +03:00 [INF] Token parameters:
2025-05-20 17:42:03.073 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:03.075 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:03.076 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:03.077 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:03.079 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:03.080 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:03.082 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:03.083 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:03.084 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:07.157 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:07.164 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:07.166 +03:00 [ERR] Cari hesap oluşturma sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427
2025-05-20 17:42:07.174 +03:00 [WRN] BİLDİRİM: Cari Hesap Oluşturma Hatası - Sipariş No: 21087 Hata: No connection could be made because the target machine actively refused it. (localhost:7070) Stack Trace:    at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427 
2025-05-20 17:42:07.176 +03:00 [ERR] Cari hesap oluşturulamadı. OrderNumber: 21087
2025-05-20 17:42:07.177 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Hatası - Cari hesap oluşturulamadı. OrderNumber: 21087 
2025-05-20 17:42:07.178 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH ERROR ============
2025-05-20 17:42:07.180 +03:00 [ERR] CreateNetsisOrderAsync hata: Cari hesap oluşturulamadı
System.Exception: Cari hesap oluşturulamadı
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719
2025-05-20 17:42:07.182 +03:00 [WRN] BİLDİRİM: Sipariş Aktarım Hatası - Sipariş No: 21087 Hata: Cari hesap oluşturulamadı Stack Trace:    at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719 
2025-05-20 17:42:07.183 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH EXCEPTION ============
2025-05-20 17:42:07.185 +03:00 [ERR] Sipariş ERP'ye gönderilemedi: 21087
2025-05-20 17:42:07.186 +03:00 [INF] Sipariş ERP'ye gönderiliyor: 21086
2025-05-20 17:42:07.187 +03:00 [INF] ============ CreateNetsisOrderAsync STARTED ============
2025-05-20 17:42:07.187 +03:00 [INF] CreateNetsisOrderAsync başladı. XmainOrderId=0, OrderNumber=21086
2025-05-20 17:42:07.189 +03:00 [INF] Sipariş zaten var mı kontrol ediliyor: 21086
2025-05-20 17:42:07.190 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:07.191 +03:00 [INF] Checking current token...
2025-05-20 17:42:07.192 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:07.193 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:07.195 +03:00 [INF] Token parameters:
2025-05-20 17:42:07.196 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:07.197 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:07.198 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:07.199 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:07.200 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:07.201 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:07.203 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:07.204 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:07.205 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:11.283 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:11.290 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:11.292 +03:00 [ERR] Sipariş varlığı kontrolü sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.IsOrderAlreadyExistsAsync(String orderNumber) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 215
2025-05-20 17:42:11.300 +03:00 [INF] Sipariş için email bilgisi alınıyor. OrderNumber=21086
2025-05-20 17:42:11.301 +03:00 [WRN] Sipariş için email bilgisi bulunamadı. OrderNumber: 21086
2025-05-20 17:42:11.302 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Uyarısı - Sipariş için email bilgisi bulunamadı. OrderNumber: 21086 Email bilgisi olmadan işleme devam ediliyor. 
2025-05-20 17:42:11.304 +03:00 [INF] Cari hesap aranıyor: Email=, Phone=null
2025-05-20 17:42:11.305 +03:00 [WRN] Cari hesap araması için email veya telefon bilgisi bulunamadı
2025-05-20 17:42:11.306 +03:00 [INF] Cari hesap bulunamadı, yeni oluşturuluyor...
2025-05-20 17:42:11.307 +03:00 [INF] CreateCustomerWithRestApi başladı. OrderId=0
2025-05-20 17:42:11.308 +03:00 [INF] Cari hesap oluşturma isteği JSON (ilk 500 karakter): {"CariTemelBilgi":{"Sube_Kodu":1,"ISLETME_KODU":1,"CARI_KOD":"M21086","CARI_TEL":"","CARI_IL":"","ULKE_KODU":"TR","CARI_ISIM":"Web Sipari\u015Fi 21086","CARI_TIP":"A","CARI_ADRES":" ","CARI_ILCE":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","POSTAKODU":"","CM_RAP_TARIH":"2025-05-20T17:42:11.3087839+03:00","HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","UPDATE_KODU":"X","EMAIL":"","GSM1":"","KayitYapanKul":"WEB","KayitTarihi":"2025-05-20T17:42:11.3087845+03:00","OnayTipi":"A","MUSTERIBAZIKDV":"H"},"CariEkBil
2025-05-20 17:42:11.310 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:11.311 +03:00 [INF] Checking current token...
2025-05-20 17:42:11.312 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:11.314 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:11.315 +03:00 [INF] Token parameters:
2025-05-20 17:42:11.316 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:11.317 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:11.318 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:11.319 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:11.320 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:11.321 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:11.323 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:11.324 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:11.325 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:15.413 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:15.420 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:15.422 +03:00 [ERR] Cari hesap oluşturma sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427
2025-05-20 17:42:15.430 +03:00 [WRN] BİLDİRİM: Cari Hesap Oluşturma Hatası - Sipariş No: 21086 Hata: No connection could be made because the target machine actively refused it. (localhost:7070) Stack Trace:    at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427 
2025-05-20 17:42:15.432 +03:00 [ERR] Cari hesap oluşturulamadı. OrderNumber: 21086
2025-05-20 17:42:15.434 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Hatası - Cari hesap oluşturulamadı. OrderNumber: 21086 
2025-05-20 17:42:15.436 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH ERROR ============
2025-05-20 17:42:15.437 +03:00 [ERR] CreateNetsisOrderAsync hata: Cari hesap oluşturulamadı
System.Exception: Cari hesap oluşturulamadı
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719
2025-05-20 17:42:15.439 +03:00 [WRN] BİLDİRİM: Sipariş Aktarım Hatası - Sipariş No: 21086 Hata: Cari hesap oluşturulamadı Stack Trace:    at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719 
2025-05-20 17:42:15.441 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH EXCEPTION ============
2025-05-20 17:42:15.442 +03:00 [ERR] Sipariş ERP'ye gönderilemedi: 21086
2025-05-20 17:42:15.443 +03:00 [INF] Sipariş ERP'ye gönderiliyor: 21088
2025-05-20 17:42:15.444 +03:00 [INF] ============ CreateNetsisOrderAsync STARTED ============
2025-05-20 17:42:15.446 +03:00 [INF] CreateNetsisOrderAsync başladı. XmainOrderId=0, OrderNumber=21088
2025-05-20 17:42:15.447 +03:00 [INF] Sipariş zaten var mı kontrol ediliyor: 21088
2025-05-20 17:42:15.448 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:15.449 +03:00 [INF] Checking current token...
2025-05-20 17:42:15.450 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:15.452 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:15.453 +03:00 [INF] Token parameters:
2025-05-20 17:42:15.455 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:15.458 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:15.459 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:15.461 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:15.462 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:15.463 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:15.465 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:15.466 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:15.468 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:19.543 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:19.552 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:19.554 +03:00 [ERR] Sipariş varlığı kontrolü sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.IsOrderAlreadyExistsAsync(String orderNumber) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 215
2025-05-20 17:42:19.566 +03:00 [INF] Sipariş için email bilgisi alınıyor. OrderNumber=21088
2025-05-20 17:42:19.567 +03:00 [WRN] Sipariş için email bilgisi bulunamadı. OrderNumber: 21088
2025-05-20 17:42:19.569 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Uyarısı - Sipariş için email bilgisi bulunamadı. OrderNumber: 21088 Email bilgisi olmadan işleme devam ediliyor. 
2025-05-20 17:42:19.571 +03:00 [INF] Cari hesap aranıyor: Email=, Phone=null
2025-05-20 17:42:19.573 +03:00 [WRN] Cari hesap araması için email veya telefon bilgisi bulunamadı
2025-05-20 17:42:19.574 +03:00 [INF] Cari hesap bulunamadı, yeni oluşturuluyor...
2025-05-20 17:42:19.576 +03:00 [INF] CreateCustomerWithRestApi başladı. OrderId=0
2025-05-20 17:42:19.577 +03:00 [INF] Cari hesap oluşturma isteği JSON (ilk 500 karakter): {"CariTemelBilgi":{"Sube_Kodu":1,"ISLETME_KODU":1,"CARI_KOD":"M21088","CARI_TEL":"","CARI_IL":"","ULKE_KODU":"TR","CARI_ISIM":"Web Sipari\u015Fi 21088","CARI_TIP":"A","CARI_ADRES":" ","CARI_ILCE":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","POSTAKODU":"","CM_RAP_TARIH":"2025-05-20T17:42:19.5777552+03:00","HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","UPDATE_KODU":"X","EMAIL":"","GSM1":"","KayitYapanKul":"WEB","KayitTarihi":"2025-05-20T17:42:19.5777561+03:00","OnayTipi":"A","MUSTERIBAZIKDV":"H"},"CariEkBil
2025-05-20 17:42:19.579 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:19.580 +03:00 [INF] Checking current token...
2025-05-20 17:42:19.582 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:19.584 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:19.586 +03:00 [INF] Token parameters:
2025-05-20 17:42:19.587 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:19.588 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:19.590 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:19.591 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:19.593 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:19.594 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:19.596 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:19.597 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:19.598 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:22.513 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2025-05-20 17:42:22.573 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2025-05-20 17:42:22.575 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2025-05-20 17:42:22.937 +03:00 [INF] Toplam ürün sayısı: 687
2025-05-20 17:42:22.980 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2025-05-20 17:42:23.670 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:23.679 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:23.682 +03:00 [ERR] Cari hesap oluşturma sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427
2025-05-20 17:42:23.693 +03:00 [WRN] BİLDİRİM: Cari Hesap Oluşturma Hatası - Sipariş No: 21088 Hata: No connection could be made because the target machine actively refused it. (localhost:7070) Stack Trace:    at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427 
2025-05-20 17:42:23.697 +03:00 [ERR] Cari hesap oluşturulamadı. OrderNumber: 21088
2025-05-20 17:42:23.698 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Hatası - Cari hesap oluşturulamadı. OrderNumber: 21088 
2025-05-20 17:42:23.700 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH ERROR ============
2025-05-20 17:42:23.702 +03:00 [ERR] CreateNetsisOrderAsync hata: Cari hesap oluşturulamadı
System.Exception: Cari hesap oluşturulamadı
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719
2025-05-20 17:42:23.705 +03:00 [WRN] BİLDİRİM: Sipariş Aktarım Hatası - Sipariş No: 21088 Hata: Cari hesap oluşturulamadı Stack Trace:    at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719 
2025-05-20 17:42:23.707 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH EXCEPTION ============
2025-05-20 17:42:23.708 +03:00 [ERR] Sipariş ERP'ye gönderilemedi: 21088
2025-05-20 17:42:23.710 +03:00 [INF] Sipariş ERP'ye gönderiliyor: 21089
2025-05-20 17:42:23.711 +03:00 [INF] ============ CreateNetsisOrderAsync STARTED ============
2025-05-20 17:42:23.714 +03:00 [INF] CreateNetsisOrderAsync başladı. XmainOrderId=0, OrderNumber=21089
2025-05-20 17:42:23.716 +03:00 [INF] Sipariş zaten var mı kontrol ediliyor: 21089
2025-05-20 17:42:23.718 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:23.719 +03:00 [INF] Checking current token...
2025-05-20 17:42:23.720 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:23.722 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:23.724 +03:00 [INF] Token parameters:
2025-05-20 17:42:23.725 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:23.727 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:23.729 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:23.730 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:23.732 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:23.733 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:23.735 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:23.737 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:23.739 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:27.826 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:27.834 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:27.836 +03:00 [ERR] Sipariş varlığı kontrolü sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.IsOrderAlreadyExistsAsync(String orderNumber) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 215
2025-05-20 17:42:27.846 +03:00 [INF] Sipariş için email bilgisi alınıyor. OrderNumber=21089
2025-05-20 17:42:27.847 +03:00 [WRN] Sipariş için email bilgisi bulunamadı. OrderNumber: 21089
2025-05-20 17:42:27.849 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Uyarısı - Sipariş için email bilgisi bulunamadı. OrderNumber: 21089 Email bilgisi olmadan işleme devam ediliyor. 
2025-05-20 17:42:27.851 +03:00 [INF] Cari hesap aranıyor: Email=, Phone=null
2025-05-20 17:42:27.854 +03:00 [WRN] Cari hesap araması için email veya telefon bilgisi bulunamadı
2025-05-20 17:42:27.855 +03:00 [INF] Cari hesap bulunamadı, yeni oluşturuluyor...
2025-05-20 17:42:27.856 +03:00 [INF] CreateCustomerWithRestApi başladı. OrderId=0
2025-05-20 17:42:27.858 +03:00 [INF] Cari hesap oluşturma isteği JSON (ilk 500 karakter): {"CariTemelBilgi":{"Sube_Kodu":1,"ISLETME_KODU":1,"CARI_KOD":"M21089","CARI_TEL":"","CARI_IL":"","ULKE_KODU":"TR","CARI_ISIM":"Web Sipari\u015Fi 21089","CARI_TIP":"A","CARI_ADRES":" ","CARI_ILCE":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","POSTAKODU":"","CM_RAP_TARIH":"2025-05-20T17:42:27.8584188+03:00","HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","UPDATE_KODU":"X","EMAIL":"","GSM1":"","KayitYapanKul":"WEB","KayitTarihi":"2025-05-20T17:42:27.8584202+03:00","OnayTipi":"A","MUSTERIBAZIKDV":"H"},"CariEkBil
2025-05-20 17:42:27.861 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:27.862 +03:00 [INF] Checking current token...
2025-05-20 17:42:27.864 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:27.866 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:27.868 +03:00 [INF] Token parameters:
2025-05-20 17:42:27.870 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:27.871 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:27.873 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:27.874 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:27.876 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:27.877 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:27.879 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:27.880 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:27.882 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:31.958 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:31.967 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:31.969 +03:00 [ERR] Cari hesap oluşturma sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427
2025-05-20 17:42:31.978 +03:00 [WRN] BİLDİRİM: Cari Hesap Oluşturma Hatası - Sipariş No: 21089 Hata: No connection could be made because the target machine actively refused it. (localhost:7070) Stack Trace:    at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427 
2025-05-20 17:42:31.980 +03:00 [ERR] Cari hesap oluşturulamadı. OrderNumber: 21089
2025-05-20 17:42:31.982 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Hatası - Cari hesap oluşturulamadı. OrderNumber: 21089 
2025-05-20 17:42:31.983 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH ERROR ============
2025-05-20 17:42:31.985 +03:00 [ERR] CreateNetsisOrderAsync hata: Cari hesap oluşturulamadı
System.Exception: Cari hesap oluşturulamadı
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719
2025-05-20 17:42:31.987 +03:00 [WRN] BİLDİRİM: Sipariş Aktarım Hatası - Sipariş No: 21089 Hata: Cari hesap oluşturulamadı Stack Trace:    at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719 
2025-05-20 17:42:31.989 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH EXCEPTION ============
2025-05-20 17:42:31.990 +03:00 [ERR] Sipariş ERP'ye gönderilemedi: 21089
2025-05-20 17:42:31.991 +03:00 [INF] Sipariş ERP'ye gönderiliyor: 21090
2025-05-20 17:42:31.992 +03:00 [INF] ============ CreateNetsisOrderAsync STARTED ============
2025-05-20 17:42:31.993 +03:00 [INF] CreateNetsisOrderAsync başladı. XmainOrderId=0, OrderNumber=21090
2025-05-20 17:42:31.995 +03:00 [INF] Sipariş zaten var mı kontrol ediliyor: 21090
2025-05-20 17:42:31.996 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:31.997 +03:00 [INF] Checking current token...
2025-05-20 17:42:31.999 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:32.000 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:32.001 +03:00 [INF] Token parameters:
2025-05-20 17:42:32.003 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:32.004 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:32.005 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:32.007 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:32.008 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:32.009 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:32.010 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:32.012 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:32.013 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:36.114 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:36.122 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:36.123 +03:00 [ERR] Sipariş varlığı kontrolü sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.IsOrderAlreadyExistsAsync(String orderNumber) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 215
2025-05-20 17:42:36.131 +03:00 [INF] Sipariş için email bilgisi alınıyor. OrderNumber=21090
2025-05-20 17:42:36.132 +03:00 [WRN] Sipariş için email bilgisi bulunamadı. OrderNumber: 21090
2025-05-20 17:42:36.134 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Uyarısı - Sipariş için email bilgisi bulunamadı. OrderNumber: 21090 Email bilgisi olmadan işleme devam ediliyor. 
2025-05-20 17:42:36.135 +03:00 [INF] Cari hesap aranıyor: Email=, Phone=null
2025-05-20 17:42:36.137 +03:00 [WRN] Cari hesap araması için email veya telefon bilgisi bulunamadı
2025-05-20 17:42:36.138 +03:00 [INF] Cari hesap bulunamadı, yeni oluşturuluyor...
2025-05-20 17:42:36.139 +03:00 [INF] CreateCustomerWithRestApi başladı. OrderId=0
2025-05-20 17:42:36.140 +03:00 [INF] Cari hesap oluşturma isteği JSON (ilk 500 karakter): {"CariTemelBilgi":{"Sube_Kodu":1,"ISLETME_KODU":1,"CARI_KOD":"M21090","CARI_TEL":"","CARI_IL":"","ULKE_KODU":"TR","CARI_ISIM":"Web Sipari\u015Fi 21090","CARI_TIP":"A","CARI_ADRES":" ","CARI_ILCE":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","POSTAKODU":"","CM_RAP_TARIH":"2025-05-20T17:42:36.1403537+03:00","HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","UPDATE_KODU":"X","EMAIL":"","GSM1":"","KayitYapanKul":"WEB","KayitTarihi":"2025-05-20T17:42:36.1403541+03:00","OnayTipi":"A","MUSTERIBAZIKDV":"H"},"CariEkBil
2025-05-20 17:42:36.141 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:36.143 +03:00 [INF] Checking current token...
2025-05-20 17:42:36.144 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:36.145 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:36.146 +03:00 [INF] Token parameters:
2025-05-20 17:42:36.147 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:36.148 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:36.150 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:36.151 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:36.152 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:36.153 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:36.155 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:36.156 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:36.157 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:42:40.236 +03:00 [ERR] GetTokenAsync exception
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
2025-05-20 17:42:40.244 +03:00 [INF] ===== GetTokenAsync FINISHED ERROR =====
2025-05-20 17:42:40.245 +03:00 [ERR] Cari hesap oluşturma sırasında hata: No connection could be made because the target machine actively refused it. (localhost:7070)
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427
2025-05-20 17:42:40.253 +03:00 [WRN] BİLDİRİM: Cari Hesap Oluşturma Hatası - Sipariş No: 21090 Hata: No connection could be made because the target machine actively refused it. (localhost:7070) Stack Trace:    at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 110
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedHttpClient() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 153
   at XECOM_Main_LocalServer.Services.ERPService.CreateCustomerWithRestApiAsync(XmainOrder order, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 427 
2025-05-20 17:42:40.255 +03:00 [ERR] Cari hesap oluşturulamadı. OrderNumber: 21090
2025-05-20 17:42:40.256 +03:00 [WRN] BİLDİRİM: Sipariş İşleme Hatası - Cari hesap oluşturulamadı. OrderNumber: 21090 
2025-05-20 17:42:40.257 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH ERROR ============
2025-05-20 17:42:40.258 +03:00 [ERR] CreateNetsisOrderAsync hata: Cari hesap oluşturulamadı
System.Exception: Cari hesap oluşturulamadı
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719
2025-05-20 17:42:40.260 +03:00 [WRN] BİLDİRİM: Sipariş Aktarım Hatası - Sipariş No: 21090 Hata: Cari hesap oluşturulamadı Stack Trace:    at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder localOrder) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 719 
2025-05-20 17:42:40.261 +03:00 [INF] ============ CreateNetsisOrderAsync FINISHED WITH EXCEPTION ============
2025-05-20 17:42:40.262 +03:00 [ERR] Sipariş ERP'ye gönderilemedi: 21090
2025-05-20 17:42:40.263 +03:00 [INF] Sipariş ERP'ye gönderiliyor: 21091
2025-05-20 17:42:40.264 +03:00 [INF] ============ CreateNetsisOrderAsync STARTED ============
2025-05-20 17:42:40.266 +03:00 [INF] CreateNetsisOrderAsync başladı. XmainOrderId=0, OrderNumber=21091
2025-05-20 17:42:40.268 +03:00 [INF] Sipariş zaten var mı kontrol ediliyor: 21091
2025-05-20 17:42:40.269 +03:00 [INF] ===== GetTokenAsync STARTED =====
2025-05-20 17:42:40.270 +03:00 [INF] Checking current token...
2025-05-20 17:42:40.271 +03:00 [INF] Current token: false, Expires: "0001-01-01T00:00:00.0000000"
2025-05-20 17:42:40.272 +03:00 [INF] Creating new HTTP client...
2025-05-20 17:42:40.273 +03:00 [INF] Token parameters:
2025-05-20 17:42:40.274 +03:00 [INF]   ApiBaseUrl: http://localhost:7070/
2025-05-20 17:42:40.275 +03:00 [INF]   Username: NETSIS
2025-05-20 17:42:40.276 +03:00 [INF]   DbName: AKAL2024-04
2025-05-20 17:42:40.278 +03:00 [INF]   DbUser: ahmet
2025-05-20 17:42:40.279 +03:00 [INF]   DbType: MSSQL
2025-05-20 17:42:40.281 +03:00 [INF]   BranchCode: 203
2025-05-20 17:42:40.282 +03:00 [INF]   Password Length: 8
2025-05-20 17:42:40.283 +03:00 [INF]   DbPassword Length: 9
2025-05-20 17:42:40.284 +03:00 [INF] Sending POST request to: api/v2/token
2025-05-20 17:43:07.594 +03:00 [INF] Netsis API yapılandırması: URL=http://localhost:7070/, Username=NETSIS, DbName=AKAL2024-04
2025-05-20 17:43:07.639 +03:00 [INF] Netsis API yapılandırması başarıyla yüklendi.
2025-05-20 17:43:07.640 +03:00 [INF] Uygulama başlatılıyor
2025-05-20 17:43:07.654 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2025-05-20 17:43:08.923 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 17:43:09.037 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2025-05-20 17:43:09.038 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2025-05-20 17:43:09.815 +03:00 [INF] Toplam ürün sayısı: 687
2025-05-20 17:43:09.901 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2025-05-20 17:43:20.059 +03:00 [INF] ============ ReceiveOrders STARTED ============
2025-05-20 17:43:20.061 +03:00 [INF] ReceiveOrders is called with encrypted data length: 4736
2025-05-20 17:43:20.065 +03:00 [INF] Decryption and cleaning successful, length: 3536, first 100 chars: [{"Id":1142,"OrderNumber":"21085","OrderedAt":"2025-05-19T16:42:16.295","Deleted":false,"LineItems":
2025-05-20 21:32:58.810 +03:00 [INF] Kestrel listening on port: 5280
2025-05-20 21:32:59.076 +03:00 [WRN] CORS AllowedOrigins not configured in appsettings.json. SignalR and API calls from browsers might fail.
2025-05-20 21:32:59.360 +03:00 [INF] Development environment: Swagger and DeveloperExceptionPage enabled.
2025-05-20 21:33:00.025 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-20 21:33:00.065 +03:00 [INF] Checking Netsis API configuration from appsettings.json...
2025-05-20 21:33:00.068 +03:00 [INF] Netsis API configuration loaded successfully. ApiBaseUrl: http://localhost:7070/
2025-05-20 21:33:00.070 +03:00 [INF] Starting XECOM Main LocalServer application...
2025-05-20 21:33:00.290 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 21:34:17.244 +03:00 [INF] Kestrel listening on port: 5280
2025-05-20 21:34:17.443 +03:00 [WRN] CORS AllowedOrigins not configured in appsettings.json. SignalR and API calls from browsers might fail.
2025-05-20 21:34:17.625 +03:00 [INF] Development environment: Swagger and DeveloperExceptionPage enabled.
2025-05-20 21:34:17.855 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-20 21:34:17.857 +03:00 [INF] Checking Netsis API configuration from appsettings.json...
2025-05-20 21:34:17.859 +03:00 [INF] Netsis API configuration loaded successfully. ApiBaseUrl: http://localhost:7070/
2025-05-20 21:34:17.861 +03:00 [INF] Starting XECOM Main LocalServer application...
2025-05-20 21:34:17.987 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
