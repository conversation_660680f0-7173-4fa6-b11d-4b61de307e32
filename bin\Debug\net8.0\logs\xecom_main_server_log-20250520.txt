2025-05-20 21:32:58.810 +03:00 [INF] <PERSON><PERSON><PERSON> listening on port: 5280
2025-05-20 21:32:59.076 +03:00 [WRN] CORS AllowedOrigins not configured in appsettings.json. SignalR and API calls from browsers might fail.
2025-05-20 21:32:59.360 +03:00 [INF] Development environment: Swagger and DeveloperExceptionPage enabled.
2025-05-20 21:33:00.025 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-20 21:33:00.065 +03:00 [INF] Checking Netsis API configuration from appsettings.json...
2025-05-20 21:33:00.068 +03:00 [INF] Netsis API configuration loaded successfully. ApiBaseUrl: http://localhost:7070/
2025-05-20 21:33:00.070 +03:00 [INF] Starting XECOM Main LocalServer application...
2025-05-20 21:33:00.290 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 21:34:17.244 +03:00 [INF] Ke<PERSON><PERSON> listening on port: 5280
2025-05-20 21:34:17.443 +03:00 [WRN] CORS AllowedOrigins not configured in appsettings.json. SignalR and API calls from browsers might fail.
2025-05-20 21:34:17.625 +03:00 [INF] Development environment: Swagger and DeveloperExceptionPage enabled.
2025-05-20 21:34:17.855 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-20 21:34:17.857 +03:00 [INF] Checking Netsis API configuration from appsettings.json...
2025-05-20 21:34:17.859 +03:00 [INF] Netsis API configuration loaded successfully. ApiBaseUrl: http://localhost:7070/
2025-05-20 21:34:17.861 +03:00 [INF] Starting XECOM Main LocalServer application...
2025-05-20 21:34:17.987 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 22:45:44.767 +03:00 [INF] Kestrel listening on port: 5279
2025-05-20 22:45:44.925 +03:00 [INF] CORS policy 'AllowSpecificOrigins' configured for: http://localhost
2025-05-20 22:45:45.042 +03:00 [INF] Development environment: Swagger and DeveloperExceptionPage enabled.
2025-05-20 22:45:45.243 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-20 22:45:45.247 +03:00 [INF] Checking Netsis API configuration from appsettings.json...
2025-05-20 22:45:45.249 +03:00 [INF] Netsis API configuration loaded successfully. ApiBaseUrl: http://localhost:7070/
2025-05-20 22:45:45.251 +03:00 [INF] Starting XECOM Main LocalServer application...
2025-05-20 22:45:45.405 +03:00 [WRN] Overriding address(es) 'http://localhost:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 22:46:22.416 +03:00 [INF] Kestrel listening on port: 5279
2025-05-20 22:46:22.605 +03:00 [INF] CORS policy 'AllowSpecificOrigins' configured for: http://localhost
2025-05-20 22:46:22.736 +03:00 [INF] Development environment: Swagger and DeveloperExceptionPage enabled.
2025-05-20 22:46:22.930 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-20 22:46:22.932 +03:00 [INF] Checking Netsis API configuration from appsettings.json...
2025-05-20 22:46:22.933 +03:00 [INF] Netsis API configuration loaded successfully. ApiBaseUrl: http://localhost:7070/
2025-05-20 22:46:22.934 +03:00 [INF] Starting XECOM Main LocalServer application...
2025-05-20 22:46:23.058 +03:00 [WRN] Overriding address(es) 'http://localhost:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 22:46:58.373 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-05-20 22:46:58.377 +03:00 [INF] Received encrypted data length: 16728
2025-05-20 22:46:58.384 +03:00 [INF] Decryption successful. JSON length: 12531. First 100 chars: ﻿[
  {
    "Id": 1154,
    "OrderNumber": "21097",
    "OrderedAt": "2025-05-20T13:42:45.202",

2025-05-20 22:46:58.484 +03:00 [ERR] JSON deserialization to ClientDTO error. Decrypted JSON (first 200 chars): ﻿[
  {
    "Id": 1154,
    "OrderNumber": "21097",
    "OrderedAt": "2025-05-20T13:42:45.202",
    "Deleted": false,
    "TotalPrice": 590.99,
    "TotalFinalPrice": 590.99,
    "BillingAddres
System.Text.Json.JsonException: '0xEF' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
 ---> System.Text.Json.JsonReaderException: '0xEF' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 74
2025-05-20 22:47:58.649 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-05-20 22:47:58.650 +03:00 [INF] Received encrypted data length: 16728
2025-05-20 22:47:58.652 +03:00 [INF] Decryption successful. JSON length: 12531. First 100 chars: ﻿[
  {
    "Id": 1154,
    "OrderNumber": "21097",
    "OrderedAt": "2025-05-20T13:42:45.202",

2025-05-20 22:47:58.681 +03:00 [ERR] JSON deserialization to ClientDTO error. Decrypted JSON (first 200 chars): ﻿[
  {
    "Id": 1154,
    "OrderNumber": "21097",
    "OrderedAt": "2025-05-20T13:42:45.202",
    "Deleted": false,
    "TotalPrice": 590.99,
    "TotalFinalPrice": 590.99,
    "BillingAddres
System.Text.Json.JsonException: '0xEF' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
 ---> System.Text.Json.JsonReaderException: '0xEF' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 74
2025-05-20 22:48:58.735 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-05-20 22:48:58.737 +03:00 [INF] Received encrypted data length: 16728
2025-05-20 22:48:58.738 +03:00 [INF] Decryption successful. JSON length: 12531. First 100 chars: ﻿[
  {
    "Id": 1154,
    "OrderNumber": "21097",
    "OrderedAt": "2025-05-20T13:42:45.202",

2025-05-20 22:48:58.767 +03:00 [ERR] JSON deserialization to ClientDTO error. Decrypted JSON (first 200 chars): ﻿[
  {
    "Id": 1154,
    "OrderNumber": "21097",
    "OrderedAt": "2025-05-20T13:42:45.202",
    "Deleted": false,
    "TotalPrice": 590.99,
    "TotalFinalPrice": 590.99,
    "BillingAddres
System.Text.Json.JsonException: '0xEF' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
 ---> System.Text.Json.JsonReaderException: '0xEF' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 74
2025-05-20 22:58:52.718 +03:00 [INF] Kestrel listening on port: 5279
2025-05-20 22:58:52.910 +03:00 [INF] CORS policy 'AllowSpecificOrigins' configured for: http://localhost
2025-05-20 22:58:53.006 +03:00 [INF] Development environment: Swagger and DeveloperExceptionPage enabled.
2025-05-20 22:58:53.167 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-20 22:58:53.169 +03:00 [INF] Checking Netsis API configuration from appsettings.json...
2025-05-20 22:58:53.170 +03:00 [INF] Netsis API configuration loaded successfully. ApiBaseUrl: http://localhost:7070/
2025-05-20 22:58:53.171 +03:00 [INF] Starting XECOM Main LocalServer application...
2025-05-20 22:58:53.275 +03:00 [WRN] Overriding address(es) 'http://localhost:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 22:59:22.384 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-05-20 22:59:22.387 +03:00 [INF] Received encrypted data length: 16728
2025-05-20 22:59:22.393 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-05-20 22:59:22.395 +03:00 [INF] Decryption successful. JSON length: 12530. First 100 chars: [
  {
    "Id": 1154,
    "OrderNumber": "21097",
    "OrderedAt": "2025-05-20T13:42:45.202",
 
2025-05-20 22:59:22.431 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 10
2025-05-20 22:59:22.438 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 10
2025-05-20 22:59:22.439 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 10 ORDERS ============
2025-05-20 22:59:22.444 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 22:59:22.446 +03:00 [INF] Processing OrderNumber: 21097 (Client ID: 1154) with ERPService.
2025-05-20 22:59:22.451 +03:00 [INF] Processing order 21097 for Netsis integration.
2025-05-20 22:59:22.454 +03:00 [INF] Searching for Cari with VKN_TCKN: 11111111111
2025-05-20 22:59:22.459 +03:00 [INF] Requesting new Netsis token...
2025-05-20 22:59:26.866 +03:00 [ERR] Error processing OrderNumber 21097 (Client ID: 1154) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.FindCariByTaxOrIdentityAsync(String taxNumber, String identityNumber, String vknTcknFieldForQuery) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 154
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 286
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 22:59:26.879 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 22:59:26.881 +03:00 [INF] Processing OrderNumber: 21096 (Client ID: 1153) with ERPService.
2025-05-20 22:59:26.882 +03:00 [INF] Processing order 21096 for Netsis integration.
2025-05-20 22:59:26.883 +03:00 [INF] Searching for Cari with VKN_TCKN: 11111111111
2025-05-20 22:59:26.885 +03:00 [INF] Requesting new Netsis token...
2025-05-20 22:59:31.208 +03:00 [ERR] Error processing OrderNumber 21096 (Client ID: 1153) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.FindCariByTaxOrIdentityAsync(String taxNumber, String identityNumber, String vknTcknFieldForQuery) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 154
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 286
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 22:59:31.220 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 22:59:31.222 +03:00 [INF] Processing OrderNumber: 21095 (Client ID: 1152) with ERPService.
2025-05-20 22:59:31.224 +03:00 [INF] Processing order 21095 for Netsis integration.
2025-05-20 22:59:31.225 +03:00 [WRN] TaxNumber and IdentityNumber are both empty for cari search.
2025-05-20 22:59:31.227 +03:00 [INF] Cari not found for order 21095. Attempting to create new cari.
2025-05-20 22:59:31.230 +03:00 [INF] Generated new Cari Kod: 120009977000
2025-05-20 22:59:31.232 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120009977000, Unvan: Ali Göbekoğulları
2025-05-20 22:59:31.234 +03:00 [INF] Requesting new Netsis token...
2025-05-20 22:59:35.561 +03:00 [ERR] Error processing OrderNumber 21095 (Client ID: 1152) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisCariAsync(XmainAddress address, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 214
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 293
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 22:59:35.571 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 22:59:35.572 +03:00 [INF] Processing OrderNumber: 21094 (Client ID: 1151) with ERPService.
2025-05-20 22:59:35.575 +03:00 [INF] Processing order 21094 for Netsis integration.
2025-05-20 22:59:35.576 +03:00 [WRN] TaxNumber and IdentityNumber are both empty for cari search.
2025-05-20 22:59:35.578 +03:00 [INF] Cari not found for order 21094. Attempting to create new cari.
2025-05-20 22:59:35.579 +03:00 [INF] Generated new Cari Kod: 120009977001
2025-05-20 22:59:35.580 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120009977001, Unvan: lütfiye  yavuz
2025-05-20 22:59:35.582 +03:00 [INF] Requesting new Netsis token...
2025-05-20 22:59:39.938 +03:00 [ERR] Error processing OrderNumber 21094 (Client ID: 1151) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisCariAsync(XmainAddress address, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 214
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 293
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 22:59:39.952 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 22:59:39.954 +03:00 [INF] Processing OrderNumber: 21093 (Client ID: 1150) with ERPService.
2025-05-20 22:59:39.956 +03:00 [INF] Processing order 21093 for Netsis integration.
2025-05-20 22:59:39.958 +03:00 [WRN] TaxNumber and IdentityNumber are both empty for cari search.
2025-05-20 22:59:39.960 +03:00 [INF] Cari not found for order 21093. Attempting to create new cari.
2025-05-20 22:59:39.961 +03:00 [INF] Generated new Cari Kod: 120009977002
2025-05-20 22:59:39.963 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120009977002, Unvan: Melihşah unç
2025-05-20 22:59:39.965 +03:00 [INF] Requesting new Netsis token...
2025-05-20 22:59:44.299 +03:00 [ERR] Error processing OrderNumber 21093 (Client ID: 1150) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisCariAsync(XmainAddress address, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 214
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 293
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 22:59:44.308 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 22:59:44.309 +03:00 [INF] Processing OrderNumber: 21092 (Client ID: 1149) with ERPService.
2025-05-20 22:59:44.311 +03:00 [INF] Processing order 21092 for Netsis integration.
2025-05-20 22:59:44.313 +03:00 [WRN] TaxNumber and IdentityNumber are both empty for cari search.
2025-05-20 22:59:44.314 +03:00 [INF] Cari not found for order 21092. Attempting to create new cari.
2025-05-20 22:59:44.316 +03:00 [INF] Generated new Cari Kod: 120009977003
2025-05-20 22:59:44.317 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120009977003, Unvan: Aysun  PULAT
2025-05-20 22:59:44.319 +03:00 [INF] Requesting new Netsis token...
2025-05-20 22:59:48.636 +03:00 [ERR] Error processing OrderNumber 21092 (Client ID: 1149) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisCariAsync(XmainAddress address, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 214
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 293
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 22:59:48.647 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 22:59:48.648 +03:00 [INF] Processing OrderNumber: 21091 (Client ID: 1148) with ERPService.
2025-05-20 22:59:48.650 +03:00 [INF] Processing order 21091 for Netsis integration.
2025-05-20 22:59:48.652 +03:00 [INF] Searching for Cari with VKN_TCKN: 11111111111
2025-05-20 22:59:48.654 +03:00 [INF] Requesting new Netsis token...
2025-05-20 22:59:52.964 +03:00 [ERR] Error processing OrderNumber 21091 (Client ID: 1148) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.FindCariByTaxOrIdentityAsync(String taxNumber, String identityNumber, String vknTcknFieldForQuery) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 154
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 286
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 22:59:52.974 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 22:59:52.976 +03:00 [INF] Processing OrderNumber: 21090 (Client ID: 1147) with ERPService.
2025-05-20 22:59:52.978 +03:00 [INF] Processing order 21090 for Netsis integration.
2025-05-20 22:59:52.979 +03:00 [WRN] TaxNumber and IdentityNumber are both empty for cari search.
2025-05-20 22:59:52.980 +03:00 [INF] Cari not found for order 21090. Attempting to create new cari.
2025-05-20 22:59:52.982 +03:00 [INF] Generated new Cari Kod: 120009977004
2025-05-20 22:59:52.983 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120009977004, Unvan: Hilal Ösken
2025-05-20 22:59:52.985 +03:00 [INF] Requesting new Netsis token...
2025-05-20 22:59:57.356 +03:00 [ERR] Error processing OrderNumber 21090 (Client ID: 1147) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisCariAsync(XmainAddress address, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 214
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 293
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 22:59:57.368 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 22:59:57.370 +03:00 [INF] Processing OrderNumber: 21089 (Client ID: 1146) with ERPService.
2025-05-20 22:59:57.376 +03:00 [INF] Processing order 21089 for Netsis integration.
2025-05-20 22:59:57.378 +03:00 [WRN] TaxNumber and IdentityNumber are both empty for cari search.
2025-05-20 22:59:57.379 +03:00 [INF] Cari not found for order 21089. Attempting to create new cari.
2025-05-20 22:59:57.381 +03:00 [INF] Generated new Cari Kod: 120009977005
2025-05-20 22:59:57.382 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120009977005, Unvan: Oylum  Uludağ
2025-05-20 22:59:57.384 +03:00 [INF] Requesting new Netsis token...
2025-05-20 23:00:01.752 +03:00 [ERR] Error processing OrderNumber 21089 (Client ID: 1146) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisCariAsync(XmainAddress address, String email) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 214
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 293
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 23:00:01.766 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-20 23:00:01.768 +03:00 [INF] Processing OrderNumber: 21088 (Client ID: 1145) with ERPService.
2025-05-20 23:00:01.770 +03:00 [INF] Processing order 21088 for Netsis integration.
2025-05-20 23:00:01.772 +03:00 [INF] Searching for Cari with VKN_TCKN: 11111111111
2025-05-20 23:00:01.774 +03:00 [INF] Requesting new Netsis token...
2025-05-20 23:00:06.137 +03:00 [ERR] Error processing OrderNumber 21088 (Client ID: 1145) with ERPService.
System.Net.Http.HttpRequestException: No connection could be made because the target machine actively refused it. (localhost:7070)
 ---> System.Net.Sockets.SocketException (10061): No connection could be made because the target machine actively refused it.
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at System.Net.Sockets.Socket.<ConnectAsync>g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.HttpConnectionWaiter`1.WaitForConnectionWithTelemetryAsync(HttpRequestMessage request, HttpConnectionPool pool, Boolean async, CancellationToken requestCancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler.<SendCoreAsync>g__Core|5_0(HttpRequestMessage request, Boolean useAsync, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.<SendAsync>g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)
   at XECOM_Main_LocalServer.Services.ERPService.GetTokenAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 98
   at XECOM_Main_LocalServer.Services.ERPService.CreateAuthenticatedClientAsync() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 125
   at XECOM_Main_LocalServer.Services.ERPService.FindCariByTaxOrIdentityAsync(String taxNumber, String identityNumber, String vknTcknFieldForQuery) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 154
   at XECOM_Main_LocalServer.Services.ERPService.CreateNetsisOrderAsync(XmainOrder order) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ERPService.cs:line 286
   at XECOM_Main_LocalServer.Services.DataSyncHub.ReceiveOrders(String encryptedData) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncHub.cs:line 155
2025-05-20 23:00:06.149 +03:00 [INF] ERP SYNC (Main Server) FINISHED. Total Client DTOs: 10, Processable Mapped Orders: 10, Success: 0, Failed: 10
2025-05-20 23:00:06.152 +03:00 [INF] ============ ReceiveOrders (Main Server) FINISHED ============
