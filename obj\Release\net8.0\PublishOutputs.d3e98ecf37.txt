D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\XECOM Main-LocalServer.exe
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\appsettings.Development.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\appsettings.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\XECOM Main-LocalServer.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\XECOM Main-LocalServer.runtimeconfig.json
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\XECOM Main-LocalServer.pdb
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Azure.Core.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Azure.Identity.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Bcl.AsyncInterfaces.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Data.SqlClient.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.EntityFrameworkCore.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.EntityFrameworkCore.Relational.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Extensions.Caching.Memory.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Extensions.DependencyInjection.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Extensions.DependencyModel.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Extensions.Logging.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Extensions.Logging.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Extensions.Options.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Identity.Client.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Identity.Client.Extensions.Msal.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.IdentityModel.Abstractions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.IdentityModel.JsonWebTokens.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.IdentityModel.Logging.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.IdentityModel.Protocols.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.IdentityModel.Tokens.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.OpenApi.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.SqlServer.Server.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Microsoft.Win32.SystemEvents.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Newtonsoft.Json.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Serilog.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Serilog.AspNetCore.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Serilog.Extensions.Hosting.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Serilog.Extensions.Logging.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Serilog.Formatting.Compact.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Serilog.Settings.Configuration.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Serilog.Sinks.Console.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Serilog.Sinks.Debug.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Serilog.Sinks.File.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Swashbuckle.AspNetCore.Swagger.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\System.Configuration.ConfigurationManager.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\System.Drawing.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\System.IdentityModel.Tokens.Jwt.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\System.Memory.Data.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\System.Runtime.Caching.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\System.Security.Cryptography.ProtectedData.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\System.Security.Permissions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\System.Windows.Extensions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win\lib\net6.0\System.Drawing.Common.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\bin\Release\net8.0\publish\XECOM Main-LocalServer.deps.json
