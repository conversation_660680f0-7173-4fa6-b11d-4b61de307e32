﻿using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using XECOM_Main_LocalServer.Data;
using XECOM_Main_LocalServer.Models;

namespace XECOM_Main_LocalServer.Services
{
    // Bu servis, veritabanındaki değ<PERSON>şiklikleri izler ve client'lara bildirir
    public class ChangeTrackingService : BackgroundService
    {
        private readonly IServiceProvider _services;
        private readonly ILogger<ChangeTrackingService> _logger;
        private readonly EncryptionService _encryptionService;
        private readonly IConfiguration _configuration;
        private long _lastSyncVersion = 0;

        // Konstruktör: Gerekli servisleri enjekte eder
        public ChangeTrackingService(
            IServiceProvider services,
            ILogger<ChangeTrackingService> logger,
            EncryptionService encryptionService,
            IConfiguration configuration)
        {
            _services = services;
            _logger = logger;
            _encryptionService = encryptionService;
            _configuration = configuration;
        }

        // Arka planda çalışan ana metot
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ExecuteStoredProcedure();

                    // Konfigürasyona göre senkronizasyon yöntemini seç
                    if (_configuration.GetValue<bool>("UseChangeTracking"))
                    {
                        await SyncUsingChangeTracking();
                    }
                    else
                    {
                        await SyncAllData();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Veri senkronizasyonu sırasında hata oluştu");
                }

                // 5 dakika bekle
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }

        // Stored procedure'ü çalıştıran metod
        private async Task ExecuteStoredProcedure()
        {
            using var scope = _services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            // Stored procedure'ü çalıştır
            await dbContext.Database.ExecuteSqlRawAsync(
                "EXEC NSP_NETSESGUNCELLE @p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11",
                2, "NETSIS", 2, 203, "E", "H", 2, -1, "H", 0, 0, "H");

            _logger.LogInformation("Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı");
        }

        // Change Tracking kullanarak veri değişikliklerini senkronize eder
        private async Task SyncUsingChangeTracking()
        {
            using var scope = _services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var hubContext = scope.ServiceProvider.GetRequiredService<IHubContext<DataSyncHub>>();

            // Change Tracking sorgusunu çalıştır
            var changes = await dbContext.ProductChanges
                .FromSqlInterpolated($@"
                    SELECT P.*, 
                           CT.SYS_CHANGE_VERSION, 
                           CT.SYS_CHANGE_OPERATION
                    FROM CHANGETABLE(CHANGES dbo.AlkProductList2, {@_lastSyncVersion}) AS CT
                    JOIN dbo.AlkProductList2 P ON P.ID = CT.ID")
                .AsNoTracking()
                .ToListAsync();

            if (changes.Any())
            {
                // Son senkronizasyon versiyonunu güncelle
                _lastSyncVersion = changes.Max(c => c.SYS_CHANGE_VERSION);

                // Değişiklikleri şifrele
                var encryptedChanges = _encryptionService.EncryptData(changes);

                // Değişiklikleri client'lara gönder
                await hubContext.Clients.All.SendAsync("ReceiveChanges", encryptedChanges);

                _logger.LogInformation("Change Tracking kullanılarak {Count} değişiklik client'lara gönderildi", changes.Count);
            }
        }

        // Tüm veriyi senkronize eder
        private async Task SyncAllData()
        {
            using var scope = _services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var hubContext = scope.ServiceProvider.GetRequiredService<IHubContext<DataSyncHub>>();

            // Tüm ürünleri al
            var products = await dbContext.Products.AsNoTracking().ToListAsync();

            if (products.Any())
            {
                // Verileri şifrele
                var encryptedData = _encryptionService.EncryptData(products);

                // Verileri client'lara gönder
                await hubContext.Clients.All.SendAsync("ReceiveData", encryptedData);

                _logger.LogInformation("Tam veri senkronizasyonu kullanılarak {Count} ürün client'lara gönderildi", products.Count);
            }
        }
    }
}
