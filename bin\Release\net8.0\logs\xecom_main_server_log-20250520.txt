2025-05-20 23:20:16.659 +03:00 [INF] Ke<PERSON><PERSON> listening on port: 5279
2025-05-20 23:20:16.823 +03:00 [INF] CORS policy 'AllowSpecificOrigins' configured for: http://localhost
2025-05-20 23:20:16.899 +03:00 [INF] Development environment: Swagger and DeveloperExceptionPage enabled.
2025-05-20 23:20:17.069 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-20 23:20:17.070 +03:00 [INF] Checking Netsis API configuration from appsettings.json...
2025-05-20 23:20:17.072 +03:00 [INF] Netsis API configuration loaded successfully. ApiBaseUrl: http://localhost:7070/
2025-05-20 23:20:17.073 +03:00 [INF] Starting XECOM Main LocalServer application...
2025-05-20 23:20:17.183 +03:00 [WRN] Overriding address(es) 'http://localhost:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 23:20:43.089 +03:00 [INF] Ke<PERSON><PERSON> listening on port: 5279
2025-05-20 23:20:43.135 +03:00 [INF] CORS policy 'AllowSpecificOrigins' configured for: http://localhost
2025-05-20 23:59:49.779 +03:00 [INF] Application built successfully.
2025-05-20 23:59:49.803 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-05-20 23:59:49.840 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-05-20 23:59:50.049 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-20 23:59:50.051 +03:00 [INF] HTTP request pipeline configuration complete.
2025-05-20 23:59:50.052 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-05-20 23:59:50.053 +03:00 [INF] Netsis API configuration check PASSED. ApiBaseUrl: http://localhost:7070/
2025-05-20 23:59:50.055 +03:00 [INF] Pre-run checks complete.
2025-05-20 23:59:50.056 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-05-20 23:59:50.189 +03:00 [WRN] Overriding address(es) 'http://localhost:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
