﻿// XECOM_Main_LocalServer\Models\DTOsFromClient\OrderClientDto.cs
using System;
using System.Collections.Generic;

namespace XECOM_Main_LocalServer.Models.DTOsFromClient
{
    public class OrderClientDto
    {
        public long Id { get; set; } // İstemcideki Id
        public string OrderNumber { get; set; }
        public DateTime? OrderedAt { get; set; }
        public bool? Deleted { get; set; }
        public decimal? TotalPrice { get; set; }
        public decimal? TotalFinalPrice { get; set; }
        public BillingAddressClientDto BillingAddress { get; set; }
        public List<OrderLineItemClientDto> LineItems { get; set; } = new List<OrderLineItemClientDto>();
        public List<OrderPackageClientDto> Packages { get; set; } = new List<OrderPackageClientDto>();
    }
}