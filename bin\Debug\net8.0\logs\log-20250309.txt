2025-03-09 16:50:49.675 +03:00 [WRN] No store type was specified for the decimal property 'TotalFinalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:50:49.723 +03:00 [WRN] No store type was specified for the decimal property 'TotalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:50:49.725 +03:00 [WRN] No store type was specified for the decimal property 'FinalPrice' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:50:49.727 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:50:49.901 +03:00 [WRN] No store type was specified for the decimal property 'TotalFinalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:50:49.904 +03:00 [WRN] No store type was specified for the decimal property 'TotalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:50:49.908 +03:00 [WRN] No store type was specified for the decimal property 'FinalPrice' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:50:49.912 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:51:25.140 +03:00 [WRN] No store type was specified for the decimal property 'TotalFinalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:51:25.186 +03:00 [WRN] No store type was specified for the decimal property 'TotalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:51:25.191 +03:00 [WRN] No store type was specified for the decimal property 'FinalPrice' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:51:25.195 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:51:25.834 +03:00 [ERR] Failed executing DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [AlkProductList2] (
    [ID] bigint NOT NULL IDENTITY,
    [Category] nvarchar(max) NULL,
    [Assortment_type] nvarchar(max) NULL,
    [Name] nvarchar(max) NULL,
    [Stock_Code] nvarchar(max) NULL,
    [Brand] nvarchar(max) NULL,
    [SKU_ID] nvarchar(max) NULL,
    [Model_ID] nvarchar(max) NULL,
    [Target_Age_Groups] nvarchar(max) NULL,
    [Target_Genders] nvarchar(max) NULL,
    [Season] nvarchar(max) NULL,
    [Description] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing1_Ratio] int NULL,
    [Material_Upper_Material_Clothing1] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing2_Ratio] int NULL,
    [Material_Upper_Material_Clothing2] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing3_Ratio] int NULL,
    [Material_Upper_Material_Clothing3] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing4_Ratio] int NULL,
    [Material_Upper_Material_Clothing4] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing5_Ratio] int NULL,
    [Material_Upper_Material_Clothing5] nvarchar(max) NULL,
    [Media1_URL] nvarchar(max) NULL,
    [Media2_URL] nvarchar(max) NULL,
    [Media3_URL] nvarchar(max) NULL,
    [Media4_URL] nvarchar(max) NULL,
    [Media5_URL] nvarchar(max) NULL,
    [Color_Code_Primary] nvarchar(max) NULL,
    [Supplier_Color] nvarchar(max) NULL,
    [Size] nvarchar(max) NULL,
    [Size1] nvarchar(max) NULL,
    [EAN] nvarchar(max) NULL,
    [Stock_Quantity] decimal(18,8) NULL,
    [Regular_Price] decimal(18,8) NULL,
    [Discount_Price] decimal(18,8) NULL,
    [Currency] nvarchar(max) NULL,
    [stok_last_update] datetime2 NULL,
    CONSTRAINT [PK_AlkProductList2] PRIMARY KEY ([ID])
);
2025-03-09 16:57:24.333 +03:00 [WRN] No store type was specified for the decimal property 'TotalFinalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:57:24.373 +03:00 [WRN] No store type was specified for the decimal property 'TotalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:57:24.374 +03:00 [WRN] No store type was specified for the decimal property 'FinalPrice' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:57:24.378 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 16:57:24.777 +03:00 [ERR] Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [AlkProductList2] (
    [ID] bigint NOT NULL IDENTITY,
    [Category] nvarchar(max) NULL,
    [Assortment_type] nvarchar(max) NULL,
    [Name] nvarchar(max) NULL,
    [Stock_Code] nvarchar(max) NULL,
    [Brand] nvarchar(max) NULL,
    [SKU_ID] nvarchar(max) NULL,
    [Model_ID] nvarchar(max) NULL,
    [Target_Age_Groups] nvarchar(max) NULL,
    [Target_Genders] nvarchar(max) NULL,
    [Season] nvarchar(max) NULL,
    [Description] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing1_Ratio] int NULL,
    [Material_Upper_Material_Clothing1] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing2_Ratio] int NULL,
    [Material_Upper_Material_Clothing2] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing3_Ratio] int NULL,
    [Material_Upper_Material_Clothing3] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing4_Ratio] int NULL,
    [Material_Upper_Material_Clothing4] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing5_Ratio] int NULL,
    [Material_Upper_Material_Clothing5] nvarchar(max) NULL,
    [Media1_URL] nvarchar(max) NULL,
    [Media2_URL] nvarchar(max) NULL,
    [Media3_URL] nvarchar(max) NULL,
    [Media4_URL] nvarchar(max) NULL,
    [Media5_URL] nvarchar(max) NULL,
    [Color_Code_Primary] nvarchar(max) NULL,
    [Supplier_Color] nvarchar(max) NULL,
    [Size] nvarchar(max) NULL,
    [Size1] nvarchar(max) NULL,
    [EAN] nvarchar(max) NULL,
    [Stock_Quantity] decimal(18,8) NULL,
    [Regular_Price] decimal(18,8) NULL,
    [Discount_Price] decimal(18,8) NULL,
    [Currency] nvarchar(max) NULL,
    [stok_last_update] datetime2 NULL,
    CONSTRAINT [PK_AlkProductList2] PRIMARY KEY ([ID])
);
2025-03-09 17:05:28.914 +03:00 [WRN] No store type was specified for the decimal property 'TotalFinalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:05:28.960 +03:00 [WRN] No store type was specified for the decimal property 'TotalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:05:28.962 +03:00 [WRN] No store type was specified for the decimal property 'FinalPrice' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:05:28.964 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:05:29.379 +03:00 [ERR] Failed executing DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [AlkProductList2] (
    [ID] bigint NOT NULL IDENTITY,
    [Category] nvarchar(max) NULL,
    [Assortment_type] nvarchar(max) NULL,
    [Name] nvarchar(max) NULL,
    [Stock_Code] nvarchar(max) NULL,
    [Brand] nvarchar(max) NULL,
    [SKU_ID] nvarchar(max) NULL,
    [Model_ID] nvarchar(max) NULL,
    [Target_Age_Groups] nvarchar(max) NULL,
    [Target_Genders] nvarchar(max) NULL,
    [Season] nvarchar(max) NULL,
    [Description] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing1_Ratio] int NULL,
    [Material_Upper_Material_Clothing1] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing2_Ratio] int NULL,
    [Material_Upper_Material_Clothing2] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing3_Ratio] int NULL,
    [Material_Upper_Material_Clothing3] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing4_Ratio] int NULL,
    [Material_Upper_Material_Clothing4] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing5_Ratio] int NULL,
    [Material_Upper_Material_Clothing5] nvarchar(max) NULL,
    [Media1_URL] nvarchar(max) NULL,
    [Media2_URL] nvarchar(max) NULL,
    [Media3_URL] nvarchar(max) NULL,
    [Media4_URL] nvarchar(max) NULL,
    [Media5_URL] nvarchar(max) NULL,
    [Color_Code_Primary] nvarchar(max) NULL,
    [Supplier_Color] nvarchar(max) NULL,
    [Size] nvarchar(max) NULL,
    [Size1] nvarchar(max) NULL,
    [EAN] nvarchar(max) NULL,
    [Stock_Quantity] decimal(18,8) NULL,
    [Regular_Price] decimal(18,8) NULL,
    [Discount_Price] decimal(18,8) NULL,
    [Currency] nvarchar(max) NULL,
    [stok_last_update] datetime2 NULL,
    CONSTRAINT [PK_AlkProductList2] PRIMARY KEY ([ID])
);
2025-03-09 17:08:40.785 +03:00 [WRN] No store type was specified for the decimal property 'TotalFinalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:08:40.828 +03:00 [WRN] No store type was specified for the decimal property 'TotalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:08:40.829 +03:00 [WRN] No store type was specified for the decimal property 'FinalPrice' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:08:40.831 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:08:41.236 +03:00 [ERR] Failed executing DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [AlkProductList2] (
    [ID] bigint NOT NULL IDENTITY,
    [Category] nvarchar(max) NULL,
    [Assortment_type] nvarchar(max) NULL,
    [Name] nvarchar(max) NULL,
    [Stock_Code] nvarchar(max) NULL,
    [Brand] nvarchar(max) NULL,
    [SKU_ID] nvarchar(max) NULL,
    [Model_ID] nvarchar(max) NULL,
    [Target_Age_Groups] nvarchar(max) NULL,
    [Target_Genders] nvarchar(max) NULL,
    [Season] nvarchar(max) NULL,
    [Description] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing1_Ratio] int NULL,
    [Material_Upper_Material_Clothing1] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing2_Ratio] int NULL,
    [Material_Upper_Material_Clothing2] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing3_Ratio] int NULL,
    [Material_Upper_Material_Clothing3] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing4_Ratio] int NULL,
    [Material_Upper_Material_Clothing4] nvarchar(max) NULL,
    [Material_Upper_Material_Clothing5_Ratio] int NULL,
    [Material_Upper_Material_Clothing5] nvarchar(max) NULL,
    [Media1_URL] nvarchar(max) NULL,
    [Media2_URL] nvarchar(max) NULL,
    [Media3_URL] nvarchar(max) NULL,
    [Media4_URL] nvarchar(max) NULL,
    [Media5_URL] nvarchar(max) NULL,
    [Color_Code_Primary] nvarchar(max) NULL,
    [Supplier_Color] nvarchar(max) NULL,
    [Size] nvarchar(max) NULL,
    [Size1] nvarchar(max) NULL,
    [EAN] nvarchar(max) NULL,
    [Stock_Quantity] decimal(18,8) NULL,
    [Regular_Price] decimal(18,8) NULL,
    [Discount_Price] decimal(18,8) NULL,
    [Currency] nvarchar(max) NULL,
    [stok_last_update] datetime2 NULL,
    CONSTRAINT [PK_AlkProductList2] PRIMARY KEY ([ID])
);
2025-03-09 17:14:04.955 +03:00 [WRN] No store type was specified for the decimal property 'TotalFinalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:04.995 +03:00 [WRN] No store type was specified for the decimal property 'TotalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:04.998 +03:00 [WRN] No store type was specified for the decimal property 'FinalPrice' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:05.000 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:05.149 +03:00 [WRN] No store type was specified for the decimal property 'TotalFinalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:05.152 +03:00 [WRN] No store type was specified for the decimal property 'TotalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:05.155 +03:00 [WRN] No store type was specified for the decimal property 'FinalPrice' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:05.157 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:24.676 +03:00 [WRN] No store type was specified for the decimal property 'TotalFinalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:24.718 +03:00 [WRN] No store type was specified for the decimal property 'TotalPrice' on entity type 'XmainOrder'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:24.721 +03:00 [WRN] No store type was specified for the decimal property 'FinalPrice' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-03-09 17:14:24.722 +03:00 [WRN] No store type was specified for the decimal property 'Price' on entity type 'XmainOrderLineItem'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
