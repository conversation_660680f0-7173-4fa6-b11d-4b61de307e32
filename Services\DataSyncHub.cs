﻿// XECOM_Main_LocalServer\Services\DataSyncHub.cs
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Configuration; // IConfiguration için eklendi
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using XECOM_Main_LocalServer.Models;
using XECOM_Main_LocalServer.Models.DTOsFromClient;

namespace XECOM_Main_LocalServer.Services
{
    public class DataSyncHub : Hub
    {
        private readonly ILogger<DataSyncHub> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration; // EncryptionService yerine IConfiguration enjekte edildi

        public DataSyncHub(
            ILogger<DataSyncHub> logger,
            IServiceProvider serviceProvider,
            IConfiguration configuration) // Değişiklik: IConfiguration
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _configuration = configuration; // IConfiguration atandı
        }

        public async Task ReceiveOrders(string encryptedData)
        {
            _logger.LogInformation("============ ReceiveOrders (Main Server) STARTED ============");
            _logger.LogInformation("Received encrypted data length: {length}", encryptedData?.Length ?? 0);

            if (string.IsNullOrEmpty(encryptedData))
            {
                _logger.LogWarning("ReceiveOrders: encryptedData is null or empty. Aborting.");
                await Clients.Caller.SendAsync("ReceiveOrdersAck", "Error: Encrypted data was empty.");
                return;
            }

            string decryptedJson;
            try
            {
                // Şifre çözme (STATIC EncryptionService ve IConfiguration kullanarak)
                decryptedJson = EncryptionService.Decrypt(encryptedData, _configuration); // DEĞİŞİKLİK BURADA

                if (string.IsNullOrEmpty(decryptedJson))
                {
                    _logger.LogError("Decryption resulted in null or empty JSON string. Aborting.");
                    await Clients.Caller.SendAsync("ReceiveOrdersAck", "Error: Decryption failed or produced empty result.");
                    return;
                }

                // **** YENİ ADIM: BOM Temizleme ****
                if (decryptedJson.StartsWith("\uFEFF")) // UTF-8 BOM karakteri
                {
                    _logger.LogInformation("UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.");
                    decryptedJson = decryptedJson.Substring(1);
                }

                _logger.LogInformation("Decryption successful. JSON length: {length}. First 100 chars: {jsonPreview}",
                    decryptedJson.Length,
                    decryptedJson.Length > 100 ? decryptedJson.Substring(0, 100) : decryptedJson);
            }
            catch (Exception decryptEx) // CryptographicException veya diğerleri EncryptionService'den fırlatılabilir
            {
                _logger.LogError(decryptEx, "Decryption error in ReceiveOrders. Encrypted data preview (first 50 chars): {DataPreview}", encryptedData.Length > 50 ? encryptedData.Substring(0, 50) : encryptedData);
                await Clients.Caller.SendAsync("ReceiveOrdersAck", $"Error: Decryption exception - {decryptEx.Message}");
                return;
            }

            List<OrderClientDto> clientOrdersDto;
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                clientOrdersDto = JsonSerializer.Deserialize<List<OrderClientDto>>(decryptedJson, options);

                if (clientOrdersDto == null || clientOrdersDto.Count == 0)
                {
                    _logger.LogWarning("JSON deserialization resulted in null or empty list of client DTO orders.");
                    await Clients.Caller.SendAsync("ReceiveOrdersAck", "Warning: No orders found in the received data.");
                    return;
                }
                _logger.LogInformation("Deserialization to ClientDTO successful. Number of orders: {count}", clientOrdersDto.Count);
            }
            catch (JsonException jsonEx)
            {
                _logger.LogError(jsonEx, "JSON deserialization to ClientDTO error. Decrypted JSON (first 200 chars): {jsonPreview}",
                    decryptedJson.Length > 200 ? decryptedJson.Substring(0, 200) : decryptedJson);
                await Clients.Caller.SendAsync("ReceiveOrdersAck", $"Error: JSON deserialization exception - {jsonEx.Message}");
                return;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during client DTO order deserialization.");
                await Clients.Caller.SendAsync("ReceiveOrdersAck", $"Error: Unexpected deserialization error - {ex.Message}");
                return;
            }

            List<XmainOrder> ordersToProcess = clientOrdersDto
                                                .Select(dto => dto.ToXmainOrder())
                                                .Where(order => order != null)
                                                .ToList();

            if (!ordersToProcess.Any())
            {
                _logger.LogWarning("No orders to process after mapping from ClientDTO to XmainOrder.");
                await Clients.Caller.SendAsync("ReceiveOrdersAck", "Warning: No processable orders after mapping.");
                return;
            }
            _logger.LogInformation("Mapping to XmainOrder successful. Orders to process: {count}", ordersToProcess.Count);

            _logger.LogInformation("============ ERP SYNC (Main Server) STARTED FOR {count} ORDERS ============", ordersToProcess.Count);
            int successCount = 0;
            int failCount = 0;
            List<string> errorMessages = new List<string>();

            foreach (var order in ordersToProcess)
            {
                if (order.BillingAddress == null)
                {
                    _logger.LogError("OrderNumber {OrderNumber} (Client ID: {ClientId}) is missing BillingAddress after mapping. Skipping.", order.OrderNumber, order.ClientOrderId);
                    failCount++;
                    errorMessages.Add($"Order {order.OrderNumber}: Missing BillingAddress.");
                    continue;
                }
                if (order.OrderLineItems == null || !order.OrderLineItems.Any())
                {
                    _logger.LogError("OrderNumber {OrderNumber} (Client ID: {ClientId}) has no line items after mapping. Skipping.", order.OrderNumber, order.ClientOrderId);
                    failCount++;
                    errorMessages.Add($"Order {order.OrderNumber}: No line items.");
                    continue;
                }
                if (order.OrderLineItems.Any(li => string.IsNullOrWhiteSpace(li.VariantSku)))
                {
                    _logger.LogError("OrderNumber {OrderNumber} (Client ID: {ClientId}) has line items with missing SKU after mapping. Skipping.", order.OrderNumber, order.ClientOrderId);
                    failCount++;
                    errorMessages.Add($"Order {order.OrderNumber}: Line item(s) with missing SKU.");
                    continue;
                }

                try
                {
                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var erpService = scope.ServiceProvider.GetRequiredService<ERPService>();
                        _logger.LogInformation("Processing OrderNumber: {OrderNumber} (Client ID: {ClientId}) with ERPService.", order.OrderNumber, order.ClientOrderId);

                        _logger.LogInformation("🚨 DEBUG: About to call CreateNetsisOrderAsync...");

                        //RestAPI kullan
                        string erpResult = await erpService.CreateNetsisOrderAsync(order);

                        //REST yerine COM kullan
                        //string erpResult = await erpService.CreateNetsisOrderViaCOM(order); 

                        //Netsis order RestSharp
                        //string erpResult = await erpService.CreateNetsisOrderViaRestSharp(order);

                        _logger.LogInformation("🚨 DEBUG: CreateNetsisOrderAsync returned: {Result}", erpResult);

                        if (!string.IsNullOrEmpty(erpResult) && erpResult != "ERROR_CARI" && erpResult != "ERROR_ORDER_CREATE" && erpResult != "EXCEPTION_ORDER_CREATE")
                        {
                            _logger.LogInformation("OrderNumber {OrderNumber} (Client ID: {ClientId}) successfully processed by ERP. Result: {erpResult}", order.OrderNumber, order.ClientOrderId, erpResult);
                            successCount++;
                        }
                        else
                        {
                            _logger.LogError("Failed to process OrderNumber {OrderNumber} (Client ID: {ClientId}) in ERP. Result: {erpResult}", order.OrderNumber, order.ClientOrderId, erpResult);
                            failCount++;
                            errorMessages.Add($"Order {order.OrderNumber}: ERP processing failed - {erpResult}");
                        }
                    }
                }
                catch (Exception erpEx)
                {
                    _logger.LogError(erpEx, "Error processing OrderNumber {OrderNumber} (Client ID: {ClientId}) with ERPService.", order.OrderNumber, order.ClientOrderId);
                    failCount++;
                    errorMessages.Add($"Order {order.OrderNumber}: ERP service exception - {erpEx.Message}");
                }
            }

            _logger.LogInformation("ERP SYNC (Main Server) FINISHED. Total Client DTOs: {clientDtoCount}, Processable Mapped Orders: {mappedCount}, Success: {success}, Failed: {failed}",
                clientOrdersDto.Count, ordersToProcess.Count, successCount, failCount);
            string finalAckMessage = $"Processed: {clientOrdersDto.Count}, Success: {successCount}, Failed: {failCount}.";
            if (errorMessages.Any())
            {
                finalAckMessage += " Errors: " + string.Join("; ", errorMessages.Take(5));
            }
            await Clients.Caller.SendAsync("ReceiveOrdersAck", finalAckMessage);
            _logger.LogInformation("============ ReceiveOrders (Main Server) FINISHED ============");
        }

        public async Task NotifyError(string errorMessage)
        {
            _logger.LogError("Error notification received from client: {errorMessage}", errorMessage);
            await Task.CompletedTask;
        }
    }
}