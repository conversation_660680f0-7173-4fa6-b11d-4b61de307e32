// This file contains rules to color document tabs by regular expressions. Each line contains a regular expression that will be tested against a file's full path. All files matching a regular expression will share a color.
// You can customize the color assigned to any group of files by right-clicking the tab and choosing "Set Tab Color."
// Regular expressions will be matched in the order they appear in this file. See https://docs.microsoft.com/en-us/dotnet/standard/base-types/regular-expressions for syntax.
// Regular expressions are matched as case-insensitive by default. You can override this behavior using capture group options like "(?-i:expression)".
// Edit this file and save your changes to see changes immediately applied. Any errors encountered during parsing or evaluating expressions will appear in the Output Window in a pane named "Color by Regular Expression."
^.*\.cs$
^.*\.fs$
^.*\.vb$
^.*\.cp?p?$
^.*\.hp?p?$
