﻿// XECOM_Main_LocalServer\Models\DTOsFromClient\BillingAddressClientDto.cs
namespace XECOM_Main_LocalServer.Models.DTOsFromClient
{
    public class BillingAddressClientDto
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Company { get; set; }
        public string Phone { get; set; }
        // public string Email { get; set; } // İstemcide yoktu
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string CityName { get; set; }
        public string DistrictName { get; set; }
        public string StateName { get; set; }
        public string PostalCode { get; set; }
        public string CountryName { get; set; }
        public string TaxOffice { get; set; }
        public string TaxNumber { get; set; }
        public string IdentityNumber { get; set; }
    }
}
