﻿IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20250309141405_InitialMigration'
)
BEGIN
    CREATE TABLE [xmain_Addresses] (
        [Id] bigint NOT NULL IDENTITY,
        [AddressLine1] nvarchar(max) NOT NULL,
        [AddressLine2] nvarchar(max) NOT NULL,
        [CityName] nvarchar(max) NOT NULL,
        [DistrictName] nvarchar(max) NOT NULL,
        [StateName] nvarchar(max) NOT NULL,
        [CountryName] nvarchar(max) NOT NULL,
        [Phone] nvarchar(max) NOT NULL,
        [PostalCode] nvarchar(max) NOT NULL,
        [FirstName] nvarchar(max) NOT NULL,
        [LastName] nvarchar(max) NOT NULL,
        [Company] nvarchar(max) NOT NULL,
        [IdentityNumber] nvarchar(max) NOT NULL,
        [IsDefault] bit NULL,
        [TaxNumber] nvarchar(max) NOT NULL,
        [TaxOffice] nvarchar(max) NOT NULL,
        CONSTRAINT [PK_xmain_Addresses] PRIMARY KEY ([Id])
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20250309141405_InitialMigration'
)
BEGIN
    CREATE TABLE [xmain_Orders] (
        [Id] bigint NOT NULL IDENTITY,
        [IkasId] nvarchar(max) NOT NULL,
        [OrderNumber] nvarchar(max) NOT NULL,
        [CreatedAt] datetime2 NULL,
        [UpdatedAt] datetime2 NULL,
        [Status] nvarchar(max) NOT NULL,
        [OrderPaymentStatus] nvarchar(max) NOT NULL,
        [TotalPrice] decimal(18,2) NULL,
        [TotalFinalPrice] decimal(18,2) NULL,
        [Deleted] bit NULL,
        [CancelReason] nvarchar(max) NOT NULL,
        [CancelledAt] datetime2 NULL,
        [ClientIp] nvarchar(max) NOT NULL,
        [Host] nvarchar(max) NOT NULL,
        [UserAgent] nvarchar(max) NOT NULL,
        [IsGiftPackage] bit NULL,
        [GiftPackageNote] nvarchar(max) NOT NULL,
        [OrderPackageStatus] nvarchar(max) NOT NULL,
        [OrderSequence] int NULL,
        [OrderedAt] datetime2 NULL,
        [BillingAddressId] bigint NULL,
        [ShippingAddressId] bigint NULL,
        [InsertedAt] datetime2 NULL,
        [RecordDate] datetime2 NULL,
        CONSTRAINT [PK_xmain_Orders] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_xmain_Orders_xmain_Addresses_BillingAddressId] FOREIGN KEY ([BillingAddressId]) REFERENCES [xmain_Addresses] ([Id]),
        CONSTRAINT [FK_xmain_Orders_xmain_Addresses_ShippingAddressId] FOREIGN KEY ([ShippingAddressId]) REFERENCES [xmain_Addresses] ([Id])
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20250309141405_InitialMigration'
)
BEGIN
    CREATE TABLE [xmain_OrderLineItems] (
        [Id] bigint NOT NULL IDENTITY,
        [IkasId] nvarchar(max) NOT NULL,
        [OrderId] bigint NOT NULL,
        [Quantity] int NULL,
        [Price] decimal(18,2) NULL,
        [FinalPrice] decimal(18,2) NULL,
        [VariantName] nvarchar(max) NOT NULL,
        [VariantSku] nvarchar(max) NOT NULL,
        CONSTRAINT [PK_xmain_OrderLineItems] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_xmain_OrderLineItems_xmain_Orders_OrderId] FOREIGN KEY ([OrderId]) REFERENCES [xmain_Orders] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20250309141405_InitialMigration'
)
BEGIN
    CREATE TABLE [xmain_OrderPackages] (
        [Id] bigint NOT NULL IDENTITY,
        [IkasId] nvarchar(max) NOT NULL,
        [OrderId] bigint NOT NULL,
        [Deleted] bit NULL,
        [ErrorMessage] nvarchar(max) NOT NULL,
        [Note] nvarchar(max) NOT NULL,
        [OrderPackageFulfillStatus] nvarchar(max) NOT NULL,
        [OrderPackageNumber] nvarchar(max) NOT NULL,
        [StockLocationId] nvarchar(max) NOT NULL,
        [CreatedAt] datetime2 NULL,
        [UpdatedAt] datetime2 NULL,
        [TrackingBarcode] nvarchar(max) NOT NULL,
        [TrackingCargoCompany] nvarchar(max) NOT NULL,
        [TrackingLink] nvarchar(max) NOT NULL,
        [TrackingNumber] nvarchar(max) NOT NULL,
        [OrderLineItemIds] nvarchar(max) NOT NULL,
        CONSTRAINT [PK_xmain_OrderPackages] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_xmain_OrderPackages_xmain_Orders_OrderId] FOREIGN KEY ([OrderId]) REFERENCES [xmain_Orders] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20250309141405_InitialMigration'
)
BEGIN
    CREATE INDEX [IX_xmain_OrderLineItems_OrderId] ON [xmain_OrderLineItems] ([OrderId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20250309141405_InitialMigration'
)
BEGIN
    CREATE INDEX [IX_xmain_OrderPackages_OrderId] ON [xmain_OrderPackages] ([OrderId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20250309141405_InitialMigration'
)
BEGIN
    CREATE INDEX [IX_xmain_Orders_BillingAddressId] ON [xmain_Orders] ([BillingAddressId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20250309141405_InitialMigration'
)
BEGIN
    CREATE INDEX [IX_xmain_Orders_ShippingAddressId] ON [xmain_Orders] ([ShippingAddressId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20250309141405_InitialMigration'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20250309141405_InitialMigration', N'8.0.10');
END;
GO

COMMIT;
GO

