﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace XECOM_Main_LocalServer.Migrations
{
    /// <inheritdoc />
    public partial class InitialMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "xmain_Addresses",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AddressLine1 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AddressLine2 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CityName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DistrictName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    StateName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CountryName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Phone = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PostalCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FirstName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    LastName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Company = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IdentityNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsDefault = table.Column<bool>(type: "bit", nullable: true),
                    TaxNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TaxOffice = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_xmain_Addresses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "xmain_Orders",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    IkasId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderPaymentStatus = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TotalPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    TotalFinalPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Deleted = table.Column<bool>(type: "bit", nullable: true),
                    CancelReason = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CancelledAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ClientIp = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Host = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    UserAgent = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsGiftPackage = table.Column<bool>(type: "bit", nullable: true),
                    GiftPackageNote = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderPackageStatus = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderSequence = table.Column<int>(type: "int", nullable: true),
                    OrderedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    BillingAddressId = table.Column<long>(type: "bigint", nullable: true),
                    ShippingAddressId = table.Column<long>(type: "bigint", nullable: true),
                    InsertedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    RecordDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_xmain_Orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_xmain_Orders_xmain_Addresses_BillingAddressId",
                        column: x => x.BillingAddressId,
                        principalTable: "xmain_Addresses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_xmain_Orders_xmain_Addresses_ShippingAddressId",
                        column: x => x.ShippingAddressId,
                        principalTable: "xmain_Addresses",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "xmain_OrderLineItems",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    IkasId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderId = table.Column<long>(type: "bigint", nullable: false),
                    Quantity = table.Column<int>(type: "int", nullable: true),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    FinalPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    VariantName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    VariantSku = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_xmain_OrderLineItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_xmain_OrderLineItems_xmain_Orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "xmain_Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "xmain_OrderPackages",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    IkasId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderId = table.Column<long>(type: "bigint", nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: true),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Note = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderPackageFulfillStatus = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderPackageNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    StockLocationId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TrackingBarcode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TrackingCargoCompany = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TrackingLink = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TrackingNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OrderLineItemIds = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_xmain_OrderPackages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_xmain_OrderPackages_xmain_Orders_OrderId",
                        column: x => x.OrderId,
                        principalTable: "xmain_Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_xmain_OrderLineItems_OrderId",
                table: "xmain_OrderLineItems",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_xmain_OrderPackages_OrderId",
                table: "xmain_OrderPackages",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_xmain_Orders_BillingAddressId",
                table: "xmain_Orders",
                column: "BillingAddressId");

            migrationBuilder.CreateIndex(
                name: "IX_xmain_Orders_ShippingAddressId",
                table: "xmain_Orders",
                column: "ShippingAddressId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "xmain_OrderLineItems");

            migrationBuilder.DropTable(
                name: "xmain_OrderPackages");

            migrationBuilder.DropTable(
                name: "xmain_Orders");

            migrationBuilder.DropTable(
                name: "xmain_Addresses");
        }
    }
}
