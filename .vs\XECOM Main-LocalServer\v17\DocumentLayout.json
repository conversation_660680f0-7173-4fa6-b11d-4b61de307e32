{"Version": 1, "WorkspaceRootPath": "C:\\Qix\\XECOM Main-LocalServer\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\services\\erpservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:services\\erpservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\services\\datasynchub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:services\\datasynchub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\netsismodels\\netsisorderheader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\netsismodels\\netsisorderheader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\xmain_addresses.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\xmain_addresses.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\netsisapiresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\netsisapiresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\netsismodels\\netsisorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\netsismodels\\netsisorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\netsismodels\\netsisorderitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\netsismodels\\netsisorderitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\netsismodels\\netsistokenresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\netsismodels\\netsistokenresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\services\\encryptionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:services\\encryptionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\services\\datasyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:services\\datasyncservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\controllers\\datacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:controllers\\datacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\productmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\productmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\xmain_orderpackages.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\xmain_orderpackages.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\xmain_orderlineitems.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\xmain_orderlineitems.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\services\\clientdtotomodelmapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:services\\clientdtotomodelmapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\dtosfromclient\\orderclientdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\dtosfromclient\\orderclientdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\dtosfromclient\\orderpackageclientdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\dtosfromclient\\orderpackageclientdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\dtosfromclient\\orderlineitemclientdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\dtosfromclient\\orderlineitemclientdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\dtosfromclient\\billingaddressclientdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\dtosfromclient\\billingaddressclientdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\tokenresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\tokenresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\controllers\\testcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:controllers\\testcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\xmain_orders.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\xmain_orders.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\models\\changemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:models\\changemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|c:\\qix\\xecom main-localserver\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8E87C1EC-314D-4D75-9E29-CD390FA98AAE}|XECOM Main-LocalServer.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 13, "Children": [{"$type": "Document", "DocumentIndex": 8, "Title": "NetsisTokenResponse.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisModels\\NetsisTokenResponse.cs", "RelativeDocumentMoniker": "Models\\NetsisModels\\NetsisTokenResponse.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisModels\\NetsisTokenResponse.cs", "RelativeToolTip": "Models\\NetsisModels\\NetsisTokenResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T16:33:18.86Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "NetsisOrderItem.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisModels\\NetsisOrderItem.cs", "RelativeDocumentMoniker": "Models\\NetsisModels\\NetsisOrderItem.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisModels\\NetsisOrderItem.cs", "RelativeToolTip": "Models\\NetsisModels\\NetsisOrderItem.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T16:32:31.668Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "NetsisOrderHeader.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisModels\\NetsisOrderHeader.cs", "RelativeDocumentMoniker": "Models\\NetsisModels\\NetsisOrderHeader.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisModels\\NetsisOrderHeader.cs", "RelativeToolTip": "Models\\NetsisModels\\NetsisOrderHeader.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T16:31:58.619Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "NetsisOrder.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisModels\\NetsisOrder.cs", "RelativeDocumentMoniker": "Models\\NetsisModels\\NetsisOrder.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisModels\\NetsisOrder.cs", "RelativeToolTip": "Models\\NetsisModels\\NetsisOrder.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T16:31:28.951Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "NetsisApiResponse.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisApiResponse.cs", "RelativeDocumentMoniker": "Models\\NetsisApiResponse.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\NetsisApiResponse.cs", "RelativeToolTip": "Models\\NetsisApiResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-26T15:36:37.818Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "ClientDtoToModelMapper.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Services\\ClientDtoToModelMapper.cs", "RelativeDocumentMoniker": "Services\\ClientDtoToModelMapper.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Services\\ClientDtoToModelMapper.cs", "RelativeToolTip": "Services\\ClientDtoToModelMapper.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T16:04:48.828Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "OrderClientDto.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\DTOsFromClient\\OrderClientDto.cs", "RelativeDocumentMoniker": "Models\\DTOsFromClient\\OrderClientDto.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\DTOsFromClient\\OrderClientDto.cs", "RelativeToolTip": "Models\\DTOsFromClient\\OrderClientDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T16:03:52.84Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "OrderPackageClientDto.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\DTOsFromClient\\OrderPackageClientDto.cs", "RelativeDocumentMoniker": "Models\\DTOsFromClient\\OrderPackageClientDto.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\DTOsFromClient\\OrderPackageClientDto.cs", "RelativeToolTip": "Models\\DTOsFromClient\\OrderPackageClientDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T16:03:26.407Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "OrderLineItemClientDto.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\DTOsFromClient\\OrderLineItemClientDto.cs", "RelativeDocumentMoniker": "Models\\DTOsFromClient\\OrderLineItemClientDto.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\DTOsFromClient\\OrderLineItemClientDto.cs", "RelativeToolTip": "Models\\DTOsFromClient\\OrderLineItemClientDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T16:02:59.777Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "BillingAddressClientDto.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\DTOsFromClient\\BillingAddressClientDto.cs", "RelativeDocumentMoniker": "Models\\DTOsFromClient\\BillingAddressClientDto.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\DTOsFromClient\\BillingAddressClientDto.cs", "RelativeToolTip": "Models\\DTOsFromClient\\BillingAddressClientDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T16:02:21.023Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "TokenResponse.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\TokenResponse.cs", "RelativeDocumentMoniker": "Models\\TokenResponse.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\TokenResponse.cs", "RelativeToolTip": "Models\\TokenResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T15:55:56.803Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "TestController.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Controllers\\TestController.cs", "RelativeDocumentMoniker": "Controllers\\TestController.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Controllers\\TestController.cs", "RelativeToolTip": "Controllers\\TestController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADIAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T23:41:05.166Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "xmain_Orders.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\xmain_Orders.cs", "RelativeDocumentMoniker": "Models\\xmain_Orders.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\xmain_Orders.cs", "RelativeToolTip": "Models\\xmain_Orders.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-08T22:20:49.909Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ERPService.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Services\\ERPService.cs", "RelativeDocumentMoniker": "Services\\ERPService.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Services\\ERPService.cs", "RelativeToolTip": "Services\\ERPService.cs", "ViewState": "AgIAABUAAAAAAAAAAAAAACsAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-08T22:05:49.817Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "ChangeModel.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\ChangeModel.cs", "RelativeDocumentMoniker": "Models\\ChangeModel.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\ChangeModel.cs", "RelativeToolTip": "Models\\ChangeModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-08T20:53:35.758Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "Program.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABABAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-24T15:06:59.732Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "DataSyncHub.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Services\\DataSyncHub.cs", "RelativeDocumentMoniker": "Services\\DataSyncHub.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Services\\DataSyncHub.cs", "RelativeToolTip": "Services\\DataSyncHub.cs", "ViewState": "AgIAAI0AAAAAAAAAAADwv5cAAABgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-30T11:17:02.939Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.json", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAABsAAAAAAAAAAAAAADAAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-10-24T16:21:51.894Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "xmain_OrderLineItems.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\xmain_OrderLineItems.cs", "RelativeDocumentMoniker": "Models\\xmain_OrderLineItems.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\xmain_OrderLineItems.cs", "RelativeToolTip": "Models\\xmain_OrderLineItems.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-08T22:22:36.009Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "xmain_OrderPackages.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\xmain_OrderPackages.cs", "RelativeDocumentMoniker": "Models\\xmain_OrderPackages.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\xmain_OrderPackages.cs", "RelativeToolTip": "Models\\xmain_OrderPackages.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-08T22:23:12.978Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "xmain_Addresses.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\xmain_Addresses.cs", "RelativeDocumentMoniker": "Models\\xmain_Addresses.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\xmain_Addresses.cs", "RelativeToolTip": "Models\\xmain_Addresses.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAC8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-08T22:23:54.968Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "ProductModel.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Models\\ProductModel.cs", "RelativeDocumentMoniker": "Models\\ProductModel.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Models\\ProductModel.cs", "RelativeToolTip": "Models\\ProductModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBJwDkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-24T14:33:36.044Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "DataController.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Controllers\\DataController.cs", "RelativeDocumentMoniker": "Controllers\\DataController.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Controllers\\DataController.cs", "RelativeToolTip": "Controllers\\DataController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-08T20:22:32.512Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "DataSyncService.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Services\\DataSyncService.cs", "RelativeDocumentMoniker": "Services\\DataSyncService.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Services\\DataSyncService.cs", "RelativeToolTip": "Services\\DataSyncService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAANoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-30T11:17:07.628Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "EncryptionService.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Services\\EncryptionService.cs", "RelativeDocumentMoniker": "Services\\EncryptionService.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Services\\EncryptionService.cs", "RelativeToolTip": "Services\\EncryptionService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAI4AAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-24T16:56:54.03Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "AppDbContext.cs", "DocumentMoniker": "C:\\Qix\\XECOM Main-LocalServer\\Data\\AppDbContext.cs", "RelativeDocumentMoniker": "Data\\AppDbContext.cs", "ToolTip": "C:\\Qix\\XECOM Main-LocalServer\\Data\\AppDbContext.cs", "RelativeToolTip": "Data\\AppDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-08T20:53:15.12Z"}]}]}]}