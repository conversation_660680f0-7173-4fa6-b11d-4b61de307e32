2024-10-22 03:22:19.028 +03:00 [INF] Uygulama başlatılıyor
2024-10-22 03:22:20.203 +03:00 [WRN] No store type was specified for the decimal property 'Discount_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-22 03:22:20.207 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-22 03:22:20.209 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-22 03:22:20.211 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-22 03:22:20.212 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-22 03:22:20.213 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-22 03:22:20.215 +03:00 [WRN] No store type was specified for the decimal property 'Regular_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-22 03:22:20.216 +03:00 [WRN] No store type was specified for the decimal property 'Stock_Quantity' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-22 03:22:20.844 +03:00 [ERR] Failed executing DbCommand (71ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Size = 4000), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
EXEC NSP_NETSESGUNCELLE @p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11
2024-10-22 03:22:20.933 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'NETSIS..NETSESSION'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__210_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 77
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 48
ClientConnectionId:318a70b6-a1ff-4bba-9c76-0dec35e117ed
Error Number:208,State:1,Class:16
2024-10-22 03:22:32.628 +03:00 [ERR] Failed executing DbCommand (383ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ID], [a].[Assortment_type], [a].[Brand], [a].[Category], [a].[Color_Code_Primary], [a].[Currency], [a].[Description], [a].[Discount_Price], [a].[EAN], [a].[Material_Upper_Material_Clothing1], [a].[Material_Upper_Material_Clothing1_Ratio], [a].[Material_Upper_Material_Clothing2], [a].[Material_Upper_Material_Clothing2_Ratio], [a].[Material_Upper_Material_Clothing3], [a].[Material_Upper_Material_Clothing3_Ratio], [a].[Material_Upper_Material_Clothing4], [a].[Material_Upper_Material_Clothing4_Ratio], [a].[Material_Upper_Material_Clothing5], [a].[Material_Upper_Material_Clothing5_Ratio], [a].[Media1_URL], [a].[Media2_URL], [a].[Media3_URL], [a].[Media4_URL], [a].[Media5_URL], [a].[Model_ID], [a].[Name], [a].[Regular_Price], [a].[SKU_ID], [a].[Season], [a].[Size], [a].[Size1], [a].[Stock_Code], [a].[Stock_Quantity], [a].[Supplier_Color], [a].[Target_Age_Groups], [a].[Target_Genders], [a].[stok_last_update]
FROM [AlkProductList2] AS [a]
2024-10-22 03:22:32.645 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'NETSIS..NETSESSION'.
Could not use view or function 'AlkProductList2' because of binding errors.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:318a70b6-a1ff-4bba-9c76-0dec35e117ed
Error Number:208,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'NETSIS..NETSESSION'.
Could not use view or function 'AlkProductList2' because of binding errors.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:318a70b6-a1ff-4bba-9c76-0dec35e117ed
Error Number:208,State:1,Class:16
2024-10-22 03:22:32.718 +03:00 [ERR] Error occurred while fetching products
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'NETSIS..NETSESSION'.
Could not use view or function 'AlkProductList2' because of binding errors.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
ClientConnectionId:318a70b6-a1ff-4bba-9c76-0dec35e117ed
Error Number:208,State:1,Class:16
