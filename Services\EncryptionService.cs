﻿// XECOM_Main_LocalServer\Services\EncryptionService.cs
using Microsoft.Extensions.Configuration;
using Serilog; // Global Serilog logger için (Log.Error)
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json.Serialization; // System.Text.Json için

namespace XECOM_Main_LocalServer.Services
{
    public static class EncryptionService
    {
        private static readonly System.Text.Json.JsonSerializerOptions DefaultJsonSerializerOptions = new()
        {
            PropertyNameCaseInsensitive = true, // Deserialize ederken esneklik
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull, // Null değerleri JSON'a yazma
            // Sunucudan istemciye JSON gönderirken camelCase isteniyorsa:
            // PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
        };

        public static string Encrypt(string plainText, IConfiguration configuration)
        {
            if (string.IsNullOrEmpty(plainText))
            {
                Log.Warning("[EncryptionService-Server] Encrypt called with null or empty plainText.");
                return string.Empty;
            }

            var keyBase64 = configuration["Encryption:Key"];
            var ivBase64 = configuration["Encryption:IV"];

            if (string.IsNullOrEmpty(keyBase64) || string.IsNullOrEmpty(ivBase64))
            {
                Log.Error("[EncryptionService-Server] Encryption key or IV not found in configuration for encryption. Please check appsettings.json (Encryption:Key, Encryption:IV).");
                throw new InvalidOperationException("Encryption key or IV not found in configuration for encryption.");
            }

            byte[] key;
            byte[] iv;
            try
            {
                key = Convert.FromBase64String(keyBase64);
                iv = Convert.FromBase64String(ivBase64);
            }
            catch (FormatException ex)
            {
                Log.Error(ex, "[EncryptionService-Server] Encryption key or IV is not a valid Base64 string for encryption.");
                throw;
            }

            try
            {
                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;
                aes.Padding = PaddingMode.PKCS7; // İstemci ile aynı olmalı
                aes.Mode = CipherMode.CBC;       // İstemci ile aynı olmalı

                using var ms = new MemoryStream();
                // using bloklarını doğru kullanmak önemli
                using (var encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
                {
                    using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    {
                        using (var sw = new StreamWriter(cs, Encoding.UTF8)) // UTF8 önemli
                        {
                            sw.Write(plainText);
                        } // sw (StreamWriter) burada dispose edilir, bu da cs'yi flush eder.
                    } // cs (CryptoStream) burada dispose edilir.
                } // encryptor burada dispose edilir.

                return Convert.ToBase64String(ms.ToArray());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Server] Unexpected error during encryption.");
                throw;
            }
        }

        public static string Decrypt(string encryptedBase64, IConfiguration configuration)
        {
            if (string.IsNullOrEmpty(encryptedBase64))
            {
                Log.Warning("[EncryptionService-Server] Decrypt called with null or empty encryptedBase64 string.");
                return null;
            }

            var keyBase64 = configuration["Encryption:Key"];
            var ivBase64 = configuration["Encryption:IV"];

            if (string.IsNullOrEmpty(keyBase64) || string.IsNullOrEmpty(ivBase64))
            {
                Log.Error("[EncryptionService-Server] Encryption key or IV not found in configuration for decryption. Please check appsettings.json (Encryption:Key, Encryption:IV).");
                throw new InvalidOperationException("Encryption key or IV not found in configuration for decryption.");
            }

            byte[] key;
            byte[] iv;
            try
            {
                key = Convert.FromBase64String(keyBase64);
                iv = Convert.FromBase64String(ivBase64);
            }
            catch (FormatException ex)
            {
                Log.Error(ex, "[EncryptionService-Server] Encryption key or IV is not a valid Base64 string for decryption.");
                throw;
            }

            try
            {
                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;
                aes.Padding = PaddingMode.PKCS7;
                aes.Mode = CipherMode.CBC;

                byte[] cipherBytes;
                try
                {
                    cipherBytes = Convert.FromBase64String(encryptedBase64);
                }
                catch (FormatException ex)
                {
                    Log.Error(ex, "[EncryptionService-Server] Decryption failed. Encrypted data is not a valid Base64 string. Data preview (first 50 chars): {DataPreview}", encryptedBase64.Length > 50 ? encryptedBase64.Substring(0, 50) : encryptedBase64);
                    throw;
                }

                using var ms = new MemoryStream();
                using (var decryptor = aes.CreateDecryptor(aes.Key, aes.IV))
                {
                    using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Write))
                    {
                        cs.Write(cipherBytes, 0, cipherBytes.Length);
                        cs.FlushFinalBlock();
                    }
                }
                return Encoding.UTF8.GetString(ms.ToArray());
            }
            catch (CryptographicException ex)
            {
                Log.Error(ex, "[EncryptionService-Server] Cryptographic error during decryption. Possible issues: incorrect padding, key, or IV. Data preview (first 50 chars): {DataPreview}", encryptedBase64.Length > 50 ? encryptedBase64.Substring(0, 50) : encryptedBase64);
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Server] Unexpected error during decryption.");
                throw;
            }
        }

        /// <summary>
        /// "System.Text.Json" ile dizi -> JSON -> AES.
        /// (DataSyncService tarafından kullanılacak)
        /// </summary>
        public static string EncryptData<T>(T[] data, IConfiguration configuration)
        {
            if (data == null)
            {
                Log.Warning("[EncryptionService-Server] EncryptData<T> called with null data array.");
                return null; // Veya boş bir şifreli string
            }
            try
            {
                var jsonData = System.Text.Json.JsonSerializer.Serialize(data, DefaultJsonSerializerOptions);
                return Encrypt(jsonData, configuration);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Server] EncryptData<T> (System.Text.Json) error.");
                throw;
            }
        }

        /// <summary>
        /// "System.Text.Json" (veya istenirse Newtonsoft) ile dizi -> JSON -> AES.
        /// (DataSyncService tarafından kullanılacak)
        /// </summary>
        public static string EncryptChanges<T>(T[] changes, IConfiguration configuration)
        {
            if (changes == null)
            {
                Log.Warning("[EncryptionService-Server] EncryptChanges<T> called with null changes array.");
                return null; // Veya boş bir şifreli string
            }
            try
            {
                // Eğer Newtonsoft.Json kullanılacaksa:
                // var jsonData = Newtonsoft.Json.JsonConvert.SerializeObject(changes);
                // System.Text.Json için:
                var jsonData = System.Text.Json.JsonSerializer.Serialize(changes, DefaultJsonSerializerOptions);
                return Encrypt(jsonData, configuration);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "[EncryptionService-Server] EncryptChanges<T> error.");
                throw;
            }
        }

        // Sunucu tarafında gelen veriyi Decrypt ettikten sonra JSON'dan objeye çevirme işlemini
        // genellikle DataSyncHub gibi çağıran katmanlar yapıyor.
        // Eğer burada da generic DecryptData<T> gibi metodlar istenirse,
        // istemcideki gibi eklenebilirler. Şimdilik sadece string Decrypt yeterli görünüyor.
    }
}