﻿// XECOM_Main_LocalServer\Models\TokenResponse.cs
using System.Text.Json.Serialization; // Veya Newtonsoft.Json

namespace XECOM_Main_LocalServer.Models
{
    public class TokenResponse
    {
        [JsonPropertyName("access_token")] // System.Text.Json için
        // [JsonProperty("access_token")] // Newtonsoft.Json için
        public string AccessToken { get; set; }

        [JsonPropertyName("token_type")]
        // [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonPropertyName("expires_in")]
        // [JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonPropertyName("refresh_token")]
        // [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }
    }
}