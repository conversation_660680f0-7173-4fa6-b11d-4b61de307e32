﻿// XECOM_Main_LocalServer\Models\XmainAddress.cs
using System.Collections.Generic; // ICollection için
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace XECOM_Main_LocalServer.Models
{
    [Table("XmainAddresses")] // Örnek tablo adı, kendi veritabanı şemanıza göre ayarlayın
    public class XmainAddress
    {
        [Key]
        public long Id { get; set; } // Birincil anahtar

        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Company { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; } // İstemciden geliyorsa kullanılır
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string CityName { get; set; }
        public string DistrictName { get; set; }
        public string StateName { get; set; } // Eyalet/Bölge (IksAddress'te vardı)
        public string PostalCode { get; set; }
        public string CountryName { get; set; }
        public string TaxOffice { get; set; }
        public string TaxNumber { get; set; }
        public string IdentityNumber { get; set; } // TCKN

        [JsonIgnore]
        public string NetsisUnvan => !string.IsNullOrWhiteSpace(Company) ? Company.Trim() : $"{FirstName} {LastName}".Trim();

        // Bu adresi fatura adresi olarak kullanan siparişler (EF Core ilişkisi için)
        [JsonIgnore]
        public virtual ICollection<XmainOrder> OrdersAsBillingAddress { get; set; }

        // Bu adresi teslimat adresi olarak kullanan siparişler (EF Core ilişkisi için)
        [JsonIgnore]
        public virtual ICollection<XmainOrder> OrdersAsShippingAddress { get; set; }

        public XmainAddress()
        {
            OrdersAsBillingAddress = new HashSet<XmainOrder>();
            OrdersAsShippingAddress = new HashSet<XmainOrder>();
        }
    }
}