2024-10-24 00:45:52.257 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 00:46:07.701 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:46:07.705 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:46:07.709 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:46:07.711 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:46:07.712 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:46:08.837 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 00:46:10.320 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 00:46:10.320 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 00:46:10.321 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 00:46:11.729 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:46:11.733 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:46:11.919 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 126
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 58
2024-10-24 00:46:11.919 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 00:46:20.869 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:46:20.940 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 00:47:39.289 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:47:39.348 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 00:47:46.019 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:47:46.083 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 00:48:11.265 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 00:48:24.721 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:48:24.725 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:48:24.726 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:48:24.727 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:48:24.728 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 00:48:33.526 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 00:48:41.537 +03:00 [ERR] Failed executing DbCommand (62ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Size = 4000), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
EXEC NSP_NETSESGUNCELLE @p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11
2024-10-24 00:48:44.623 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 00:48:47.508 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 00:48:49.355 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of PRIMARY KEY constraint 'NETSESSION_PKEY'. Cannot insert duplicate key in object 'dbo.NETSESSION'. The duplicate key value is (DESKTOP-4PMOKM2_173548    _Core Microsoft SqlClient Data Provider).
The statement has been terminated.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__210_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 78
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 47
ClientConnectionId:efcbc23b-709b-4b99-8d42-704c5c842996
Error Number:2627,State:1,Class:14
2024-10-24 00:48:58.088 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:53:32.056 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 00:53:37.362 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 00:53:38.524 +03:00 [ERR] Failed executing DbCommand (79ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Size = 4000), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
EXEC NSP_NETSESGUNCELLE @p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11
2024-10-24 00:53:38.524 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 00:53:38.532 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 00:53:38.609 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of PRIMARY KEY constraint 'NETSESSION_PKEY'. Cannot insert duplicate key in object 'dbo.NETSESSION'. The duplicate key value is (DESKTOP-4PMOKM2_237476    _Core Microsoft SqlClient Data Provider).
The statement has been terminated.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__210_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 78
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 47
ClientConnectionId:68936428-7e12-4bd5-afca-252897909acb
Error Number:2627,State:1,Class:14
2024-10-24 00:53:39.941 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:53:40.060 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 00:53:45.657 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:53:45.732 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 00:55:08.101 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 00:55:11.078 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 00:55:11.479 +03:00 [ERR] Failed executing DbCommand (67ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (Size = 4000), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (Size = 4000), @p9='?' (DbType = Int32), @p10='?' (DbType = Int32), @p11='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
EXEC NSP_NETSESGUNCELLE @p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11
2024-10-24 00:55:11.480 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 00:55:11.549 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
Microsoft.Data.SqlClient.SqlException (0x80131904): Violation of PRIMARY KEY constraint 'NETSESSION_PKEY'. Cannot insert duplicate key in object 'dbo.NETSESSION'. The duplicate key value is (DESKTOP-4PMOKM2_219184    _Core Microsoft SqlClient Data Provider).
The statement has been terminated.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__210_1(IAsyncResult result)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 81
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 43
ClientConnectionId:b4727653-9c1d-408f-bf79-fcd25dd07bb4
Error Number:2627,State:1,Class:14
2024-10-24 00:55:12.884 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:55:12.999 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method18(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 126
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 58
2024-10-24 00:56:06.758 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method28(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method28(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 00:56:06.834 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Decimal' to type 'System.String'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method28(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 00:57:52.776 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 00:58:26.903 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 00:58:41.418 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.NullReferenceException: Object reference not set to an instance of an object.
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindCollectionMapping(RelationalTypeMappingInfo info, Type modelType, Type providerType, CoreTypeMapping elementMapping)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.<>c.<FindMappingWithConversion>b__8_0(ValueTuple`4 k, RelationalTypeMappingSource self)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd[TArg](TKey key, Func`3 valueFactory, TArg factoryArgument)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, Type providerClrType, ValueConverter customConverter)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, IReadOnlyList`1 principals)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMapping(MemberInfo member)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.MemberClassifier.IsCandidatePrimitiveProperty(MemberInfo memberInfo, IConventionModel model, CoreTypeMapping& typeMapping)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.Process(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.ProcessEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder, IConventionContext`1 context)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnEntityTypeAddedNode.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.DelayedConventionScope.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Run()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Dispose()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.Model..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelBuilder..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelConfigurationBuilder.CreateModelBuilder(ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.CreateModel(DbContext context, IConventionSetBuilder conventionSetBuilder, ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.Microsoft.EntityFrameworkCore.Storage.IDatabaseFacadeDependenciesAccessor.get_Dependencies()
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.GetFacadeDependencies(DatabaseFacade databaseFacade)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 78
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 47
2024-10-24 00:58:42.433 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 00:58:45.469 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.NullReferenceException: Object reference not set to an instance of an object.
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindCollectionMapping(RelationalTypeMappingInfo info, Type modelType, Type providerType, CoreTypeMapping elementMapping)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.<>c.<FindMappingWithConversion>b__8_0(ValueTuple`4 k, RelationalTypeMappingSource self)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd[TArg](TKey key, Func`3 valueFactory, TArg factoryArgument)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, Type providerClrType, ValueConverter customConverter)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, IReadOnlyList`1 principals)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMapping(MemberInfo member)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.MemberClassifier.IsCandidatePrimitiveProperty(MemberInfo memberInfo, IConventionModel model, CoreTypeMapping& typeMapping)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.Process(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.ProcessEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder, IConventionContext`1 context)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnEntityTypeAddedNode.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.DelayedConventionScope.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Run()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Dispose()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.Model..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelBuilder..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelConfigurationBuilder.CreateModelBuilder(ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.CreateModel(DbContext context, IConventionSetBuilder conventionSetBuilder, ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.Microsoft.EntityFrameworkCore.Storage.IDatabaseFacadeDependenciesAccessor.get_Dependencies()
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.GetFacadeDependencies(DatabaseFacade databaseFacade)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 81
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 43
2024-10-24 00:59:07.246 +03:00 [ERR] Error occurred while fetching products
System.NullReferenceException: Object reference not set to an instance of an object.
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindCollectionMapping(RelationalTypeMappingInfo info, Type modelType, Type providerType, CoreTypeMapping elementMapping)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.<>c.<FindMappingWithConversion>b__8_0(ValueTuple`4 k, RelationalTypeMappingSource self)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd[TArg](TKey key, Func`3 valueFactory, TArg factoryArgument)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, Type providerClrType, ValueConverter customConverter)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, IReadOnlyList`1 principals)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMapping(MemberInfo member)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.MemberClassifier.IsCandidatePrimitiveProperty(MemberInfo memberInfo, IConventionModel model, CoreTypeMapping& typeMapping)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.Process(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.ProcessEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder, IConventionContext`1 context)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnEntityTypeAddedNode.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.DelayedConventionScope.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Run()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Dispose()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.Model..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelBuilder..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelConfigurationBuilder.CreateModelBuilder(ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.CreateModel(DbContext context, IConventionSetBuilder conventionSetBuilder, ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_Model()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityType()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.CheckState()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityQueryable()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.System.Collections.Generic.IAsyncEnumerable<TEntity>.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 01:20:30.099 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 01:20:44.544 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.NullReferenceException: Object reference not set to an instance of an object.
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindCollectionMapping(RelationalTypeMappingInfo info, Type modelType, Type providerType, CoreTypeMapping elementMapping)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.<>c.<FindMappingWithConversion>b__8_0(ValueTuple`4 k, RelationalTypeMappingSource self)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd[TArg](TKey key, Func`3 valueFactory, TArg factoryArgument)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, Type providerClrType, ValueConverter customConverter)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, IReadOnlyList`1 principals)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMapping(MemberInfo member)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.MemberClassifier.IsCandidatePrimitiveProperty(MemberInfo memberInfo, IConventionModel model, CoreTypeMapping& typeMapping)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.Process(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.ProcessEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder, IConventionContext`1 context)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnEntityTypeAddedNode.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.DelayedConventionScope.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Run()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Dispose()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.Model..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelBuilder..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelConfigurationBuilder.CreateModelBuilder(ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.CreateModel(DbContext context, IConventionSetBuilder conventionSetBuilder, ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.Microsoft.EntityFrameworkCore.Storage.IDatabaseFacadeDependenciesAccessor.get_Dependencies()
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.GetFacadeDependencies(DatabaseFacade databaseFacade)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 78
   at XECOM_Main_LocalServer.Services.ChangeTrackingService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\ChangeTrackingService.cs:line 47
2024-10-24 01:20:45.598 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 01:20:46.334 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.NullReferenceException: Object reference not set to an instance of an object.
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindCollectionMapping(RelationalTypeMappingInfo info, Type modelType, Type providerType, CoreTypeMapping elementMapping)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.<>c.<FindMappingWithConversion>b__8_0(ValueTuple`4 k, RelationalTypeMappingSource self)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd[TArg](TKey key, Func`3 valueFactory, TArg factoryArgument)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, Type providerClrType, ValueConverter customConverter)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, IReadOnlyList`1 principals)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMapping(MemberInfo member)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.MemberClassifier.IsCandidatePrimitiveProperty(MemberInfo memberInfo, IConventionModel model, CoreTypeMapping& typeMapping)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.Process(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.ProcessEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder, IConventionContext`1 context)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnEntityTypeAddedNode.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.DelayedConventionScope.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Run()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Dispose()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.Model..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelBuilder..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelConfigurationBuilder.CreateModelBuilder(ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.CreateModel(DbContext context, IConventionSetBuilder conventionSetBuilder, ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.Microsoft.EntityFrameworkCore.Storage.IDatabaseFacadeDependenciesAccessor.get_Dependencies()
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.GetFacadeDependencies(DatabaseFacade databaseFacade)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 81
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 43
2024-10-24 01:22:26.716 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 01:26:05.163 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 01:29:45.518 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 01:29:47.892 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 04:15:56.784 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 04:17:50.741 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 04:17:57.591 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 04:23:34.015 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 04:23:40.513 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 04:28:27.742 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 04:28:31.815 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 04:36:18.433 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.NullReferenceException: Object reference not set to an instance of an object.
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindCollectionMapping(RelationalTypeMappingInfo info, Type modelType, Type providerType, CoreTypeMapping elementMapping)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.<>c.<FindMappingWithConversion>b__8_0(ValueTuple`4 k, RelationalTypeMappingSource self)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd[TArg](TKey key, Func`3 valueFactory, TArg factoryArgument)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, Type providerClrType, ValueConverter customConverter)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMappingWithConversion(RelationalTypeMappingInfo mappingInfo, IReadOnlyList`1 principals)
   at Microsoft.EntityFrameworkCore.Storage.RelationalTypeMappingSource.FindMapping(MemberInfo member)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.MemberClassifier.IsCandidatePrimitiveProperty(MemberInfo memberInfo, IConventionModel model, CoreTypeMapping& typeMapping)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.Process(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.PropertyDiscoveryConvention.ProcessEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder, IConventionContext`1 context)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnEntityTypeAdded(IConventionEntityTypeBuilder entityTypeBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnEntityTypeAddedNode.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.DelayedConventionScope.Run(ConventionDispatcher dispatcher)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Run()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ConventionBatch.Dispose()
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.ImmediateConventionScope.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Conventions.Internal.ConventionDispatcher.OnModelInitialized(IConventionModelBuilder modelBuilder)
   at Microsoft.EntityFrameworkCore.Metadata.Internal.Model..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelBuilder..ctor(ConventionSet conventions, ModelDependencies modelDependencies, ModelConfiguration modelConfiguration)
   at Microsoft.EntityFrameworkCore.ModelConfigurationBuilder.CreateModelBuilder(ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.CreateModel(DbContext context, IConventionSetBuilder conventionSetBuilder, ModelDependencies modelDependencies)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitFactory(FactoryCallSite factoryCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.Microsoft.EntityFrameworkCore.Storage.IDatabaseFacadeDependenciesAccessor.get_Dependencies()
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.GetFacadeDependencies(DatabaseFacade databaseFacade)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 81
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 43
2024-10-24 04:40:44.866 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 04:40:47.190 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 04:40:49.458 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:40:49.461 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:40:49.462 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:40:49.464 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:40:49.466 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:40:50.044 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 04:40:50.045 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 04:40:50.465 +03:00 [ERR] Failed executing DbCommand (222ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ID], [a].[Assortment_type], [a].[Brand], [a].[Category], [a].[Color_Code_Primary], [a].[Currency], [a].[Description], [a].[Discount_Price], [a].[EAN], [a].[Material_Upper_Material_Clothing1], [a].[Material_Upper_Material_Clothing1_Ratio], [a].[Material_Upper_Material_Clothing2], [a].[Material_Upper_Material_Clothing2_Ratio], [a].[Material_Upper_Material_Clothing3], [a].[Material_Upper_Material_Clothing3_Ratio], [a].[Material_Upper_Material_Clothing4], [a].[Material_Upper_Material_Clothing4_Ratio], [a].[Material_Upper_Material_Clothing5], [a].[Material_Upper_Material_Clothing5_Ratio], [a].[Media1_URL], [a].[Media2_URL], [a].[Media3_URL], [a].[Media4_URL], [a].[Media5_URL], [a].[Model_ID], [a].[Name], [a].[Regular_Price], [a].[SKU_ID], [a].[Season], [a].[Size], [a].[Size1], [a].[Stock_Code], [a].[Stock_Last_Update], [a].[Stock_Quantity], [a].[Supplier_Color], [a].[Target_Age_Groups], [a].[Target_Genders]
FROM [AlkProductList2] AS [a]
2024-10-24 04:40:50.489 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Stock_Last_Update'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:9aa0a6fd-7979-4e73-8d8c-72f62e927da0
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Stock_Last_Update'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:9aa0a6fd-7979-4e73-8d8c-72f62e927da0
Error Number:207,State:1,Class:16
2024-10-24 04:40:50.588 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Stock_Last_Update'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
ClientConnectionId:9aa0a6fd-7979-4e73-8d8c-72f62e927da0
Error Number:207,State:1,Class:16
2024-10-24 04:40:57.976 +03:00 [ERR] Failed executing DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ID], [a].[Assortment_type], [a].[Brand], [a].[Category], [a].[Color_Code_Primary], [a].[Currency], [a].[Description], [a].[Discount_Price], [a].[EAN], [a].[Material_Upper_Material_Clothing1], [a].[Material_Upper_Material_Clothing1_Ratio], [a].[Material_Upper_Material_Clothing2], [a].[Material_Upper_Material_Clothing2_Ratio], [a].[Material_Upper_Material_Clothing3], [a].[Material_Upper_Material_Clothing3_Ratio], [a].[Material_Upper_Material_Clothing4], [a].[Material_Upper_Material_Clothing4_Ratio], [a].[Material_Upper_Material_Clothing5], [a].[Material_Upper_Material_Clothing5_Ratio], [a].[Media1_URL], [a].[Media2_URL], [a].[Media3_URL], [a].[Media4_URL], [a].[Media5_URL], [a].[Model_ID], [a].[Name], [a].[Regular_Price], [a].[SKU_ID], [a].[Season], [a].[Size], [a].[Size1], [a].[Stock_Code], [a].[Stock_Last_Update], [a].[Stock_Quantity], [a].[Supplier_Color], [a].[Target_Age_Groups], [a].[Target_Genders]
FROM [AlkProductList2] AS [a]
2024-10-24 04:40:57.982 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Stock_Last_Update'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:9aa0a6fd-7979-4e73-8d8c-72f62e927da0
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Stock_Last_Update'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:9aa0a6fd-7979-4e73-8d8c-72f62e927da0
Error Number:207,State:1,Class:16
2024-10-24 04:40:58.030 +03:00 [ERR] Error occurred while fetching products
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'Stock_Last_Update'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__211_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
ClientConnectionId:9aa0a6fd-7979-4e73-8d8c-72f62e927da0
Error Number:207,State:1,Class:16
2024-10-24 04:48:07.757 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 04:48:16.909 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 04:48:18.374 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:48:18.377 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:48:18.379 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:48:18.380 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:48:18.381 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:48:18.813 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 04:48:18.814 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 04:48:19.437 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 04:48:19.577 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 04:48:23.953 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 04:48:24.027 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 04:49:28.295 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 04:49:28.370 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 04:49:28.883 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:49:28.885 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:49:28.886 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:49:28.887 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:49:28.889 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 04:49:29.344 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 04:49:29.346 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 04:49:29.895 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 04:49:30.053 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 04:49:37.937 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 04:49:38.009 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 04:49:39.704 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 04:49:39.766 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 04:54:30.066 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 04:54:30.078 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 04:54:30.080 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 04:54:30.269 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 04:54:30.356 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 04:59:30.375 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 04:59:30.389 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 04:59:30.390 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 04:59:30.573 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 04:59:30.653 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:04:30.666 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:04:30.671 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:04:30.672 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:04:30.850 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:04:30.933 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:09:30.943 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:09:30.955 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:09:30.956 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:09:31.157 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:09:31.238 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:14:31.245 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:14:31.264 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:14:31.265 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:14:31.442 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:14:31.527 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:19:31.539 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:19:31.552 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:19:31.552 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:19:31.736 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:19:31.816 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:24:31.826 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:24:31.845 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:24:31.846 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:24:32.030 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:24:32.113 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:29:34.068 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:29:34.078 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:29:34.079 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:29:34.254 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:29:34.343 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:34:34.358 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:34:34.370 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:34:34.371 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:34:34.549 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:34:34.627 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:39:34.643 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:39:34.657 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:39:34.658 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:39:34.839 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:39:34.915 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:44:34.919 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:44:34.937 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:44:34.937 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:44:35.113 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:44:35.191 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:49:35.195 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:49:35.204 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:49:35.205 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:49:35.386 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:49:35.464 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:54:35.478 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:54:35.487 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:54:35.488 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:54:35.672 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:54:35.758 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 05:59:35.771 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 05:59:35.786 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 05:59:35.787 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 05:59:35.972 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 05:59:36.050 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:04:36.065 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:04:36.077 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:04:36.079 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:04:36.379 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:04:36.508 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:09:36.524 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:09:36.534 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:09:36.535 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:09:36.713 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:09:36.790 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:14:36.807 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:14:36.817 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:14:36.818 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:14:36.993 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:14:37.069 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:19:37.074 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:19:37.093 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:19:37.094 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:19:37.395 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:19:37.523 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:24:37.537 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:24:37.544 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:24:37.545 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:24:37.723 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:24:37.801 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:29:37.819 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:29:37.836 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:29:37.837 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:29:38.016 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:29:38.091 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:34:38.093 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:34:38.102 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:34:38.103 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:34:38.400 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:34:38.527 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:39:38.536 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:39:38.552 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:39:38.553 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:39:38.730 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:39:38.808 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:44:38.828 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:44:38.835 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:44:38.837 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:44:39.136 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:44:39.263 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 06:49:39.271 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 06:49:39.291 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 06:49:39.292 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 06:49:39.591 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 06:49:39.720 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 11:19:34.229 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 11:19:34.237 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 11:19:34.238 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 11:19:34.429 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 11:19:34.508 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 11:24:34.513 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 11:24:34.526 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 11:24:34.527 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 11:24:34.724 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 11:24:34.807 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 11:29:34.825 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 11:29:34.834 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 11:29:34.835 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 11:29:35.030 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 11:29:35.108 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 11:34:35.126 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 11:34:35.135 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 11:34:35.136 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 11:34:35.335 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 11:34:35.424 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 11:39:35.427 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 11:39:35.440 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 11:39:35.441 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 11:39:35.641 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 11:39:35.743 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 11:44:35.760 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 11:44:35.773 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 11:44:35.774 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 11:44:35.968 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 11:44:36.058 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 11:49:36.060 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 11:49:36.063 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 11:49:36.063 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 11:49:36.261 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 11:49:36.347 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 11:54:36.356 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 11:54:36.373 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 11:54:36.373 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 11:54:36.570 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 11:54:36.651 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 11:59:36.654 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 11:59:36.658 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 11:59:36.658 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 11:59:36.875 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 11:59:36.961 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:04:36.969 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:04:36.979 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:04:36.980 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:04:37.187 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:04:37.269 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:09:37.276 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:09:37.284 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:09:37.284 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:09:37.485 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:09:37.565 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:14:37.571 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:14:37.591 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:14:37.593 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:14:37.938 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:14:38.022 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:19:38.034 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:19:38.037 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:19:38.038 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:19:38.251 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:19:38.339 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:24:38.347 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:24:38.434 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:24:38.436 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:24:39.684 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:24:39.795 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:29:39.812 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:29:39.842 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:29:39.843 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:29:40.586 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:29:40.675 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:34:40.681 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:34:40.712 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:34:40.713 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:34:41.433 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:34:41.526 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:39:41.544 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:39:41.570 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:39:41.571 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:39:42.294 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:39:42.379 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:44:42.397 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:44:42.415 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:44:42.415 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:44:43.192 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:44:43.271 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:49:43.289 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:49:43.309 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:49:43.311 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:49:44.076 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:49:44.161 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:54:44.175 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:54:44.193 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:54:44.194 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:54:44.930 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:54:45.014 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 12:59:45.027 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 12:59:45.039 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 12:59:45.040 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 12:59:45.770 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 12:59:45.849 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:04:45.856 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:04:45.888 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:04:45.889 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:04:46.630 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:04:46.715 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:09:46.722 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:09:46.740 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:09:46.740 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:09:47.469 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:09:47.555 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:14:47.559 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:14:47.583 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:14:47.584 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:14:48.385 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:14:48.468 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:19:48.479 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:19:48.492 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:19:48.493 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:19:49.264 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:19:49.355 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:24:49.367 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:24:49.386 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:24:49.387 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:24:50.129 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:24:50.217 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:29:50.234 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:29:50.246 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:29:50.247 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:29:51.028 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:29:51.119 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:34:51.127 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:34:51.161 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:34:51.163 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:34:51.873 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:34:51.955 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:39:51.961 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:39:51.974 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:39:51.975 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:39:52.700 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:39:52.781 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:44:52.786 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:44:52.810 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:44:52.812 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:44:53.502 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:44:53.589 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:49:53.600 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:49:53.626 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:49:53.627 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:49:54.450 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:49:54.540 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:54:54.545 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:54:54.579 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:54:54.579 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:54:55.375 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:54:55.457 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 13:59:55.477 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 13:59:55.500 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 13:59:55.501 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 13:59:56.244 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 13:59:56.334 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 14:04:56.340 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 14:04:56.374 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 14:04:56.375 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 14:04:57.158 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 14:04:57.241 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 14:09:57.248 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 14:09:57.265 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 14:09:57.267 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 14:09:58.062 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 14:09:58.157 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 14:14:58.167 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 14:14:58.195 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 14:14:58.196 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 14:14:58.975 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 14:14:59.053 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 14:19:59.057 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 14:19:59.073 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 14:19:59.074 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 14:19:59.835 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 14:19:59.911 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 14:24:59.915 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 14:24:59.939 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 14:24:59.941 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 14:25:00.702 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 14:25:00.781 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 14:30:00.797 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 14:30:00.818 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 14:30:00.819 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 14:30:01.562 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 14:30:01.639 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 14:35:01.645 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 14:35:01.674 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 14:35:01.676 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 14:35:02.469 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 14:35:02.544 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 14:40:02.556 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 14:40:02.576 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 14:40:02.577 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 14:40:03.460 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 14:40:03.589 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 14:45:03.594 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 14:45:03.624 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 14:45:03.625 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 14:45:04.528 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 14:45:04.611 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 17:34:08.408 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 17:34:08.464 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 17:34:08.948 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 17:34:08.952 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 17:34:08.953 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 17:34:08.954 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 17:34:08.956 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 17:34:09.500 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 17:34:09.501 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 17:34:10.658 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 17:34:10.790 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 17:34:14.309 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 17:34:14.390 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 17:39:10.804 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 17:39:10.829 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 17:39:10.830 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 17:39:11.622 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 17:39:11.714 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 17:44:11.721 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 17:44:11.762 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 17:44:11.763 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 17:44:12.549 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 17:44:12.633 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 17:49:12.645 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 17:49:12.671 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 17:49:12.672 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 17:49:13.469 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 17:49:13.554 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 17:54:13.571 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 17:54:13.592 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 17:54:13.593 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 17:54:19.218 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 17:54:19.304 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 17:59:19.310 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 17:59:19.333 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 17:59:19.334 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 17:59:20.069 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 17:59:20.156 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 18:04:20.177 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:04:20.194 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:04:20.195 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:04:20.937 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 18:04:21.023 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 18:08:21.410 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 18:08:21.461 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:08:21.959 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 18:08:21.963 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 18:08:21.965 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 18:08:21.966 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 18:08:21.967 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-24 18:08:22.468 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:08:22.469 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:08:23.776 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 18:08:23.910 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.SyncAllData() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 142
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 54
2024-10-24 18:08:28.884 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'XECOM_Main_LocalServer.Data.AppDbContext'.
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2024-10-24 18:08:28.964 +03:00 [ERR] Error occurred while fetching products
System.InvalidCastException: Unable to cast object of type 'System.Int32' to type 'System.Decimal'.
   at Microsoft.Data.SqlClient.SqlBuffer.get_Decimal()
   at Microsoft.Data.SqlClient.SqlDataReader.GetDecimal(Int32 i)
   at lambda_method19(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Controllers.DataController.GetProducts() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Controllers\DataController.cs:line 25
2024-10-24 18:11:07.843 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 18:11:07.893 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:11:08.829 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:11:08.831 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:11:10.178 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:11:10.313 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 18:16:10.326 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:16:10.357 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:16:10.358 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:16:11.141 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:16:11.163 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 18:21:11.164 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:21:11.202 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:21:11.203 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:21:11.945 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:21:11.968 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 18:26:11.977 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:26:11.991 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:26:11.992 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:26:16.361 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:26:16.376 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 18:31:16.380 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:31:16.404 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:31:16.405 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:31:17.084 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:31:17.091 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 18:36:17.094 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:36:17.116 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:36:17.117 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:36:17.765 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:36:17.772 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 18:41:17.783 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:41:17.798 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:41:17.799 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:41:18.439 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:41:18.447 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 18:46:18.451 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:46:18.469 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:46:18.469 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:46:19.088 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:46:19.101 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 18:51:19.118 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:51:19.137 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:51:19.138 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:51:19.920 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:51:19.928 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 18:56:19.938 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 18:56:19.967 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 18:56:19.967 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 18:56:20.650 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 18:56:20.657 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:01:20.670 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:01:20.703 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:01:20.704 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:01:21.391 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:01:21.400 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:06:21.409 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:06:21.437 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:06:21.438 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:06:22.114 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:06:22.123 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:11:22.140 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:11:22.156 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:11:22.157 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:11:24.664 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:11:24.671 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:16:24.672 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:16:24.698 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:16:24.699 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:16:25.460 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:16:25.495 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:21:25.496 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:21:25.529 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:21:25.530 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:21:26.280 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:21:26.311 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:26:26.317 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:26:26.341 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:26:26.341 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:26:27.027 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:26:27.048 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:31:27.062 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:31:27.096 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:31:27.097 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:31:27.808 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:31:27.826 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:36:27.842 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:36:27.863 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:36:27.864 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:36:28.646 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:36:28.662 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:41:28.665 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:41:28.691 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:41:28.692 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:41:29.374 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:41:29.400 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:46:33.091 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 19:46:33.155 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:46:34.116 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:46:34.117 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:46:35.303 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:46:35.442 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:51:35.458 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:51:35.530 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:51:35.531 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:51:36.466 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:51:36.508 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 19:56:36.521 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 19:56:36.560 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 19:56:36.561 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 19:56:37.451 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 19:56:37.491 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:01:14.164 +03:00 [INF] Uygulama başlatılıyor
2024-10-24 20:01:14.225 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:01:15.190 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:01:15.191 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:01:16.355 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:01:16.524 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:06:16.535 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:06:16.601 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:06:16.602 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:06:17.447 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:06:17.512 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:11:17.518 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:11:17.558 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:11:17.560 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:11:18.255 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:11:18.288 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:16:18.299 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:16:18.322 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:16:18.323 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:16:19.065 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:16:19.094 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:21:19.104 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:21:19.128 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:21:19.130 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:21:19.825 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:21:19.863 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:26:19.872 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:26:19.896 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:26:19.897 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:26:22.182 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:26:22.208 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:31:22.210 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:31:22.236 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:31:22.238 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:31:22.909 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:31:22.929 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:36:22.943 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:36:22.974 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:36:22.976 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:36:23.644 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:36:23.660 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:41:23.662 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:41:23.684 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:41:23.685 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:41:24.340 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:41:24.372 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:46:24.376 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:46:24.404 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:46:24.404 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:46:25.107 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:46:25.121 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:51:25.128 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:51:25.155 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:51:25.156 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:51:25.859 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:51:25.880 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 20:56:25.893 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 20:56:25.920 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 20:56:25.920 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 20:56:26.578 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 20:56:26.600 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:01:26.610 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:01:26.627 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:01:26.628 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:01:30.387 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:01:30.408 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:06:30.415 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:06:30.435 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:06:30.435 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:06:31.091 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:06:31.112 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:11:31.121 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:11:31.149 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:11:31.150 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:11:31.816 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:11:31.833 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:16:31.841 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:16:31.861 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:16:31.862 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:16:32.540 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:16:32.556 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:21:32.565 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:21:32.588 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:21:32.589 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:21:33.305 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:21:33.321 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:26:33.328 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:26:33.345 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:26:33.346 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:26:34.022 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:26:34.043 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:31:34.047 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:31:34.073 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:31:34.074 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:31:34.775 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:31:34.790 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:36:34.795 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:36:34.814 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:36:34.816 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:36:47.674 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:36:47.699 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:41:47.700 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:41:47.728 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:41:47.729 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:41:48.442 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:41:48.464 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:46:48.470 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:46:48.492 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:46:48.494 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:46:49.203 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:46:49.224 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:51:49.233 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:51:49.261 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:51:49.261 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:51:49.992 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:51:50.008 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 21:56:50.012 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 21:56:50.031 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 21:56:50.032 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 21:56:50.760 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 21:56:50.780 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:01:50.795 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:01:50.823 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:01:50.825 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:01:51.569 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:01:51.598 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:06:51.602 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:06:51.619 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:06:51.619 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:06:52.358 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:06:52.377 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:11:52.381 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:11:52.414 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:11:52.415 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:11:53.138 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:11:53.153 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:16:53.163 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:16:53.184 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:16:53.184 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:16:53.890 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:16:53.905 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:21:53.906 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:21:53.941 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:21:53.942 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:21:54.647 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:21:54.661 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:26:54.662 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:26:54.687 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:26:54.688 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:26:55.397 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:26:55.418 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:31:55.430 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:31:55.468 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:31:55.469 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:31:56.250 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:31:56.280 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:36:56.295 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:36:56.315 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:36:56.316 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:36:58.009 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:36:58.033 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:41:58.048 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:41:58.080 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:41:58.081 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:41:58.856 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:41:58.891 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:46:58.903 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:46:58.923 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:46:58.924 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:46:59.757 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:46:59.780 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:51:59.790 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:51:59.828 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:51:59.829 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:52:00.683 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:52:00.702 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 22:57:00.708 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 22:57:00.737 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 22:57:00.738 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 22:57:01.483 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 22:57:01.507 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:02:01.512 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:02:01.551 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:02:01.551 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:02:02.260 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:02:02.290 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:07:02.292 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:07:02.322 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:07:02.323 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:07:03.022 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:07:03.040 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:12:03.056 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:12:03.089 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:12:03.090 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:12:03.835 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:12:03.861 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:17:03.869 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:17:03.885 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:17:03.886 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:17:04.593 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:17:04.616 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:22:04.622 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:22:04.655 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:22:04.656 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:22:05.348 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:22:05.366 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:27:05.373 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:27:05.398 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:27:05.399 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:27:06.139 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:27:06.164 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:32:06.179 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:32:06.207 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:32:06.208 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:32:06.938 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:32:06.955 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:37:06.957 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:37:06.983 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:37:06.984 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:37:07.691 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:37:07.726 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:42:07.727 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:42:07.757 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:42:07.757 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:42:08.495 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:42:08.514 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:47:08.525 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:47:08.542 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:47:08.543 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:47:09.266 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:47:09.283 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:52:09.287 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:52:09.319 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:52:09.320 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:52:10.053 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:52:10.074 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-10-24 23:57:10.083 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-10-24 23:57:10.108 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-10-24 23:57:10.109 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-10-24 23:57:10.831 +03:00 [INF] Toplam ürün sayısı: 687
2024-10-24 23:57:10.853 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
