2025-06-03 11:48:55.864 +03:00 [INF] Application built successfully.
2025-06-03 11:48:55.907 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 11:48:55.934 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 11:48:56.244 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 11:48:56.245 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 11:48:56.246 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 11:48:56.248 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 11:48:56.250 +03:00 [INF] Pre-run checks complete.
2025-06-03 11:48:56.251 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 11:48:56.414 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 15:52:44.206 +03:00 [INF] Application built successfully.
2025-06-03 15:52:44.240 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 15:52:44.265 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 15:52:44.518 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 15:52:44.520 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 15:52:44.521 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 15:52:44.522 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 15:52:44.524 +03:00 [INF] Pre-run checks complete.
2025-06-03 15:52:44.525 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 15:52:44.683 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 15:53:31.305 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 15:53:31.311 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 15:53:31.321 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 15:53:31.323 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 15:53:31.384 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 15:53:31.395 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 15:53:31.397 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 15:53:31.401 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 15:53:31.403 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 15:53:31.409 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 15:53:31.410 +03:00 [INF] Loaded last cari kod suffix from file: 582
2025-06-03 15:53:31.419 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21298
2025-06-03 15:53:45.933 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 15:53:46.733 +03:00 [DBG] Saved last cari kod suffix to file: 583
2025-06-03 15:53:46.738 +03:00 [INF] Generated new Cari Kod: 120055826583 (suffix: 583, timestamp: 1748955226)
2025-06-03 15:53:47.395 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120055826583, Unvan: Didem Düzenci
2025-06-03 15:53:49.229 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 15:53:50.478 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T13:12:49.4782038Z"
2025-06-03 15:53:50.481 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 15:53:50.499 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120055826583","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120055826583","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 15:53:51.417 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120055826583","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120055826583","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 15:53:51.428 +03:00 [INF] Successfully created Netsis cari. CariKod: 120055826583 (attempt 1)
2025-06-03 15:53:51.433 +03:00 [INF] ✅ Successfully created cari: 120055826583 for customer: Didem Düzenci
2025-06-03 15:53:51.441 +03:00 [INF] 🔥 Getting token via RestSharp...
2025-06-03 15:53:51.467 +03:00 [INF] 🔥 Token request body: grant_type=password&branchcode=203&password=AKAL2013&username=NETSIS&dbname=AKAL2024-04&dbuser=TEMELSET&dbpassword=&dbtype=0
2025-06-03 15:53:52.614 +03:00 [INF] 🔥 Token response - Success: true, StatusCode: "OK"
2025-06-03 15:53:52.628 +03:00 [INF] 🔥 Token response content: {"access_token":"AQAAANCMnd8BFdERjHoAwE_Cl-sBAAAA0LsvjB2MI0ikLlGZr6YFswAAAAACAAAAAAAQZgAAAAEAACAAAADCz49YXlgVqNlfHZoCJQqdkpv6YQrd3fjXRdBleORQBwAAAAAOgAAAAAIAACAAAACS-wwykx8YJFpzgAUdxBVDJIpD4lzAYEksIu5sNwKMOlABAAC6fRZQLicsoXn4BN-CzYMTMrK_04YMjzuPrEVyzLqZKZYHfgU5lr3lWkqsynR_8UIAIXGhaiRra_uBQsaghQOo9fWOXTkVf2ls3NJV2kN_fNCOkLGgDpBNXkuFlYD2mun45hQpgo6xN_mz2JfMYmb8-7_uLMLPLvj0X5Rx72PgyquTyqb9LFHmPdZNCVS3NtEMt_bhLPHAjLp9WErxZ_R_Rf8HFRAynp5zq14O3J2ynhB4wvlNmqmuZaC-hgp1QRUiH2J6SrshZkk9WIzy0A6PQyb9PwwfGGrtzCC8Al8pzY98QRPLARyLmJYwQZRSz-JceLasaXcE_mqVeoFBLTqBpiVjzzsNdxYes9p-5hDOmvSqPXi3dW3zLnAgNPfwvafC3OliABSK8T4eEg31MRbGWHE3aahGjRZyD3MXpfEhF6z2Lj6OY1ZyPkCeft1PL-5AAAAAi_XFN4eWkqu0LQgeECafkeFqxhTdvRZ8mtP-bNRhnxDGbaxHjGgGauQ_lhuwAE48E1008DE6ycO8jvBdF1mmTA","token_type":"bearer","expires_in":1199,"refresh_token":"d52a0ef221cc4ceaa617a5e07e35878e","as:client_id":"","username":"NETSIS","branchcode":"203","dbname":"AKAL2024-04","jlogin":"{\"DbType\":0,\"DbName\":\"AKAL2024-04\",\"DbUser\":\"TEMELSET\",\"DbPassword\":\"\",\"NetsisUser\":\"NETSIS\",\"NetsisPassword\":\"AKAL2013\",\"BranchCode\":203,\"HashCode\":\"\",\"IDMToken\":\"\",\"IsTrusted\":false,\"Key\":\"AKAL2024-04_203_netsıs_0_\"}",".issued":"Tue, 03 Jun 2025 12:53:51 GMT",".expires":"Tue, 03 Jun 2025 13:13:51 GMT"}
2025-06-03 15:53:52.639 +03:00 [INF] ✅ RestSharp token acquired successfully
2025-06-03 15:53:52.643 +03:00 [INF] 🔥 Sending order via RestSharp...
2025-06-03 15:53:52.670 +03:00 [INF] 🔥 Sending order request...
2025-06-03 15:53:53.008 +03:00 [INF] 🔥 Order response - Success: true, StatusCode: "OK"
2025-06-03 15:53:53.011 +03:00 [INF] 🔥 Order response content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-06-03 15:53:53.018 +03:00 [ERR] ❌ RestSharp API Error: 101 - Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-06-03 15:53:53.022 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RESTSHARP_API_ERROR
2025-06-03 15:53:53.024 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: RESTSHARP_API_ERROR
2025-06-03 15:53:53.029 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 15:53:53.031 +03:00 [INF] Loaded last cari kod suffix from file: 583
2025-06-03 15:53:53.032 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 15:53:53.036 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 15:53:53.038 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21297
2025-06-03 15:59:32.251 +03:00 [INF] Application built successfully.
2025-06-03 15:59:32.281 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 15:59:32.303 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 15:59:32.541 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 15:59:32.542 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 15:59:32.544 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 15:59:32.545 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 15:59:32.547 +03:00 [INF] Pre-run checks complete.
2025-06-03 15:59:32.548 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 15:59:32.699 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 16:00:18.502 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 16:00:18.507 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 16:00:18.518 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 16:00:18.520 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 16:00:18.573 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 16:00:18.586 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 16:00:18.588 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 16:00:18.593 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:00:18.595 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 16:00:18.599 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:00:18.602 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21298
2025-06-03 16:00:18.602 +03:00 [INF] Loaded last cari kod suffix from file: 583
2025-06-03 16:01:11.344 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 16:01:11.401 +03:00 [DBG] Saved last cari kod suffix to file: 584
2025-06-03 16:01:11.403 +03:00 [INF] Generated new Cari Kod: 120056271584 (suffix: 584, timestamp: 1748955671)
2025-06-03 16:01:11.407 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120056271584, Unvan: Didem Düzenci
2025-06-03 16:01:11.415 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 16:01:12.671 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T13:20:11.6713968Z"
2025-06-03 16:01:12.674 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:01:12.696 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120056271584","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120056271584","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:01:13.744 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120056271584","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120056271584","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 16:01:13.754 +03:00 [INF] Successfully created Netsis cari. CariKod: 120056271584 (attempt 1)
2025-06-03 16:01:13.760 +03:00 [INF] ✅ Successfully created cari: 120056271584 for customer: Didem Düzenci
2025-06-03 16:01:13.766 +03:00 [INF] 🔥 Getting token via RestSharp...
2025-06-03 16:01:13.789 +03:00 [INF] 🔥 Token request body: grant_type=password&branchcode=203&password=AKAL2013&username=NETSIS&dbname=AKAL2025x05&dbuser=TEMELSET&dbpassword=&dbtype=0
2025-06-03 16:01:14.990 +03:00 [INF] 🔥 Token response - Success: true, StatusCode: "OK"
2025-06-03 16:01:14.993 +03:00 [INF] 🔥 Token response content: {"access_token":"AQAAANCMnd8BFdERjHoAwE_Cl-sBAAAA0LsvjB2MI0ikLlGZr6YFswAAAAACAAAAAAAQZgAAAAEAACAAAADeHB5KQ3UyOMKmpZ6x0ZPzORzbNZXnlYvOjxQDkEgnvAAAAAAOgAAAAAIAACAAAAB6EFkK12gdqiA1D_aRnFKQahl71bBecXH7hjsfF5BRFVABAACslhRZ_3w3TBdHWGaWj2WLZae-Vu_NRPONfjRMlTFl8tWRD_W79a-7k3fOL2f3BPF9F7lFxMiEQgQdQggOjBQ5MKWQhYgtwCG4yCndbp9NAf4AcUBXjdHjnGMG7qOJwaJv_St_lxEpVZyGCPKung2NUFS_6hcAI9cwINwiI_2OYYTy6qnTlEfQYYCzVtMtoaoxAsbbB0T7ajCp44RGMfoBfQJWixqgRBWkWDDo5kzviop4EWkhWnq_73ZynaZ-2ee4DI0PZYQZdyad-xlBCGamE3c6k-Gpocz5gE9Sfln_90Vl7idatFcbZpJTsG04mjIaY24OGVewUHswEE2M6RTkrJotjzPPNrP7_Y2Pm8WiKPbjXJGFnbiaHrklyLRnQxrJlj_QQnIeo2v-GeOD_HY-3TpBFhCYJfFqrsNKn-Vlfp89WOR89iYDp_lnkj6l0nFAAAAAX7yAxmeZCZidHsftj3zDEN02QgWFJzM0mpiCwmAgqMJKeHdZMWHhyL0AsrWbGqtJC6MzDYkUDdLroFNyverfIw","token_type":"bearer","expires_in":1199,"refresh_token":"f8a0f574abda43599c332da1186b3013","as:client_id":"","username":"NETSIS","branchcode":"203","dbname":"AKAL2025x05","jlogin":"{\"DbType\":0,\"DbName\":\"AKAL2025x05\",\"DbUser\":\"TEMELSET\",\"DbPassword\":\"\",\"NetsisUser\":\"NETSIS\",\"NetsisPassword\":\"AKAL2013\",\"BranchCode\":203,\"HashCode\":\"\",\"IDMToken\":\"\",\"IsTrusted\":false,\"Key\":\"AKAL2025x05_203_netsıs_0_\"}",".issued":"Tue, 03 Jun 2025 13:01:13 GMT",".expires":"Tue, 03 Jun 2025 13:21:13 GMT"}
2025-06-03 16:01:15.002 +03:00 [INF] ✅ RestSharp token acquired successfully
2025-06-03 16:01:15.007 +03:00 [INF] 🔥 Sending order via RestSharp...
2025-06-03 16:01:15.032 +03:00 [INF] 🔥 Sending order request...
2025-06-03 16:01:35.592 +03:00 [INF] 🔥 Order response - Success: true, StatusCode: "OK"
2025-06-03 16:01:35.600 +03:00 [INF] 🔥 Order response content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Hata Kodu : 700\r\nDetay : 1 kodlu depo kodu bulunamadı.\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 16:01:35\r\nKernel Version : ********\r\nKernel Address : 000C55E4\r\nObject Address : 07903670\r\nObject Name : NXObj_TFatura07903670\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nDepo kodu tanımsızNetOpenX50.Fatura"}
2025-06-03 16:01:35.610 +03:00 [ERR] ❌ RestSharp API Error: 101 - Hata Kodu : 700
Detay : 1 kodlu depo kodu bulunamadı.

<ErrorHeader>
Error Time : 3.06.2025 16:01:35
Kernel Version : ********
Kernel Address : 000C55E4
Object Address : 07903670
Object Name : NXObj_TFatura07903670
Class Name : TFatura
</ErrorHeader>
<Hata>
Depo kodu tanımsızNetOpenX50.Fatura
2025-06-03 16:01:35.614 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RESTSHARP_API_ERROR
2025-06-03 16:01:35.616 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: RESTSHARP_API_ERROR
2025-06-03 16:01:35.625 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:01:35.628 +03:00 [INF] Loaded last cari kod suffix from file: 584
2025-06-03 16:01:35.628 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 16:01:35.636 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:01:35.638 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21297
2025-06-03 16:15:17.680 +03:00 [INF] Application built successfully.
2025-06-03 16:15:17.715 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 16:15:17.737 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 16:15:18.009 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 16:15:18.010 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 16:15:18.012 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 16:15:18.013 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 16:15:18.016 +03:00 [INF] Pre-run checks complete.
2025-06-03 16:15:18.017 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 16:15:18.204 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 16:16:15.584 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 16:16:15.589 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 16:16:15.599 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 16:16:15.601 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 16:16:15.644 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 16:16:15.657 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 16:16:15.658 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 16:16:15.664 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:16:15.666 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 16:16:15.676 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:16:15.684 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21298
2025-06-03 16:16:15.686 +03:00 [INF] Loaded last cari kod suffix from file: 584
2025-06-03 16:16:19.781 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 16:16:19.793 +03:00 [DBG] Saved last cari kod suffix to file: 585
2025-06-03 16:16:19.796 +03:00 [INF] Generated new Cari Kod: 120057179585 (suffix: 585, timestamp: 1748956579)
2025-06-03 16:16:19.798 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057179585, Unvan: Didem Düzenci
2025-06-03 16:16:19.806 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 16:16:21.019 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T13:35:20.0193966Z"
2025-06-03 16:16:21.023 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:16:21.043 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057179585","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057179585","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:16:21.219 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057179585","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120057179585","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 16:16:21.226 +03:00 [INF] Successfully created Netsis cari. CariKod: 120057179585 (attempt 1)
2025-06-03 16:16:21.230 +03:00 [INF] ✅ Successfully created cari: 120057179585 for customer: Didem Düzenci
2025-06-03 16:16:21.234 +03:00 [INF] 🔥 Getting token via RestSharp...
2025-06-03 16:16:21.255 +03:00 [INF] 🔥 Token request body: grant_type=password&branchcode=203&password=AKAL2013&username=NETSIS&dbname=AKAL2025x05&dbuser=TEMELSET&dbpassword=&dbtype=0
2025-06-03 16:16:22.630 +03:00 [INF] 🔥 Token response - Success: true, StatusCode: "OK"
2025-06-03 16:16:22.632 +03:00 [INF] 🔥 Token response content: {"access_token":"AQAAANCMnd8BFdERjHoAwE_Cl-sBAAAA0LsvjB2MI0ikLlGZr6YFswAAAAACAAAAAAAQZgAAAAEAACAAAACJt86hKzFW1Cm54ZFrmu1A08Dm51vh6ZdGtlvGJbAtHwAAAAAOgAAAAAIAACAAAAAKHsVdVpn7TGxeYECQRbBmMqwLAxuPH7R528PtbQ82TlABAAB0WK5vqqJdGTFVyJpTRM8nElMnO6pLZ5aaZlxj6LDKn6Re2VF4zTa1KvuKHP-yqoCZ77tq5nEc1euQqNhnlbwfKbxMHn02z3yTBE5ZjfIA4oa3N6w4AjPBl1ShUWU4fd7NvVQENqrjME78F_g4wgNglmqcaUeqrT_g2FvgUFX5ka8iCX6XxNVvsid7hqdNWAnykdRxgIbyjOG4PbtooQuuMft8y4H4GaJMd53winEz_wFuwRyzV_riBYw0k5h9INChWNqZwdxjzUOTT8BM59M1dXb9OT-5SSD5EnyQ6ySY_Q0fbQjpaqnpKajNrfK_pZBkEsFh6dl1qZ5llc6bZwvZvuXEvT2XRhMJsk8-JS14xpAa6dy4jhYRsjzBZh9IYyen69SP3x9dws0Iid8g6h8oAMttWCGfj_u7XkNPD2hHA3sjur98u7t3mAZ5kDLRo6FAAAAAz2J1S0lDWvSNBPWv9qI5NRMMYexZEgPH1ECHB7LLJm0hlPrka_T4MgU64r23ODNcBXbtNzXuyW-1G-vrQYnUGA","token_type":"bearer","expires_in":1199,"refresh_token":"362f07d08b724310b7069bca197ece0b","as:client_id":"","username":"NETSIS","branchcode":"203","dbname":"AKAL2025x05","jlogin":"{\"DbType\":0,\"DbName\":\"AKAL2025x05\",\"DbUser\":\"TEMELSET\",\"DbPassword\":\"\",\"NetsisUser\":\"NETSIS\",\"NetsisPassword\":\"AKAL2013\",\"BranchCode\":203,\"HashCode\":\"\",\"IDMToken\":\"\",\"IsTrusted\":false,\"Key\":\"AKAL2025x05_203_netsıs_0_\"}",".issued":"Tue, 03 Jun 2025 13:16:21 GMT",".expires":"Tue, 03 Jun 2025 13:36:21 GMT"}
2025-06-03 16:16:22.639 +03:00 [INF] ✅ RestSharp token acquired successfully
2025-06-03 16:16:22.645 +03:00 [INF] 🔥 Sending order via RestSharp...
2025-06-03 16:16:22.677 +03:00 [INF] 🔥 Sending order request...
2025-06-03 16:16:24.603 +03:00 [INF] 🔥 Order response - Success: true, StatusCode: "OK"
2025-06-03 16:16:24.612 +03:00 [INF] 🔥 Order response content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Hata Kodu : 700\r\nDetay : 1 kodlu depo kodu bulunamadı.\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 16:16:24\r\nKernel Version : ********\r\nKernel Address : 000C4314\r\nObject Address : 07903670\r\nObject Name : NXObj_TFatura07903670\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nDepo kodu tanımsızNetOpenX50.Fatura"}
2025-06-03 16:16:24.618 +03:00 [ERR] ❌ RestSharp API Error: 101 - Hata Kodu : 700
Detay : 1 kodlu depo kodu bulunamadı.

<ErrorHeader>
Error Time : 3.06.2025 16:16:24
Kernel Version : ********
Kernel Address : 000C4314
Object Address : 07903670
Object Name : NXObj_TFatura07903670
Class Name : TFatura
</ErrorHeader>
<Hata>
Depo kodu tanımsızNetOpenX50.Fatura
2025-06-03 16:16:24.624 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RESTSHARP_API_ERROR
2025-06-03 16:16:24.626 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: RESTSHARP_API_ERROR
2025-06-03 16:16:24.632 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:16:24.635 +03:00 [INF] Loaded last cari kod suffix from file: 585
2025-06-03 16:16:24.636 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 16:16:24.642 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:16:24.644 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21297
2025-06-03 16:18:55.144 +03:00 [INF] Application built successfully.
2025-06-03 16:18:55.177 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 16:18:55.195 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 16:18:55.441 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 16:18:55.442 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 16:18:55.444 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 16:18:55.449 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 16:18:55.453 +03:00 [INF] Pre-run checks complete.
2025-06-03 16:18:55.454 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 16:18:55.615 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 16:19:45.434 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 16:19:45.438 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 16:19:45.448 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 16:19:45.450 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 16:19:45.492 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 16:19:45.502 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 16:19:45.505 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 16:19:45.514 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:19:45.519 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 16:19:45.525 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:19:45.527 +03:00 [INF] Loaded last cari kod suffix from file: 585
2025-06-03 16:19:45.531 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21298
2025-06-03 16:19:49.946 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 16:19:49.959 +03:00 [DBG] Saved last cari kod suffix to file: 586
2025-06-03 16:19:49.961 +03:00 [INF] Generated new Cari Kod: 120057389586 (suffix: 586, timestamp: 1748956789)
2025-06-03 16:19:49.964 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057389586, Unvan: Didem Düzenci
2025-06-03 16:19:49.973 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 16:19:51.112 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T13:38:50.1124732Z"
2025-06-03 16:19:51.116 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:19:51.136 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057389586","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057389586","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:19:51.271 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057389586","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120057389586","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 16:19:51.290 +03:00 [INF] Successfully created Netsis cari. CariKod: 120057389586 (attempt 1)
2025-06-03 16:19:51.293 +03:00 [INF] ✅ Successfully created cari: 120057389586 for customer: Didem Düzenci
2025-06-03 16:19:51.299 +03:00 [INF] 🔥 Getting token via RestSharp...
2025-06-03 16:19:51.323 +03:00 [INF] 🔥 Token request body: grant_type=password&branchcode=203&password=AKAL2013&username=NETSIS&dbname=AKAL2025x05&dbuser=TEMELSET&dbpassword=&dbtype=0
2025-06-03 16:19:52.476 +03:00 [INF] 🔥 Token response - Success: true, StatusCode: "OK"
2025-06-03 16:19:52.479 +03:00 [INF] 🔥 Token response content: {"access_token":"AQAAANCMnd8BFdERjHoAwE_Cl-sBAAAA0LsvjB2MI0ikLlGZr6YFswAAAAACAAAAAAAQZgAAAAEAACAAAACeJ-RhCK4Xo9o8vjzWc-4fvXWntO0z0lMmDDmJFUHP9wAAAAAOgAAAAAIAACAAAAAPKOatNzjx5rXMymCJiJYHm6SPG8p6ivaXU0z9Q8sPCVABAADQxLb4Ci6YX2qpIQbKwegW-UakSYevZziLwc8LGxy1Xnmi6nXgL5gyxfaki4EDdvp5LOjminc1VZGAraT5myd0JiStdv_Opl-esm3dnmf75Kd40lvPfg8PDPZfXgrsWmBgLVSiDTwo7uziSx7NlD45fo4IdEviZslB1JI5rm84KQ_SL6u2NabGv8lBehVJcRh-_PN05CiS1BQSnOZ9Y-D5eTYwB0J4YQJFx_7WgnHPRMB05_eoIEnH7dRUN0TLowjZSa1VBegcysKWAk4v6KHcnGnt8PqNH-1Kh4ioKXUmje2jjjZFKoFf6pNYu-p4gvh5Ga-hhIw29wLidyfuXe26Uo02kLgRNpeKDZ_lvN4Co1E-1--PDnjb-jUUhjBHT755n4PkYX2gYrrn8QLJhhqeFMTyz48jSWv5p5U54BjgrED4e5GJKJ_EUds3PWY_4r5AAAAAn0vqgIsUqpJ7BP08CHh-MOGcrax7huGJysE-GRRAh494beEu6ilB4N9-ZcUJeuXnUtF4wJxwrq7jEl4Uvi9LFA","token_type":"bearer","expires_in":1199,"refresh_token":"faa4d78847c9474db3085addcd23e11e","as:client_id":"","username":"NETSIS","branchcode":"203","dbname":"AKAL2025x05","jlogin":"{\"DbType\":0,\"DbName\":\"AKAL2025x05\",\"DbUser\":\"TEMELSET\",\"DbPassword\":\"\",\"NetsisUser\":\"NETSIS\",\"NetsisPassword\":\"AKAL2013\",\"BranchCode\":203,\"HashCode\":\"\",\"IDMToken\":\"\",\"IsTrusted\":false,\"Key\":\"AKAL2025x05_203_netsıs_0_\"}",".issued":"Tue, 03 Jun 2025 13:19:51 GMT",".expires":"Tue, 03 Jun 2025 13:39:51 GMT"}
2025-06-03 16:19:52.491 +03:00 [INF] ✅ RestSharp token acquired successfully
2025-06-03 16:19:52.499 +03:00 [INF] 🔥 Sending order via RestSharp...
2025-06-03 16:19:52.533 +03:00 [INF] 🔥 Sending order request...
2025-06-03 16:19:54.370 +03:00 [INF] 🔥 Order response - Success: true, StatusCode: "OK"
2025-06-03 16:19:54.374 +03:00 [INF] 🔥 Order response content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Hata Kodu : 700\r\nDetay : 1 kodlu depo kodu bulunamadı.\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 16:19:54\r\nKernel Version : ********\r\nKernel Address : 000C6764\r\nObject Address : 07903670\r\nObject Name : NXObj_TFatura07903670\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nDepo kodu tanımsızNetOpenX50.Fatura"}
2025-06-03 16:19:54.380 +03:00 [ERR] ❌ RestSharp API Error: 101 - Hata Kodu : 700
Detay : 1 kodlu depo kodu bulunamadı.

<ErrorHeader>
Error Time : 3.06.2025 16:19:54
Kernel Version : ********
Kernel Address : 000C6764
Object Address : 07903670
Object Name : NXObj_TFatura07903670
Class Name : TFatura
</ErrorHeader>
<Hata>
Depo kodu tanımsızNetOpenX50.Fatura
2025-06-03 16:19:54.385 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RESTSHARP_API_ERROR
2025-06-03 16:19:54.386 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: RESTSHARP_API_ERROR
2025-06-03 16:19:54.390 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:19:54.393 +03:00 [INF] Loaded last cari kod suffix from file: 586
2025-06-03 16:19:54.396 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 16:19:54.400 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:19:54.401 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21297
2025-06-03 16:24:41.349 +03:00 [INF] Application built successfully.
2025-06-03 16:24:41.384 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 16:24:41.403 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 16:24:41.666 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 16:24:41.668 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 16:24:41.669 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 16:24:41.670 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 16:24:41.672 +03:00 [INF] Pre-run checks complete.
2025-06-03 16:24:41.673 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 16:24:41.884 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 16:25:20.313 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 16:25:20.319 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 16:25:20.330 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 16:25:20.332 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 16:25:20.394 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 16:25:20.403 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 16:25:20.407 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 16:25:20.412 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:25:20.415 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 16:25:20.419 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:25:20.422 +03:00 [INF] Loaded last cari kod suffix from file: 586
2025-06-03 16:25:20.423 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21298
2025-06-03 16:25:34.859 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 16:25:34.875 +03:00 [DBG] Saved last cari kod suffix to file: 587
2025-06-03 16:25:34.878 +03:00 [INF] Generated new Cari Kod: 120057734587 (suffix: 587, timestamp: 1748957134)
2025-06-03 16:25:34.881 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057734587, Unvan: Didem Düzenci
2025-06-03 16:25:34.890 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 16:25:36.089 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T13:44:35.0891798Z"
2025-06-03 16:25:36.093 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:25:36.113 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057734587","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057734587","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:26:48.135 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057734587","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120057734587","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 16:26:48.149 +03:00 [INF] Successfully created Netsis cari. CariKod: 120057734587 (attempt 1)
2025-06-03 16:26:48.153 +03:00 [INF] ✅ Successfully created cari: 120057734587 for customer: Didem Düzenci
2025-06-03 16:26:48.160 +03:00 [INF] 🔥 Getting token via RestSharp...
2025-06-03 16:26:48.189 +03:00 [INF] 🔥 Token request body: grant_type=password&branchcode=203&password=AKAL2013&username=NETSIS&dbname=AKAL2025x05&dbuser=TEMELSET&dbpassword=&dbtype=0
2025-06-03 16:26:49.370 +03:00 [INF] 🔥 Token response - Success: true, StatusCode: "OK"
2025-06-03 16:26:49.373 +03:00 [INF] 🔥 Token response content: {"access_token":"AQAAANCMnd8BFdERjHoAwE_Cl-sBAAAA0LsvjB2MI0ikLlGZr6YFswAAAAACAAAAAAAQZgAAAAEAACAAAAACgPr2WuXFphBQgY4XhL1vEv5slgW4AT-zDdTIUQlAuAAAAAAOgAAAAAIAACAAAADeWrJPNDmLxcZvKqKFgE1Wjf3HhFg_9GevVG-R4oA1SVABAAAuB69rKtEi8eqoUt7NgeH_pcDaY1AzRgsbiVCKepRiu-EysPUTn5QmfZy2-6rPyTUz1GZFybj-lCRCyKj1nh4HHRVwojii3iiq-aJudhGWdq8jKJYcOLGV9dhODkCrUI2nmqxL50ASot5xcWAPIb9EMBzlngSrNPKPOjklg9qq404IIQTcO3IJhCjRalQpNgR79BaTrk2FZNam04I4ZS-3kMKh_1REbDZ1sOsNPCEBsuru83zE6F2ZjBgH-4mJF7RnhWXJyaAXBMfZY4tu4RuOC_Lyzsv3D8CuFLPFbQWtj6TGiRU0T2NKNEwq7e7RxtqweiMJn4zKT0ZpJMI_PV9DBcHRGgQPwWS2ub3Fl1Drmnc32lFDerEEsqdMiIase2g5aTGg4BR9lhYxnnGsh7o4uV2OrlkCEAel-CXFbrIOu33YAGatfUINNxYLaWv9hgFAAAAAGBK_Cn3LfnHo_q1cLj3gu5fTzQA3iNr5eT49UbYFz8nhbUSHXysITEQgy7LUwxKHqaLCvLt-7zrymirZ9uwaUw","token_type":"bearer","expires_in":1199,"refresh_token":"96840c4a6dcf448db2d4acbdb5acc10b","as:client_id":"","username":"NETSIS","branchcode":"203","dbname":"AKAL2025x05","jlogin":"{\"DbType\":0,\"DbName\":\"AKAL2025x05\",\"DbUser\":\"TEMELSET\",\"DbPassword\":\"\",\"NetsisUser\":\"NETSIS\",\"NetsisPassword\":\"AKAL2013\",\"BranchCode\":203,\"HashCode\":\"\",\"IDMToken\":\"\",\"IsTrusted\":false,\"Key\":\"AKAL2025x05_203_netsıs_0_\"}",".issued":"Tue, 03 Jun 2025 13:26:48 GMT",".expires":"Tue, 03 Jun 2025 13:46:48 GMT"}
2025-06-03 16:26:49.379 +03:00 [INF] ✅ RestSharp token acquired successfully
2025-06-03 16:26:49.386 +03:00 [INF] 🔥 Sending order via RestSharp...
2025-06-03 16:26:49.416 +03:00 [INF] 🔥 Sending order request...
2025-06-03 16:26:51.249 +03:00 [INF] 🔥 Order response - Success: true, StatusCode: "OK"
2025-06-03 16:26:51.251 +03:00 [INF] 🔥 Order response content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Hata Kodu : 700\r\nDetay : 1 kodlu depo kodu bulunamadı.\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 16:26:51\r\nKernel Version : ********\r\nKernel Address : 000C4314\r\nObject Address : 07903670\r\nObject Name : NXObj_TFatura07903670\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nDepo kodu tanımsızNetOpenX50.Fatura"}
2025-06-03 16:26:51.257 +03:00 [ERR] ❌ RestSharp API Error: 101 - Hata Kodu : 700
Detay : 1 kodlu depo kodu bulunamadı.

<ErrorHeader>
Error Time : 3.06.2025 16:26:51
Kernel Version : ********
Kernel Address : 000C4314
Object Address : 07903670
Object Name : NXObj_TFatura07903670
Class Name : TFatura
</ErrorHeader>
<Hata>
Depo kodu tanımsızNetOpenX50.Fatura
2025-06-03 16:26:51.266 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RESTSHARP_API_ERROR
2025-06-03 16:26:51.268 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: RESTSHARP_API_ERROR
2025-06-03 16:26:51.276 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:26:51.278 +03:00 [INF] Loaded last cari kod suffix from file: 587
2025-06-03 16:26:51.279 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 16:26:51.285 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:26:51.286 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21297
2025-06-03 16:27:13.588 +03:00 [INF] Creating cari with data - Name: şerife arıkan, Email: NULL, Phone: , City: İstanbul, Address: Yunus Emre Mah (AYŞE ÇARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / İSTANBUL
2025-06-03 16:27:13.642 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 16:27:13.650 +03:00 [DBG] Saved last cari kod suffix to file: 588
2025-06-03 16:27:13.650 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 16:27:13.652 +03:00 [INF] Generated new Cari Kod: 120057833588 (suffix: 588, timestamp: 1748957233)
2025-06-03 16:27:13.656 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 16:27:13.658 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057833588, Unvan: şerife arıkan
2025-06-03 16:27:13.660 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 16:27:13.662 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:27:13.666 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 16:27:13.667 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:27:13.669 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 16:27:13.670 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057833588","CARI_ISIM":"\u015Ferife ar\u0131kan","CARI_TIP":"A","ADRES1":"Yunus Emre Mah (AY\u015EE \u00C7ARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / \u0130STANBUL","ADRES2":"","IL":"\u0130stanbul","ILCE":"Sancaktepe","ULKE_KODU":"TR","POSTA_KODU":"34000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057833588","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:27:13.672 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 16:27:21.145 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:27:21.153 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 16:27:21.154 +03:00 [INF] Loaded last cari kod suffix from file: 588
2025-06-03 16:27:21.155 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:27:21.158 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21298
2025-06-03 16:27:23.051 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 16:27:23.052 +03:00 [DBG] TryCreateCariAsync Response - Status: "Unauthorized", Content: {"Message":"Authorization has been denied for this request."} (attempt 1)
2025-06-03 16:27:23.061 +03:00 [DBG] Saved last cari kod suffix to file: 589
2025-06-03 16:27:23.064 +03:00 [ERR] Failed to create Netsis cari (attempt 1). Status: "Unauthorized". ErrorCode: , ErrorDesc: . Full Response: {"Message":"Authorization has been denied for this request."}
2025-06-03 16:27:23.066 +03:00 [INF] Generated new Cari Kod: 120057843589 (suffix: 589, timestamp: 1748957243)
2025-06-03 16:27:23.070 +03:00 [ERR] ❌ Failed to create cari after 1 attempts for customer: şerife arıkan, Email: NULL
2025-06-03 16:27:23.073 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057843589, Unvan: Didem Düzenci
2025-06-03 16:27:23.076 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: ERROR_CARI
2025-06-03 16:27:23.078 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:27:23.080 +03:00 [ERR] Failed to process OrderNumber 21297 (Client ID: 956) in ERP. Result: ERROR_CARI
2025-06-03 16:27:23.081 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:27:23.086 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:27:23.088 +03:00 [INF] Loaded last cari kod suffix from file: 589
2025-06-03 16:27:23.089 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057843589","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057843589","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:27:23.090 +03:00 [INF] Processing OrderNumber: 21296 (Client ID: 955) with ERPService.
2025-06-03 16:27:25.584 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:27:25.587 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21296
2025-06-03 16:27:54.077 +03:00 [INF] Creating cari with data - Name: Mustafa Tacettin Ayrancı, Email: NULL, Phone: , City: Balıkesir, Address: Bahçelievler Mah 5135. Sokak No:13/4 Demir32 Apt.
2025-06-03 16:27:54.109 +03:00 [DBG] TryCreateCariAsync Response - Status: "Unauthorized", Content: {"Message":"Authorization has been denied for this request."} (attempt 1)
2025-06-03 16:27:54.116 +03:00 [DBG] Saved last cari kod suffix to file: 590
2025-06-03 16:27:54.126 +03:00 [ERR] Failed to create Netsis cari (attempt 1). Status: "Unauthorized". ErrorCode: , ErrorDesc: . Full Response: {"Message":"Authorization has been denied for this request."}
2025-06-03 16:27:54.132 +03:00 [INF] Generated new Cari Kod: 120057874590 (suffix: 590, timestamp: 1748957274)
2025-06-03 16:27:54.147 +03:00 [ERR] ❌ Failed to create cari after 1 attempts for customer: Didem Düzenci, Email: NULL
2025-06-03 16:27:54.152 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057874590, Unvan: Mustafa Tacettin Ayrancı
2025-06-03 16:27:54.158 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: ERROR_CARI
2025-06-03 16:27:54.160 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:27:54.162 +03:00 [ERR] Failed to process OrderNumber 21298 (Client ID: 957) in ERP. Result: ERROR_CARI
2025-06-03 16:27:54.163 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:27:54.166 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:27:54.168 +03:00 [INF] Loaded last cari kod suffix from file: 590
2025-06-03 16:27:54.169 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057874590","CARI_ISIM":"Mustafa Tacettin Ayranc\u0131","CARI_TIP":"A","ADRES1":"Bah\u00E7elievler Mah 5135. Sokak No:13/4 Demir32 Apt.","ADRES2":"","IL":"Bal\u0131kesir","ILCE":"Alt\u0131eyl\u00FCl","ULKE_KODU":"TR","POSTA_KODU":"10000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057874590","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:27:54.170 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 16:27:54.181 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:27:54.182 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21297
2025-06-03 16:27:54.184 +03:00 [DBG] TryCreateCariAsync Response - Status: "Unauthorized", Content: {"Message":"Authorization has been denied for this request."} (attempt 1)
2025-06-03 16:27:56.186 +03:00 [INF] Creating cari with data - Name: şerife arıkan, Email: NULL, Phone: , City: İstanbul, Address: Yunus Emre Mah (AYŞE ÇARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / İSTANBUL
2025-06-03 16:27:56.191 +03:00 [ERR] Failed to create Netsis cari (attempt 1). Status: "Unauthorized". ErrorCode: , ErrorDesc: . Full Response: {"Message":"Authorization has been denied for this request."}
2025-06-03 16:27:56.201 +03:00 [DBG] Saved last cari kod suffix to file: 591
2025-06-03 16:27:56.203 +03:00 [ERR] ❌ Failed to create cari after 1 attempts for customer: Mustafa Tacettin Ayrancı, Email: NULL
2025-06-03 16:27:56.207 +03:00 [INF] Generated new Cari Kod: 120057876591 (suffix: 591, timestamp: 1748957276)
2025-06-03 16:27:56.210 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: ERROR_CARI
2025-06-03 16:27:56.212 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057876591, Unvan: şerife arıkan
2025-06-03 16:27:56.213 +03:00 [ERR] Failed to process OrderNumber 21296 (Client ID: 955) in ERP. Result: ERROR_CARI
2025-06-03 16:27:56.215 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:27:56.218 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:27:56.220 +03:00 [INF] Loaded last cari kod suffix from file: 591
2025-06-03 16:27:56.222 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:27:56.223 +03:00 [INF] Processing OrderNumber: 21295 (Client ID: 954) with ERPService.
2025-06-03 16:27:56.226 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057876591","CARI_ISIM":"\u015Ferife ar\u0131kan","CARI_TIP":"A","ADRES1":"Yunus Emre Mah (AY\u015EE \u00C7ARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / \u0130STANBUL","ADRES2":"","IL":"\u0130stanbul","ILCE":"Sancaktepe","ULKE_KODU":"TR","POSTA_KODU":"34000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057876591","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:27:56.229 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:27:56.238 +03:00 [DBG] TryCreateCariAsync Response - Status: "Unauthorized", Content: {"Message":"Authorization has been denied for this request."} (attempt 1)
2025-06-03 16:27:56.239 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21295
2025-06-03 16:27:56.245 +03:00 [ERR] Failed to create Netsis cari (attempt 1). Status: "Unauthorized". ErrorCode: , ErrorDesc: . Full Response: {"Message":"Authorization has been denied for this request."}
2025-06-03 16:27:57.531 +03:00 [INF] Creating cari with data - Name: kadriyle Aytek, Email: NULL, Phone: , City: Muğla, Address: Turgutreis Mah Karatoprak caddesi, Nağme Sitesi, 1 Ada, No: 10 Turgutreis, Bodrum , Muğla
2025-06-03 16:27:57.534 +03:00 [ERR] ❌ Failed to create cari after 1 attempts for customer: şerife arıkan, Email: NULL
2025-06-03 16:27:57.542 +03:00 [DBG] Saved last cari kod suffix to file: 592
2025-06-03 16:27:57.545 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: ERROR_CARI
2025-06-03 16:27:57.547 +03:00 [INF] Generated new Cari Kod: 120057877592 (suffix: 592, timestamp: 1748957277)
2025-06-03 16:27:57.548 +03:00 [ERR] Failed to process OrderNumber 21297 (Client ID: 956) in ERP. Result: ERROR_CARI
2025-06-03 16:27:57.553 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057877592, Unvan: kadriyle Aytek
2025-06-03 16:27:57.556 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:27:57.558 +03:00 [INF] Loaded last cari kod suffix from file: 592
2025-06-03 16:27:57.559 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:27:57.564 +03:00 [INF] Processing OrderNumber: 21296 (Client ID: 955) with ERPService.
2025-06-03 16:27:57.571 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:27:57.574 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:27:57.576 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057877592","CARI_ISIM":"kadriyle Aytek","CARI_TIP":"A","ADRES1":"Turgutreis Mah Karatoprak caddesi, Na\u011Fme Sitesi, 1 Ada, No: 10 Turgutreis, Bodrum , Mu\u011Fla","ADRES2":"","IL":"Mu\u011Fla","ILCE":"Bodrum","ULKE_KODU":"TR","POSTA_KODU":"48000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057877592","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:27:57.578 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21296
2025-06-03 16:27:57.589 +03:00 [DBG] TryCreateCariAsync Response - Status: "Unauthorized", Content: {"Message":"Authorization has been denied for this request."} (attempt 1)
2025-06-03 16:27:58.410 +03:00 [INF] Creating cari with data - Name: Mustafa Tacettin Ayrancı, Email: NULL, Phone: , City: Balıkesir, Address: Bahçelievler Mah 5135. Sokak No:13/4 Demir32 Apt.
2025-06-03 16:27:58.415 +03:00 [ERR] Failed to create Netsis cari (attempt 1). Status: "Unauthorized". ErrorCode: , ErrorDesc: . Full Response: {"Message":"Authorization has been denied for this request."}
2025-06-03 16:27:58.424 +03:00 [DBG] Saved last cari kod suffix to file: 593
2025-06-03 16:27:58.426 +03:00 [ERR] ❌ Failed to create cari after 1 attempts for customer: kadriyle Aytek, Email: NULL
2025-06-03 16:27:58.427 +03:00 [INF] Generated new Cari Kod: 120057878593 (suffix: 593, timestamp: 1748957278)
2025-06-03 16:27:58.432 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: ERROR_CARI
2025-06-03 16:27:58.434 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057878593, Unvan: Mustafa Tacettin Ayrancı
2025-06-03 16:27:58.435 +03:00 [ERR] Failed to process OrderNumber 21295 (Client ID: 954) in ERP. Result: ERROR_CARI
2025-06-03 16:27:58.437 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:27:58.439 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:27:58.441 +03:00 [INF] Loaded last cari kod suffix from file: 593
2025-06-03 16:27:58.442 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:27:58.443 +03:00 [INF] Processing OrderNumber: 21294 (Client ID: 953) with ERPService.
2025-06-03 16:27:58.446 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057878593","CARI_ISIM":"Mustafa Tacettin Ayranc\u0131","CARI_TIP":"A","ADRES1":"Bah\u00E7elievler Mah 5135. Sokak No:13/4 Demir32 Apt.","ADRES2":"","IL":"Bal\u0131kesir","ILCE":"Alt\u0131eyl\u00FCl","ULKE_KODU":"TR","POSTA_KODU":"10000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057878593","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:27:58.448 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:27:58.455 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21294
2025-06-03 16:28:25.554 +03:00 [INF] Application built successfully.
2025-06-03 16:28:25.584 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 16:28:25.605 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 16:28:25.881 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 16:28:25.884 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 16:28:25.885 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 16:28:25.886 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 16:28:25.888 +03:00 [INF] Pre-run checks complete.
2025-06-03 16:28:25.889 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 16:28:26.070 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 16:28:44.163 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 16:28:44.169 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 16:28:44.186 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 16:28:44.188 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 16:28:44.238 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 16:28:44.247 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 16:28:44.250 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 16:28:44.256 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:28:44.260 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 16:28:44.262 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:28:44.266 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21298
2025-06-03 16:28:44.270 +03:00 [INF] Loaded last cari kod suffix from file: 593
2025-06-03 16:28:56.803 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 16:28:56.820 +03:00 [DBG] Saved last cari kod suffix to file: 594
2025-06-03 16:28:56.823 +03:00 [INF] Generated new Cari Kod: 120057936594 (suffix: 594, timestamp: 1748957336)
2025-06-03 16:28:56.826 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120057936594, Unvan: Didem Düzenci
2025-06-03 16:28:56.837 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 16:28:58.162 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T13:47:57.1617292Z"
2025-06-03 16:28:58.165 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:28:58.186 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057936594","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120057936594","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:28:58.347 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120057936594","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120057936594","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 16:28:58.359 +03:00 [INF] Successfully created Netsis cari. CariKod: 120057936594 (attempt 1)
2025-06-03 16:28:58.362 +03:00 [INF] ✅ Successfully created cari: 120057936594 for customer: Didem Düzenci
2025-06-03 16:28:58.367 +03:00 [INF] 🔥 Getting token via RestSharp...
2025-06-03 16:28:58.389 +03:00 [INF] 🔥 Token request body: grant_type=password&branchcode=203&password=AKAL2013&username=NETSIS&dbname=AKAL2025x05&dbuser=TEMELSET&dbpassword=&dbtype=0
2025-06-03 16:28:59.629 +03:00 [INF] 🔥 Token response - Success: true, StatusCode: "OK"
2025-06-03 16:28:59.632 +03:00 [INF] 🔥 Token response content: {"access_token":"AQAAANCMnd8BFdERjHoAwE_Cl-sBAAAA0LsvjB2MI0ikLlGZr6YFswAAAAACAAAAAAAQZgAAAAEAACAAAABYfCTjq864sVDM2Cq3CA7lYpEuGecpFO_UWlBsD_1poAAAAAAOgAAAAAIAACAAAABwq2mq1BJarMjPjNvqmlhs8IzuhHtu14PayfbE0d92blABAACEzsr6Yy-1XJKKQbHtaA4bkYSmIikYPPz5EMQxc_kyrIFyZu8CEIOY5qCjahWPbIgzIybJgVHYQzytpcOiHNxnzVKFRJqA0O261c8yNplWQb2TPD72d-FFDItsbr1Xoq9Uc6xXCtatopN1VVDeXeDB5IAJ6DIqEtWzK0y3jGN4WDCz45b_YYWdEIBvaycW6lhe1C1fiUkTYQy5XQ1KiIUbYwHUrwjKrs7Y03LC-DgUvEgiwWwnhotOmwTmqM90JVQRg70Ev91IvyIuWSOC2qnOzTpY5MkCu0rnyimVlLrU0HDKZmOFlw0mS5OQYS6YMiJ_j89FvUX4XJqZ5eqs4ub1trfWDgkVDN3hD3htyWmmxJUjm8lhk3iL27SpgZar68ndbI5c8bim8J1Ikb-TCIP7xaLJFSlY-YUFOHnMlruoRLXZ_LcQb7RjqC6iHWVpyL1AAAAAScIgaF7iZt5p7PjcXrZukgKt-AyVnipdMPyRSCECpz9z0qVGyzlxcYtiSoMWftFgwK6278uGNc_ZPmzuRAKRrg","token_type":"bearer","expires_in":1199,"refresh_token":"22f9a22f59584a5ca806b5682f655976","as:client_id":"","username":"NETSIS","branchcode":"203","dbname":"AKAL2025x05","jlogin":"{\"DbType\":0,\"DbName\":\"AKAL2025x05\",\"DbUser\":\"TEMELSET\",\"DbPassword\":\"\",\"NetsisUser\":\"NETSIS\",\"NetsisPassword\":\"AKAL2013\",\"BranchCode\":203,\"HashCode\":\"\",\"IDMToken\":\"\",\"IsTrusted\":false,\"Key\":\"AKAL2025x05_203_netsıs_0_\"}",".issued":"Tue, 03 Jun 2025 13:28:58 GMT",".expires":"Tue, 03 Jun 2025 13:48:58 GMT"}
2025-06-03 16:28:59.639 +03:00 [INF] ✅ RestSharp token acquired successfully
2025-06-03 16:28:59.648 +03:00 [INF] 🔥 Sending order via RestSharp...
2025-06-03 16:28:59.677 +03:00 [INF] 🔥 Sending order request...
2025-06-03 16:29:01.631 +03:00 [INF] 🔥 Order response - Success: true, StatusCode: "OK"
2025-06-03 16:29:01.633 +03:00 [INF] 🔥 Order response content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Hata Kodu : 700\r\nDetay : 1 kodlu depo kodu bulunamadı.\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 16:29:01\r\nKernel Version : ********\r\nKernel Address : 000C5964\r\nObject Address : 078E9B20\r\nObject Name : NXObj_TFatura078E9B20\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nDepo kodu tanımsızNetOpenX50.Fatura"}
2025-06-03 16:29:01.639 +03:00 [ERR] ❌ RestSharp API Error: 101 - Hata Kodu : 700
Detay : 1 kodlu depo kodu bulunamadı.

<ErrorHeader>
Error Time : 3.06.2025 16:29:01
Kernel Version : ********
Kernel Address : 000C5964
Object Address : 078E9B20
Object Name : NXObj_TFatura078E9B20
Class Name : TFatura
</ErrorHeader>
<Hata>
Depo kodu tanımsızNetOpenX50.Fatura
2025-06-03 16:29:01.645 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RESTSHARP_API_ERROR
2025-06-03 16:29:01.647 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: RESTSHARP_API_ERROR
2025-06-03 16:29:01.650 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:29:01.652 +03:00 [INF] Loaded last cari kod suffix from file: 594
2025-06-03 16:29:01.655 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 16:29:01.660 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:29:01.662 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21297
2025-06-03 16:30:38.223 +03:00 [INF] Creating cari with data - Name: şerife arıkan, Email: NULL, Phone: , City: İstanbul, Address: Yunus Emre Mah (AYŞE ÇARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / İSTANBUL
2025-06-03 16:30:38.277 +03:00 [DBG] Saved last cari kod suffix to file: 595
2025-06-03 16:30:38.290 +03:00 [INF] Generated new Cari Kod: 120058038595 (suffix: 595, timestamp: 1748957438)
2025-06-03 16:30:38.300 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120058038595, Unvan: şerife arıkan
2025-06-03 16:30:38.306 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:30:38.309 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:30:38.313 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120058038595","CARI_ISIM":"\u015Ferife ar\u0131kan","CARI_TIP":"A","ADRES1":"Yunus Emre Mah (AY\u015EE \u00C7ARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / \u0130STANBUL","ADRES2":"","IL":"\u0130stanbul","ILCE":"Sancaktepe","ULKE_KODU":"TR","POSTA_KODU":"34000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120058038595","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:30:38.324 +03:00 [DBG] TryCreateCariAsync Response - Status: "Unauthorized", Content: {"Message":"Authorization has been denied for this request."} (attempt 1)
2025-06-03 16:30:38.327 +03:00 [ERR] Failed to create Netsis cari (attempt 1). Status: "Unauthorized". ErrorCode: , ErrorDesc: . Full Response: {"Message":"Authorization has been denied for this request."}
2025-06-03 16:30:38.332 +03:00 [ERR] ❌ Failed to create cari after 1 attempts for customer: şerife arıkan, Email: NULL
2025-06-03 16:30:38.334 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: ERROR_CARI
2025-06-03 16:30:38.335 +03:00 [ERR] Failed to process OrderNumber 21297 (Client ID: 956) in ERP. Result: ERROR_CARI
2025-06-03 16:30:38.339 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:30:38.341 +03:00 [INF] Loaded last cari kod suffix from file: 595
2025-06-03 16:30:38.342 +03:00 [INF] Processing OrderNumber: 21296 (Client ID: 955) with ERPService.
2025-06-03 16:30:38.347 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:30:40.511 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21296
2025-06-03 16:30:44.551 +03:00 [INF] Creating cari with data - Name: Mustafa Tacettin Ayrancı, Email: NULL, Phone: , City: Balıkesir, Address: Bahçelievler Mah 5135. Sokak No:13/4 Demir32 Apt.
2025-06-03 16:30:45.653 +03:00 [DBG] Saved last cari kod suffix to file: 596
2025-06-03 16:30:45.683 +03:00 [INF] Generated new Cari Kod: 120058045596 (suffix: 596, timestamp: 1748957445)
2025-06-03 16:30:46.156 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120058045596, Unvan: Mustafa Tacettin Ayrancı
2025-06-03 16:30:47.165 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:30:47.167 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:30:47.171 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120058045596","CARI_ISIM":"Mustafa Tacettin Ayranc\u0131","CARI_TIP":"A","ADRES1":"Bah\u00E7elievler Mah 5135. Sokak No:13/4 Demir32 Apt.","ADRES2":"","IL":"Bal\u0131kesir","ILCE":"Alt\u0131eyl\u00FCl","ULKE_KODU":"TR","POSTA_KODU":"10000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120058045596","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:30:47.183 +03:00 [DBG] TryCreateCariAsync Response - Status: "Unauthorized", Content: {"Message":"Authorization has been denied for this request."} (attempt 1)
2025-06-03 16:30:47.202 +03:00 [ERR] Failed to create Netsis cari (attempt 1). Status: "Unauthorized". ErrorCode: , ErrorDesc: . Full Response: {"Message":"Authorization has been denied for this request."}
2025-06-03 16:30:48.173 +03:00 [ERR] ❌ Failed to create cari after 1 attempts for customer: Mustafa Tacettin Ayrancı, Email: NULL
2025-06-03 16:31:11.128 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: ERROR_CARI
2025-06-03 16:31:13.636 +03:00 [ERR] Failed to process OrderNumber 21296 (Client ID: 955) in ERP. Result: ERROR_CARI
2025-06-03 16:31:27.367 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 16:31:27.387 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:31:27.387 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 16:31:27.389 +03:00 [INF] Loaded last cari kod suffix from file: 596
2025-06-03 16:31:27.411 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 16:31:27.731 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 16:31:27.756 +03:00 [INF] Processing OrderNumber: 21295 (Client ID: 954) with ERPService.
2025-06-03 16:31:27.758 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 16:31:27.897 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 16:31:27.898 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:31:27.900 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 16:31:28.264 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:31:28.266 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 16:31:28.266 +03:00 [INF] Loaded last cari kod suffix from file: 596
2025-06-03 16:31:28.288 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:31:28.288 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21295
2025-06-03 16:31:29.374 +03:00 [INF] 🔥 Creating Netsis order via RestSharp for OrderNumber: 21298
2025-06-03 16:31:45.110 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 16:31:45.153 +03:00 [DBG] Saved last cari kod suffix to file: 597
2025-06-03 16:31:45.156 +03:00 [INF] Creating cari with data - Name: kadriyle Aytek, Email: NULL, Phone: , City: Muğla, Address: Turgutreis Mah Karatoprak caddesi, Nağme Sitesi, 1 Ada, No: 10 Turgutreis, Bodrum , Muğla
2025-06-03 16:32:16.209 +03:00 [INF] Application built successfully.
2025-06-03 16:32:16.246 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 16:32:16.362 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 16:32:16.612 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 16:32:16.614 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 16:32:16.615 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 16:32:16.616 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 16:32:16.618 +03:00 [INF] Pre-run checks complete.
2025-06-03 16:32:16.619 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 16:32:16.783 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 16:32:49.090 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 16:32:49.095 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 16:32:49.110 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 16:32:49.113 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 16:32:49.174 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 16:32:49.190 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 16:32:49.191 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 16:32:49.196 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:32:49.198 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 16:32:49.203 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:32:49.206 +03:00 [INF] Loaded last cari kod suffix from file: 597
2025-06-03 16:33:03.187 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21298
2025-06-03 16:33:04.244 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21298 for Netsis integration.
2025-06-03 16:33:15.282 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 16:33:19.490 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 16:33:22.611 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 16:33:23.528 +03:00 [DBG] Saved last cari kod suffix to file: 598
2025-06-03 16:33:23.549 +03:00 [INF] Generated new Cari Kod: 120058203598 (suffix: 598, timestamp: 1748957603)
2025-06-03 16:33:24.132 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120058203598, Unvan: Didem Düzenci
2025-06-03 16:33:26.396 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 16:33:27.589 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T13:52:26.5895090Z"
2025-06-03 16:33:27.595 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:33:27.619 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120058203598","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120058203598","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:33:27.763 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120058203598","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120058203598","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 16:33:27.772 +03:00 [INF] Successfully created Netsis cari. CariKod: 120058203598 (attempt 1)
2025-06-03 16:33:27.777 +03:00 [INF] ✅ Successfully created cari: 120058203598 for customer: Didem Düzenci
2025-06-03 16:33:27.779 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120058203598
2025-06-03 16:33:27.780 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21298 with CariKod: 120058203598
2025-06-03 16:33:27.784 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 16:33:27.786 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:33:27.787 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:33:32.808 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 16:34:10.457 +03:00 [DBG] 🎯 API EXAMPLE JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120058203598","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1","ProjeKodu":"b"}]}
2025-06-03 16:34:10.492 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 16:34:10.513 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 16:34:10.530 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 16:34:10.534 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 16:34:10.536 +03:00 [INF] 🔍 Content-Length: 504
2025-06-03 16:34:10.540 +03:00 [INF] 🔍 JSON Payload Length: 504
2025-06-03 16:34:10.542 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 16:34:10.545 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 16:34:10.547 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120058203598","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1","ProjeKodu":"b"}]}
2025-06-03 16:34:10.552 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 16:34:10.554 +03:00 [INF] 🔍 Content length check: 504 chars
2025-06-03 16:34:10.555 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 16:34:10.557 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 16:34:10.561 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120058203598","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1","ProjeKodu":"b"}]}
2025-06-03 16:34:28.842 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 16:34:28.855 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 16:34:28.857 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 16:34:28.859 +03:00 [INF] 🔍 Response Content-Length: 1059
2025-06-03 16:34:28.862 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 16:34:28.864 +03:00 [INF] 🔍 Response Body Length: 1044
2025-06-03 16:34:28.866 +03:00 [INF] 🔍 Response Body: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Hata Kodu : 700\r\nDetay : Kayıt Yeni işleminde hata oluştu. Hata detayı: Hata Kodu : 700\r\nDetay : (Info)Kalem listesinde kayıt var.\r\n(Info)Yedek kalem listesi kayıt için hazır.\r\n(Info)Üst bilgiler düzeltildi.\r\n(Info)Satır açıklamaları silindi.\r\n(Ikaz) Hata:\r\nSQL : \r\nBEGIN\r\nDECLARE @ITEMINCKEYNO INT\r\nINSERT INTO SIPATRA (STOK_KODU,FISNO,STHAR_GCMIK,STHAR_GCKOD,STHAR_TARIH,STHAR_NF,STHAR_BF,STHAR_KDV,STHAR_ACIKLAMA,STHAR_FTIRSIP,LISTE_FIAT,STHAR_HTU\r\n\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 16:34:13\r\nKernel Version : ********\r\nKernel Address : 000C5964\r\nObject Address : 078E9B20\r\nObject Name : NXObj_TFatura078E9B20\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nFatura - Hata\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 16:34:13\r\nKernel Version : ********\r\nKernel Address : 000C5964\r\nObject Address : 078E9B20\r\nObject Name : NXObj_TFatura078E9B20\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nKayıtYeniNetOpenX50.Fatura"}
2025-06-03 16:34:28.879 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: false, ErrorCode: 101, ErrorDesc: Hata Kodu : 700
Detay : Kayıt Yeni işleminde hata oluştu. Hata detayı: Hata Kodu : 700
Detay : (Info)Kalem listesinde kayıt var.
(Info)Yedek kalem listesi kayıt için hazır.
(Info)Üst bilgiler düzeltildi.
(Info)Satır açıklamaları silindi.
(Ikaz) Hata:
SQL : 
BEGIN
DECLARE @ITEMINCKEYNO INT
INSERT INTO SIPATRA (STOK_KODU,FISNO,STHAR_GCMIK,STHAR_GCKOD,STHAR_TARIH,STHAR_NF,STHAR_BF,STHAR_KDV,STHAR_ACIKLAMA,STHAR_FTIRSIP,LISTE_FIAT,STHAR_HTU


<ErrorHeader>
Error Time : 3.06.2025 16:34:13
Kernel Version : ********
Kernel Address : 000C5964
Object Address : 078E9B20
Object Name : NXObj_TFatura078E9B20
Class Name : TFatura
</ErrorHeader>
<Hata>
Fatura - Hata

<ErrorHeader>
Error Time : 3.06.2025 16:34:13
Kernel Version : ********
Kernel Address : 000C5964
Object Address : 078E9B20
Object Name : NXObj_TFatura078E9B20
Class Name : TFatura
</ErrorHeader>
<Hata>
KayıtYeniNetOpenX50.Fatura
2025-06-03 16:34:28.888 +03:00 [ERR] ❌ API EXAMPLE API Error: 101 - Hata Kodu : 700
Detay : Kayıt Yeni işleminde hata oluştu. Hata detayı: Hata Kodu : 700
Detay : (Info)Kalem listesinde kayıt var.
(Info)Yedek kalem listesi kayıt için hazır.
(Info)Üst bilgiler düzeltildi.
(Info)Satır açıklamaları silindi.
(Ikaz) Hata:
SQL : 
BEGIN
DECLARE @ITEMINCKEYNO INT
INSERT INTO SIPATRA (STOK_KODU,FISNO,STHAR_GCMIK,STHAR_GCKOD,STHAR_TARIH,STHAR_NF,STHAR_BF,STHAR_KDV,STHAR_ACIKLAMA,STHAR_FTIRSIP,LISTE_FIAT,STHAR_HTU


<ErrorHeader>
Error Time : 3.06.2025 16:34:13
Kernel Version : ********
Kernel Address : 000C5964
Object Address : 078E9B20
Object Name : NXObj_TFatura078E9B20
Class Name : TFatura
</ErrorHeader>
<Hata>
Fatura - Hata

<ErrorHeader>
Error Time : 3.06.2025 16:34:13
Kernel Version : ********
Kernel Address : 000C5964
Object Address : 078E9B20
Object Name : NXObj_TFatura078E9B20
Class Name : TFatura
</ErrorHeader>
<Hata>
KayıtYeniNetOpenX50.Fatura
2025-06-03 16:34:28.899 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: API_EXAMPLE_API_ERROR
2025-06-03 16:34:28.901 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: API_EXAMPLE_API_ERROR
2025-06-03 16:34:28.905 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:34:28.908 +03:00 [INF] Loaded last cari kod suffix from file: 598
2025-06-03 16:34:28.910 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 16:34:28.914 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:34:30.221 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21297
2025-06-03 16:34:30.224 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21297 for Netsis integration.
2025-06-03 16:34:30.228 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 16:34:30.230 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 16:37:05.621 +03:00 [INF] Application built successfully.
2025-06-03 16:37:05.665 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 16:37:05.685 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 16:37:05.952 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 16:37:05.954 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 16:37:05.955 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 16:37:05.957 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 16:37:05.959 +03:00 [INF] Pre-run checks complete.
2025-06-03 16:37:05.960 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 16:37:06.198 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 16:38:00.577 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 16:38:00.582 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 16:38:00.595 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 16:38:00.598 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 16:38:00.654 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 16:38:00.672 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 16:38:00.674 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 16:38:00.681 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:38:00.683 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 16:38:00.688 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:38:00.690 +03:00 [INF] Loaded last cari kod suffix from file: 598
2025-06-03 16:38:00.704 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21298
2025-06-03 16:38:00.705 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21298 for Netsis integration.
2025-06-03 16:38:00.707 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 16:38:00.709 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 16:38:08.239 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 16:38:08.251 +03:00 [DBG] Saved last cari kod suffix to file: 599
2025-06-03 16:38:08.253 +03:00 [INF] Generated new Cari Kod: 120058488599 (suffix: 599, timestamp: 1748957888)
2025-06-03 16:38:08.256 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120058488599, Unvan: Didem Düzenci
2025-06-03 16:38:08.263 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 16:38:09.401 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T13:57:08.4009130Z"
2025-06-03 16:38:09.404 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:38:09.427 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120058488599","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120058488599","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 16:38:09.574 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120058488599","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120058488599","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 16:38:09.586 +03:00 [INF] Successfully created Netsis cari. CariKod: 120058488599 (attempt 1)
2025-06-03 16:38:09.589 +03:00 [INF] ✅ Successfully created cari: 120058488599 for customer: Didem Düzenci
2025-06-03 16:38:09.591 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120058488599
2025-06-03 16:38:09.593 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21298 with CariKod: 120058488599
2025-06-03 16:38:09.598 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 16:38:09.600 +03:00 [DBG] Using cached Netsis token.
2025-06-03 16:38:09.602 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 16:38:09.606 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 16:38:54.548 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120058488599","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1","ProjeKodu":"b"}]}
2025-06-03 16:38:54.561 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 16:38:54.576 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 16:38:54.605 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 16:38:54.623 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 16:38:54.627 +03:00 [INF] 🔍 Content-Length: 504
2025-06-03 16:38:54.631 +03:00 [INF] 🔍 JSON Payload Length: 504
2025-06-03 16:38:54.633 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 16:38:54.636 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 16:38:54.637 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120058488599","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1","ProjeKodu":"b"}]}
2025-06-03 16:38:54.642 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 16:38:54.644 +03:00 [INF] 🔍 Content length check: 504 chars
2025-06-03 16:38:54.645 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 16:38:54.646 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 16:38:54.648 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120058488599","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1","ProjeKodu":"b"}]}
2025-06-03 16:39:01.912 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 16:39:01.916 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 16:39:01.918 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 16:39:01.921 +03:00 [INF] 🔍 Response Content-Length: 1059
2025-06-03 16:39:01.925 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 16:39:01.927 +03:00 [INF] 🔍 Response Body Length: 1044
2025-06-03 16:39:01.928 +03:00 [INF] 🔍 Response Body: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Hata Kodu : 700\r\nDetay : Kayıt Yeni işleminde hata oluştu. Hata detayı: Hata Kodu : 700\r\nDetay : (Info)Kalem listesinde kayıt var.\r\n(Info)Yedek kalem listesi kayıt için hazır.\r\n(Info)Üst bilgiler düzeltildi.\r\n(Info)Satır açıklamaları silindi.\r\n(Ikaz) Hata:\r\nSQL : \r\nBEGIN\r\nDECLARE @ITEMINCKEYNO INT\r\nINSERT INTO SIPATRA (STOK_KODU,FISNO,STHAR_GCMIK,STHAR_GCKOD,STHAR_TARIH,STHAR_NF,STHAR_BF,STHAR_KDV,STHAR_ACIKLAMA,STHAR_FTIRSIP,LISTE_FIAT,STHAR_HTU\r\n\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 16:38:56\r\nKernel Version : ********\r\nKernel Address : 000C6684\r\nObject Address : 078E9B20\r\nObject Name : NXObj_TFatura078E9B20\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nFatura - Hata\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 16:38:57\r\nKernel Version : ********\r\nKernel Address : 000C6684\r\nObject Address : 078E9B20\r\nObject Name : NXObj_TFatura078E9B20\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nKayıtYeniNetOpenX50.Fatura"}
2025-06-03 16:39:01.940 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: false, ErrorCode: 101, ErrorDesc: Hata Kodu : 700
Detay : Kayıt Yeni işleminde hata oluştu. Hata detayı: Hata Kodu : 700
Detay : (Info)Kalem listesinde kayıt var.
(Info)Yedek kalem listesi kayıt için hazır.
(Info)Üst bilgiler düzeltildi.
(Info)Satır açıklamaları silindi.
(Ikaz) Hata:
SQL : 
BEGIN
DECLARE @ITEMINCKEYNO INT
INSERT INTO SIPATRA (STOK_KODU,FISNO,STHAR_GCMIK,STHAR_GCKOD,STHAR_TARIH,STHAR_NF,STHAR_BF,STHAR_KDV,STHAR_ACIKLAMA,STHAR_FTIRSIP,LISTE_FIAT,STHAR_HTU


<ErrorHeader>
Error Time : 3.06.2025 16:38:56
Kernel Version : ********
Kernel Address : 000C6684
Object Address : 078E9B20
Object Name : NXObj_TFatura078E9B20
Class Name : TFatura
</ErrorHeader>
<Hata>
Fatura - Hata

<ErrorHeader>
Error Time : 3.06.2025 16:38:57
Kernel Version : ********
Kernel Address : 000C6684
Object Address : 078E9B20
Object Name : NXObj_TFatura078E9B20
Class Name : TFatura
</ErrorHeader>
<Hata>
KayıtYeniNetOpenX50.Fatura
2025-06-03 16:39:01.946 +03:00 [ERR] ❌ API EXAMPLE API Error: 101 - Hata Kodu : 700
Detay : Kayıt Yeni işleminde hata oluştu. Hata detayı: Hata Kodu : 700
Detay : (Info)Kalem listesinde kayıt var.
(Info)Yedek kalem listesi kayıt için hazır.
(Info)Üst bilgiler düzeltildi.
(Info)Satır açıklamaları silindi.
(Ikaz) Hata:
SQL : 
BEGIN
DECLARE @ITEMINCKEYNO INT
INSERT INTO SIPATRA (STOK_KODU,FISNO,STHAR_GCMIK,STHAR_GCKOD,STHAR_TARIH,STHAR_NF,STHAR_BF,STHAR_KDV,STHAR_ACIKLAMA,STHAR_FTIRSIP,LISTE_FIAT,STHAR_HTU


<ErrorHeader>
Error Time : 3.06.2025 16:38:56
Kernel Version : ********
Kernel Address : 000C6684
Object Address : 078E9B20
Object Name : NXObj_TFatura078E9B20
Class Name : TFatura
</ErrorHeader>
<Hata>
Fatura - Hata

<ErrorHeader>
Error Time : 3.06.2025 16:38:57
Kernel Version : ********
Kernel Address : 000C6684
Object Address : 078E9B20
Object Name : NXObj_TFatura078E9B20
Class Name : TFatura
</ErrorHeader>
<Hata>
KayıtYeniNetOpenX50.Fatura
2025-06-03 16:39:01.964 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: API_EXAMPLE_API_ERROR
2025-06-03 16:39:01.966 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: API_EXAMPLE_API_ERROR
2025-06-03 16:39:01.970 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 16:39:01.974 +03:00 [INF] Loaded last cari kod suffix from file: 599
2025-06-03 16:39:01.975 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 16:39:01.981 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 16:39:01.986 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21297
2025-06-03 16:39:01.988 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21297 for Netsis integration.
2025-06-03 16:39:01.990 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 16:39:01.992 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:05:37.164 +03:00 [INF] Application built successfully.
2025-06-03 17:05:37.192 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 17:05:37.211 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 17:05:37.450 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 17:05:37.451 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 17:05:37.452 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 17:05:37.453 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 17:05:37.456 +03:00 [INF] Pre-run checks complete.
2025-06-03 17:05:37.457 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 17:05:37.594 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 17:10:45.515 +03:00 [INF] Application built successfully.
2025-06-03 17:10:45.550 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 17:10:45.570 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 17:11:06.771 +03:00 [INF] Application built successfully.
2025-06-03 17:11:06.801 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 17:11:06.818 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 17:11:07.058 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 17:11:07.060 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 17:11:07.062 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 17:11:07.064 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 17:11:07.066 +03:00 [INF] Pre-run checks complete.
2025-06-03 17:11:07.067 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 17:11:07.234 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 17:14:12.837 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 17:14:12.840 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 17:14:12.848 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 17:14:12.850 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 17:14:12.885 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 17:14:12.893 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 17:14:12.896 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 17:14:12.899 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:14:12.901 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 17:14:12.904 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:14:12.909 +03:00 [INF] Loaded last cari kod suffix from file: 599
2025-06-03 17:14:12.922 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21298
2025-06-03 17:14:12.925 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21298 for Netsis integration.
2025-06-03 17:14:12.928 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:14:12.930 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:14:12.933 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 17:14:12.942 +03:00 [DBG] Saved last cari kod suffix to file: 600
2025-06-03 17:14:12.946 +03:00 [INF] Generated new Cari Kod: 120060652600 (suffix: 600, timestamp: 1748960052)
2025-06-03 17:14:12.949 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120060652600, Unvan: Didem Düzenci
2025-06-03 17:14:12.957 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 17:14:14.008 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T14:33:13.0076138Z"
2025-06-03 17:14:14.011 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:14:14.027 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120060652600","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120060652600","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:14:14.305 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120060652600","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120060652600","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:14:14.316 +03:00 [INF] Successfully created Netsis cari. CariKod: 120060652600 (attempt 1)
2025-06-03 17:14:14.319 +03:00 [INF] ✅ Successfully created cari: 120060652600 for customer: Didem Düzenci
2025-06-03 17:14:14.321 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120060652600
2025-06-03 17:14:14.323 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21298 with CariKod: 120060652600
2025-06-03 17:14:14.328 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:14:14.329 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:14:14.334 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:14:14.336 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:14:27.294 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1"}]}
2025-06-03 17:14:27.300 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 17:14:27.302 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 17:14:27.305 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 17:14:27.309 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 17:14:27.311 +03:00 [INF] 🔍 Content-Length: 487
2025-06-03 17:14:27.313 +03:00 [INF] 🔍 JSON Payload Length: 487
2025-06-03 17:14:27.316 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 17:14:27.319 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 17:14:27.321 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1"}]}
2025-06-03 17:14:27.327 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 17:14:27.329 +03:00 [INF] 🔍 Content length check: 487 chars
2025-06-03 17:14:27.333 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 17:14:27.335 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 17:14:27.339 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1"}]}
2025-06-03 17:14:47.987 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 17:14:47.995 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 17:14:47.998 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 17:14:48.002 +03:00 [INF] 🔍 Response Content-Length: 901
2025-06-03 17:14:48.004 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 17:14:48.006 +03:00 [INF] 🔍 Response Body Length: 884
2025-06-03 17:14:48.007 +03:00 [INF] 🔍 Response Body: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Hata Kodu : 700\r\nDetay : Kayıt Yeni işleminde hata oluştu. Hata detayı: Hata Kodu : 700\r\nDetay : (Info)Kalem listesinde kayıt var.\r\n(Info)Yedek kalem listesi kayıt için hazır.\r\n(Info)Üst bilgiler düzeltildi.\r\n(Info)Satır açıklamaları silindi.\r\n(Ikaz)Lokal Depo Uygulaması: Kalem Depo Kodu Geçersiz!\r\n\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 17:14:47\r\nKernel Version : ********\r\nKernel Address : 000C6374\r\nObject Address : 07903670\r\nObject Name : NXObj_TFatura07903670\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nFatura - Hata\r\n\r\n<ErrorHeader>\r\nError Time : 3.06.2025 17:14:47\r\nKernel Version : ********\r\nKernel Address : 000C6374\r\nObject Address : 07903670\r\nObject Name : NXObj_TFatura07903670\r\nClass Name : TFatura\r\n</ErrorHeader>\r\n<Hata>\r\nKayıtYeniNetOpenX50.Fatura"}
2025-06-03 17:14:48.020 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: false, ErrorCode: 101, ErrorDesc: Hata Kodu : 700
Detay : Kayıt Yeni işleminde hata oluştu. Hata detayı: Hata Kodu : 700
Detay : (Info)Kalem listesinde kayıt var.
(Info)Yedek kalem listesi kayıt için hazır.
(Info)Üst bilgiler düzeltildi.
(Info)Satır açıklamaları silindi.
(Ikaz)Lokal Depo Uygulaması: Kalem Depo Kodu Geçersiz!


<ErrorHeader>
Error Time : 3.06.2025 17:14:47
Kernel Version : ********
Kernel Address : 000C6374
Object Address : 07903670
Object Name : NXObj_TFatura07903670
Class Name : TFatura
</ErrorHeader>
<Hata>
Fatura - Hata

<ErrorHeader>
Error Time : 3.06.2025 17:14:47
Kernel Version : ********
Kernel Address : 000C6374
Object Address : 07903670
Object Name : NXObj_TFatura07903670
Class Name : TFatura
</ErrorHeader>
<Hata>
KayıtYeniNetOpenX50.Fatura
2025-06-03 17:14:48.027 +03:00 [ERR] ❌ API EXAMPLE API Error: 101 - Hata Kodu : 700
Detay : Kayıt Yeni işleminde hata oluştu. Hata detayı: Hata Kodu : 700
Detay : (Info)Kalem listesinde kayıt var.
(Info)Yedek kalem listesi kayıt için hazır.
(Info)Üst bilgiler düzeltildi.
(Info)Satır açıklamaları silindi.
(Ikaz)Lokal Depo Uygulaması: Kalem Depo Kodu Geçersiz!


<ErrorHeader>
Error Time : 3.06.2025 17:14:47
Kernel Version : ********
Kernel Address : 000C6374
Object Address : 07903670
Object Name : NXObj_TFatura07903670
Class Name : TFatura
</ErrorHeader>
<Hata>
Fatura - Hata

<ErrorHeader>
Error Time : 3.06.2025 17:14:47
Kernel Version : ********
Kernel Address : 000C6374
Object Address : 07903670
Object Name : NXObj_TFatura07903670
Class Name : TFatura
</ErrorHeader>
<Hata>
KayıtYeniNetOpenX50.Fatura
2025-06-03 17:14:48.039 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: API_EXAMPLE_API_ERROR
2025-06-03 17:14:48.041 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: API_EXAMPLE_API_ERROR
2025-06-03 17:14:48.045 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:14:48.060 +03:00 [INF] Loaded last cari kod suffix from file: 600
2025-06-03 17:14:48.060 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 17:14:48.064 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:14:48.066 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21297
2025-06-03 17:14:48.070 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21297 for Netsis integration.
2025-06-03 17:14:48.071 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:14:48.073 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:14:48.074 +03:00 [INF] Creating cari with data - Name: şerife arıkan, Email: NULL, Phone: , City: İstanbul, Address: Yunus Emre Mah (AYŞE ÇARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / İSTANBUL
2025-06-03 17:14:48.084 +03:00 [DBG] Saved last cari kod suffix to file: 601
2025-06-03 17:14:48.086 +03:00 [INF] Generated new Cari Kod: 120060688601 (suffix: 601, timestamp: 1748960088)
2025-06-03 17:14:48.089 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120060688601, Unvan: şerife arıkan
2025-06-03 17:14:48.092 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:14:48.094 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:14:48.096 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120060688601","CARI_ISIM":"\u015Ferife ar\u0131kan","CARI_TIP":"A","ADRES1":"Yunus Emre Mah (AY\u015EE \u00C7ARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / \u0130STANBUL","ADRES2":"","IL":"\u0130stanbul","ILCE":"Sancaktepe","ULKE_KODU":"TR","POSTA_KODU":"34000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120060688601","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:14:48.213 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120060688601","ULKE_KODU":"TR","CARI_ISIM":"şerife arıkan","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120060688601","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:14:48.221 +03:00 [INF] Successfully created Netsis cari. CariKod: 120060688601 (attempt 1)
2025-06-03 17:14:48.226 +03:00 [INF] ✅ Successfully created cari: 120060688601 for customer: şerife arıkan
2025-06-03 17:14:48.228 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120060688601
2025-06-03 17:14:48.230 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21297 with CariKod: 120060688601
2025-06-03 17:14:48.233 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:14:48.236 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:14:48.238 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:14:48.240 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:27:33.135 +03:00 [INF] Application built successfully.
2025-06-03 17:27:33.166 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-06-03 17:27:33.185 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-06-03 17:27:33.410 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-06-03 17:27:33.412 +03:00 [INF] HTTP request pipeline configuration complete.
2025-06-03 17:27:33.414 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-06-03 17:27:33.416 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-06-03 17:27:33.418 +03:00 [INF] Pre-run checks complete.
2025-06-03 17:27:33.421 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-06-03 17:27:33.573 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-06-03 17:36:23.050 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 17:36:23.054 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 17:36:23.064 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 17:36:23.065 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 17:36:23.107 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 17:36:23.115 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 17:36:23.116 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 17:36:23.123 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:36:23.126 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 17:36:23.132 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:36:23.140 +03:00 [INF] Loaded last cari kod suffix from file: 601
2025-06-03 17:36:23.146 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21298
2025-06-03 17:36:23.148 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21298 for Netsis integration.
2025-06-03 17:36:23.150 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:36:23.151 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:36:23.155 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 17:36:23.166 +03:00 [DBG] Saved last cari kod suffix to file: 602
2025-06-03 17:36:23.168 +03:00 [INF] Generated new Cari Kod: 120061983602 (suffix: 602, timestamp: 1748961383)
2025-06-03 17:36:23.171 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120061983602, Unvan: Didem Düzenci
2025-06-03 17:36:23.181 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-06-03 17:36:24.404 +03:00 [INF] New Netsis token acquired. Expires at: "2025-06-03T14:55:23.4046121Z"
2025-06-03 17:36:24.408 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:36:24.435 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120061983602","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120061983602","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:36:24.606 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120061983602","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120061983602","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:36:24.617 +03:00 [INF] Successfully created Netsis cari. CariKod: 120061983602 (attempt 1)
2025-06-03 17:36:24.621 +03:00 [INF] ✅ Successfully created cari: 120061983602 for customer: Didem Düzenci
2025-06-03 17:36:24.624 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120061983602
2025-06-03 17:36:24.627 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21298 with CariKod: 120061983602
2025-06-03 17:36:24.632 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:36:24.634 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:36:24.638 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:36:24.641 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:36:30.584 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1"}]}
2025-06-03 17:36:30.589 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 17:36:30.592 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 17:36:30.593 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 17:36:30.600 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 17:36:30.602 +03:00 [INF] 🔍 Content-Length: 503
2025-06-03 17:36:30.604 +03:00 [INF] 🔍 JSON Payload Length: 503
2025-06-03 17:36:30.608 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 17:36:30.611 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 17:36:30.613 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1"}]}
2025-06-03 17:36:30.621 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 17:36:30.623 +03:00 [INF] 🔍 Content length check: 503 chars
2025-06-03 17:36:30.624 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 17:36:30.626 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 17:36:30.627 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1"}]}
2025-06-03 17:36:51.780 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 17:36:51.793 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 17:36:51.795 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 17:36:51.798 +03:00 [INF] 🔍 Response Content-Length: 4440
2025-06-03 17:36:51.801 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 17:36:51.803 +03:00 [INF] 🔍 Response Body Length: 4440
2025-06-03 17:36:51.805 +03:00 [INF] 🔍 Response Body: {"Meta":{"Href":"http://localhost:7070/api/v2/ItemSlips?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"Seri":null,"FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","FATIRS_NO":"F00002025000832","Tarih":"2025-06-03 00:00:00","Tip":7,"KOD1":"\u0000","YEDEK":"\u0000","KOD2":"\u0000","TIPI":1,"BRUTTUTAR":730.9,"KDV":73.09,"DovizTut":0.0,"SAT_ISKT":0.0,"GENELTOPLAM":800.0,"YUVARLAMA":-3.99,"MFAZ_ISKT":0.0,"GEN_ISK1O":0.0,"GEN_ISK2O":0.0,"GEN_ISK3O":0.0,"FAT_ALTM1":0.0,"FAT_ALTM2":0.0,"KS_KODU":"C-00","ODEMEGUNU":0,"ODEMETARIHI":"2025-06-03 00:00:00","ENTEGRE_TRH":"2025-06-03 00:00:00","KDV_DAHILMI":true,"PLA_KODU":"19","SIRANO":0,"DOVIZTIP":0,"FIYATTARIHI":"2025-06-03 10:46:35","GENISK1TIP":0,"GENISK2TIP":0,"GENISK3TIP":0,"EXPORTTYPE":0,"AMBHARTUR":4,"OnayTipi":"A","OnayNum":0,"GCKOD_GIRIS":0,"GCKOD_CIKIS":0,"GEN_ISK1T":0.0,"GEN_ISK2T":0.0,"GEN_ISK3T":0.0,"CikisYeri":4,"Degissin":0,"TopGirDepo":0,"TopDepo":0,"KOSVADEGUNU":0,"BrMaliyet":0.0,"BOLGE_FARKI_ISK":0.0,"KDV1T":0.0,"KDV1O":0.0,"KDV2O":0.0,"KDV2T":0.0,"KDV3O":0.0,"KDV3T":0.0,"KDV4O":0.0,"KDV4T":0.0,"KDV5O":0.0,"KDV5T":0.0,"EfaturaCarisiMi":true,"BaglantiNo":0,"EFatOzelKod":0,"OTV":0.0,"EIrsaliye":false,"HalFaturasi":0,"FAT_ALTM3":0.0,"DovBazTarihi":"2025-06-03 00:00:00","OTVTevTutar":0.0,"Konaklama":false},"EIrsEkBilgi":{},"HalFaturaMasraflari":{},"Use64BitService":false,"TransactSupport":true,"MuhasebelesmisBelge":false,"KalemAdedi":1,"FaturaTip":7,"SonNumaraYazilsin":true,"OtoIskontoGetir":true,"KosulluHesapla":false,"InternalObjectAddress":126263056,"SeriliHesapla":true,"FiyatSistemineGoreHesapla":false,"StokKartinaGoreHesapla":false,"OtoVadeGunGetir":true,"OtomatikIslemTipiGetir":false,"OtomatikOdemeKoduGetir":false,"MaliyetTipineGoreHesapla":false,"OtomatikCevrimYapilsin":false,"KayitliNumaraOtomatikGuncellensin":false,"Siralama":"\u0000","EPostaGonderilsin":true,"OtoNakliyeKatSayisiGetir":true,"OtoBolgeFarkIskGetir":true,"RiskKontrol":true,"TahsilatKalemAdedi":0,"TahsilatKayitKullan":false,"AcikBelgeTahsilat":false,"BaglantiKontrol":true,"FaturaOtvTevkifatHesaplansin":false,"YuklemeGunuHesapla":false,"Kalems":[{"DovizAdi":"USD","STra_DovizAdi":"TL","KalemSeri":[],"Asorti":null,"KosulMalFazlasiIsle":false,"StokKodu":"1235C017","Sira":1,"STra_FATIRSNO":"F00002025000832","STra_GCMIK":1.0,"STra_GCMIK2":0.0,"CEVRIM":0.0,"STra_TAR":"2025-06-03 00:00:00","STra_NF":730.9,"STra_BF":803.99,"STra_IAF":0.0,"STra_KDV":10.0,"STra_SatIsk":0.0,"STra_SatIsk2":0.0,"STra_MALFISK":0.0,"STra_HTUR":"H","STra_DOVTIP":0,"PROMASYON_KODU":0,"STra_DOVFIAT":0.0,"STra_ODEGUN":0,"STra_KOD1":"\u0000","STra_KOD2":"\u0000","STra_SIP_TURU":"S","Plasiyer_Kodu":"19","Ekalanneden":"\u0000","Stra_Otv":0.0,"Redneden":0,"STra_SIPKONT":1,"Firmadovtip":0,"Firmadovtut":0.0,"Firmadovmal":0.0,"Update_Kodu":"\u0000","Ecza_fat_tip":0,"Olcubr":1,"Vadetar":"2025-06-03 00:00:00","BaglantiNo":0,"BrCevrim1":1.0,"BrCevrim2":1.0,"Yed_Bf":803.99,"STra_BGTIP":"I","ReferansKodu":"21298-1","C_Yedek6":" ","STra_FTIRSIP":"6","STra_CARI_KOD":"12000104118","STra_GC":"C","DEPO_KODU":203,"Gir_Depo_Kodu":0,"STra_ACIK":"12000104118","Stra_OnayTipi":"A","Stra_OnayNum":0,"Stra_SubeKodu":203,"Stok_IsletmeKod":-1,"Stok_SubeKod":-1,"Stra_Exporttype":0,"IncKeyNo":73338,"IncKeyNo2":0,"TesMik":0.0,"TesMFMik":0.0,"MALADI":"Wayla Scarf","STOK_GRKOD":"1001","STMUHDKOD":153,"SONGIRBFIAT":0.0,"OBR1":"AD","SabitDepKod":203,"DOVTIP":1,"DOVIZ_TURU":0,"Fiyatlar1":0.0,"Fiyatlar2":0.0,"Fiyatlar3":0.0,"Fiyatlar4":0.0,"Fiyatlar5":0.0,"Fiyatlar6":0.0,"Fiyatlar7":0.0,"Kilit":"\u0000","SatisKDVOran":10.0,"AlisKDVOran":10.0,"Isk_Flag":0,"SipTesKont":0,"Mamulmu":"H","SeriTakibi":"H","Stra_Exportmik":0.0,"STra_SatIsk3":0.0,"Stra_FiyatTar":"2025-06-03 00:00:00","Kul_Mik":0.0,"Fiat_birimi":1,"Sat_IskTipleri1":0,"Sat_IskTipleri2":0,"Sat_IskTipleri3":0,"Koli_Inc":0,"KoliStok":false,"Tur":"D","Stra_FiiliTar":"1900-01-01 00:00:00","BirimPuan":0,"PuanDeger":0.0,"KalemGenIskOran1":0.0,"KalemGenIskOran2":0.0,"KalemGenIskOran3":0.0,"OtvFlag":0,"Otvtut":0.0,"STra_SatIsk4":0.0,"STra_SatIsk5":0.0,"STra_SatIsk6":0.0,"Kull1S":"8680797209217","KKMalF":0.0,"Stra_FiyatBirimi":1,"Stra_IrsKont":0,"SatisKilit":"H","Payda_1":1.0,"D_YEDEK10":"1900-01-01 00:00:00","Sat_IskTipleri4":0,"Sat_IskTipleri5":0,"Sat_IskTipleri6":0,"EsnekMi":false,"SeriSayisi":0,"Stra_OTVFiat":0.0,"BolgeFark":0.0,"GEKAPTutar":0.0,"GEKAPAmbTutar":0.0,"KalemStopajOran":0.0}]}}
2025-06-03 17:36:51.827 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: true, ErrorCode: null, ErrorDesc: null
2025-06-03 17:36:51.830 +03:00 [INF] ✅ RestAPI SUCCESS for 21298
2025-06-03 17:36:51.833 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RestAPI_OK:21298
2025-06-03 17:36:51.834 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: RestAPI_OK:21298
2025-06-03 17:36:51.837 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:36:51.845 +03:00 [INF] Loaded last cari kod suffix from file: 602
2025-06-03 17:36:51.849 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 17:36:51.856 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:36:51.858 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21297
2025-06-03 17:36:51.861 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21297 for Netsis integration.
2025-06-03 17:36:51.863 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:36:51.864 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:36:51.866 +03:00 [INF] Creating cari with data - Name: şerife arıkan, Email: NULL, Phone: , City: İstanbul, Address: Yunus Emre Mah (AYŞE ÇARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / İSTANBUL
2025-06-03 17:36:51.875 +03:00 [DBG] Saved last cari kod suffix to file: 603
2025-06-03 17:36:51.877 +03:00 [INF] Generated new Cari Kod: 120062011603 (suffix: 603, timestamp: 1748961411)
2025-06-03 17:36:51.882 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120062011603, Unvan: şerife arıkan
2025-06-03 17:36:51.884 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:36:51.887 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:36:51.889 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062011603","CARI_ISIM":"\u015Ferife ar\u0131kan","CARI_TIP":"A","ADRES1":"Yunus Emre Mah (AY\u015EE \u00C7ARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / \u0130STANBUL","ADRES2":"","IL":"\u0130stanbul","ILCE":"Sancaktepe","ULKE_KODU":"TR","POSTA_KODU":"34000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120062011603","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:36:51.998 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062011603","ULKE_KODU":"TR","CARI_ISIM":"şerife arıkan","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120062011603","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:36:52.008 +03:00 [INF] Successfully created Netsis cari. CariKod: 120062011603 (attempt 1)
2025-06-03 17:36:52.015 +03:00 [INF] ✅ Successfully created cari: 120062011603 for customer: şerife arıkan
2025-06-03 17:36:52.018 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120062011603
2025-06-03 17:36:52.023 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21297 with CariKod: 120062011603
2025-06-03 17:36:52.027 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:36:52.030 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:36:52.034 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:36:52.036 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:41:58.112 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:40:08","FIYATTARIHI":"2025-06-03 10:40:08","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C060","STra_GCMIK":1,"STra_NF":603.99,"STra_BF":603.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:40:08","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21297-1"}]}
2025-06-03 17:41:58.133 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 17:41:58.143 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 17:41:58.152 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 17:41:58.164 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 17:41:58.175 +03:00 [INF] 🔍 Content-Length: 503
2025-06-03 17:41:58.179 +03:00 [INF] 🔍 JSON Payload Length: 503
2025-06-03 17:41:58.182 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 17:41:58.185 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 17:41:58.188 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:40:08","FIYATTARIHI":"2025-06-03 10:40:08","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C060","STra_GCMIK":1,"STra_NF":603.99,"STra_BF":603.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:40:08","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21297-1"}]}
2025-06-03 17:41:58.192 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 17:41:58.194 +03:00 [INF] 🔍 Content length check: 503 chars
2025-06-03 17:41:58.197 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 17:41:58.198 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 17:41:58.200 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:40:08","FIYATTARIHI":"2025-06-03 10:40:08","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C060","STra_GCMIK":1,"STra_NF":603.99,"STra_BF":603.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:40:08","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21297-1"}]}
2025-06-03 17:41:59.694 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 17:41:59.696 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 17:41:59.699 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 17:41:59.702 +03:00 [INF] 🔍 Response Content-Length: 4455
2025-06-03 17:41:59.706 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 17:41:59.709 +03:00 [INF] 🔍 Response Body Length: 4455
2025-06-03 17:41:59.710 +03:00 [INF] 🔍 Response Body: {"Meta":{"Href":"http://localhost:7070/api/v2/ItemSlips?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"Seri":null,"FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","FATIRS_NO":"F00002025000833","Tarih":"2025-06-03 00:00:00","Tip":7,"KOD1":"\u0000","YEDEK":"\u0000","KOD2":"\u0000","TIPI":1,"BRUTTUTAR":549.08,"KDV":54.91,"DovizTut":0.0,"SAT_ISKT":0.0,"GENELTOPLAM":600.0,"YUVARLAMA":-3.99,"MFAZ_ISKT":0.0,"GEN_ISK1O":0.0,"GEN_ISK2O":0.0,"GEN_ISK3O":0.0,"FAT_ALTM1":0.0,"FAT_ALTM2":0.0,"KS_KODU":"C-00","ODEMEGUNU":0,"ODEMETARIHI":"2025-06-03 00:00:00","ENTEGRE_TRH":"2025-06-03 00:00:00","KDV_DAHILMI":true,"PLA_KODU":"19","SIRANO":0,"DOVIZTIP":0,"FIYATTARIHI":"2025-06-03 10:40:08","GENISK1TIP":0,"GENISK2TIP":0,"GENISK3TIP":0,"EXPORTTYPE":0,"AMBHARTUR":4,"OnayTipi":"A","OnayNum":0,"GCKOD_GIRIS":0,"GCKOD_CIKIS":0,"GEN_ISK1T":0.0,"GEN_ISK2T":0.0,"GEN_ISK3T":0.0,"CikisYeri":4,"Degissin":0,"TopGirDepo":0,"TopDepo":0,"KOSVADEGUNU":0,"BrMaliyet":0.0,"BOLGE_FARKI_ISK":0.0,"KDV1T":0.0,"KDV1O":0.0,"KDV2O":0.0,"KDV2T":0.0,"KDV3O":0.0,"KDV3T":0.0,"KDV4O":0.0,"KDV4T":0.0,"KDV5O":0.0,"KDV5T":0.0,"EfaturaCarisiMi":true,"BaglantiNo":0,"EFatOzelKod":0,"OTV":0.0,"EIrsaliye":false,"HalFaturasi":0,"FAT_ALTM3":0.0,"DovBazTarihi":"2025-06-03 00:00:00","OTVTevTutar":0.0,"Konaklama":false},"EIrsEkBilgi":{},"HalFaturaMasraflari":{},"Use64BitService":false,"TransactSupport":true,"MuhasebelesmisBelge":false,"KalemAdedi":1,"FaturaTip":7,"SonNumaraYazilsin":true,"OtoIskontoGetir":true,"KosulluHesapla":false,"InternalObjectAddress":100924288,"SeriliHesapla":true,"FiyatSistemineGoreHesapla":false,"StokKartinaGoreHesapla":false,"OtoVadeGunGetir":true,"OtomatikIslemTipiGetir":false,"OtomatikOdemeKoduGetir":false,"MaliyetTipineGoreHesapla":false,"OtomatikCevrimYapilsin":false,"KayitliNumaraOtomatikGuncellensin":false,"Siralama":"\u0000","EPostaGonderilsin":true,"OtoNakliyeKatSayisiGetir":true,"OtoBolgeFarkIskGetir":true,"RiskKontrol":true,"TahsilatKalemAdedi":0,"TahsilatKayitKullan":false,"AcikBelgeTahsilat":false,"BaglantiKontrol":true,"FaturaOtvTevkifatHesaplansin":false,"YuklemeGunuHesapla":false,"Kalems":[{"DovizAdi":"USD","STra_DovizAdi":"TL","KalemSeri":[],"Asorti":null,"KosulMalFazlasiIsle":false,"StokKodu":"1276C060","Sira":1,"STra_FATIRSNO":"F00002025000833","STra_GCMIK":1.0,"STra_GCMIK2":0.0,"CEVRIM":0.0,"STra_TAR":"2025-06-03 00:00:00","STra_NF":549.08181818,"STra_BF":603.99,"STra_IAF":0.0,"STra_KDV":10.0,"STra_SatIsk":0.0,"STra_SatIsk2":0.0,"STra_MALFISK":0.0,"STra_HTUR":"H","STra_DOVTIP":0,"PROMASYON_KODU":0,"STra_DOVFIAT":0.0,"STra_ODEGUN":0,"STra_KOD1":"\u0000","STra_KOD2":"\u0000","STra_SIP_TURU":"S","Plasiyer_Kodu":"19","Ekalanneden":"\u0000","Stra_Otv":0.0,"Redneden":0,"STra_SIPKONT":1,"Firmadovtip":0,"Firmadovtut":0.0,"Firmadovmal":0.0,"Update_Kodu":"\u0000","Ecza_fat_tip":0,"Olcubr":1,"Vadetar":"2025-06-03 00:00:00","BaglantiNo":0,"BrCevrim1":1.0,"BrCevrim2":1.0,"Yed_Bf":603.99,"STra_BGTIP":"I","ReferansKodu":"21297-1","C_Yedek6":" ","STra_FTIRSIP":"6","STra_CARI_KOD":"12000104118","STra_GC":"C","DEPO_KODU":203,"Gir_Depo_Kodu":0,"STra_ACIK":"12000104118","Stra_OnayTipi":"A","Stra_OnayNum":0,"Stra_SubeKodu":203,"Stok_IsletmeKod":-1,"Stok_SubeKod":-1,"Stra_Exporttype":0,"IncKeyNo":73339,"IncKeyNo2":0,"TesMik":0.0,"TesMFMik":0.0,"MALADI":"Gravois Bucket Hat","STOK_GRKOD":"1002","STMUHDKOD":153,"SONGIRBFIAT":0.0,"OBR1":"AD","SabitDepKod":203,"DOVTIP":1,"DOVIZ_TURU":0,"Fiyatlar1":0.0,"Fiyatlar2":0.0,"Fiyatlar3":0.0,"Fiyatlar4":0.0,"Fiyatlar5":0.0,"Fiyatlar6":0.0,"Fiyatlar7":0.0,"Kilit":"\u0000","SatisKDVOran":10.0,"AlisKDVOran":10.0,"Isk_Flag":0,"SipTesKont":0,"Mamulmu":"H","SeriTakibi":"H","Stra_Exportmik":0.0,"STra_SatIsk3":0.0,"Stra_FiyatTar":"2025-06-03 00:00:00","Kul_Mik":0.0,"Fiat_birimi":1,"Sat_IskTipleri1":0,"Sat_IskTipleri2":0,"Sat_IskTipleri3":0,"Koli_Inc":0,"KoliStok":false,"Tur":"D","Stra_FiiliTar":"1900-01-01 00:00:00","BirimPuan":0,"PuanDeger":0.0,"KalemGenIskOran1":0.0,"KalemGenIskOran2":0.0,"KalemGenIskOran3":0.0,"OtvFlag":0,"Otvtut":0.0,"STra_SatIsk4":0.0,"STra_SatIsk5":0.0,"STra_SatIsk6":0.0,"Kull1S":"8680797906956","KKMalF":0.0,"Stra_FiyatBirimi":1,"Stra_IrsKont":0,"SatisKilit":"H","Payda_1":1.0,"D_YEDEK10":"1900-01-01 00:00:00","Sat_IskTipleri4":0,"Sat_IskTipleri5":0,"Sat_IskTipleri6":0,"EsnekMi":false,"SeriSayisi":0,"Stra_OTVFiat":0.0,"BolgeFark":0.0,"GEKAPTutar":0.0,"GEKAPAmbTutar":0.0,"KalemStopajOran":0.0}]}}
2025-06-03 17:41:59.749 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: true, ErrorCode: null, ErrorDesc: null
2025-06-03 17:41:59.751 +03:00 [INF] ✅ RestAPI SUCCESS for 21297
2025-06-03 17:41:59.752 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RestAPI_OK:21297
2025-06-03 17:41:59.754 +03:00 [INF] OrderNumber 21297 (Client ID: 956) successfully processed by ERP. Result: RestAPI_OK:21297
2025-06-03 17:41:59.760 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:41:59.762 +03:00 [INF] Loaded last cari kod suffix from file: 603
2025-06-03 17:41:59.763 +03:00 [INF] Processing OrderNumber: 21296 (Client ID: 955) with ERPService.
2025-06-03 17:41:59.769 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:41:59.771 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21296
2025-06-03 17:41:59.772 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21296 for Netsis integration.
2025-06-03 17:41:59.774 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:41:59.777 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:41:59.780 +03:00 [INF] Creating cari with data - Name: Mustafa Tacettin Ayrancı, Email: NULL, Phone: , City: Balıkesir, Address: Bahçelievler Mah 5135. Sokak No:13/4 Demir32 Apt.
2025-06-03 17:41:59.784 +03:00 [DBG] Saved last cari kod suffix to file: 604
2025-06-03 17:41:59.788 +03:00 [INF] Generated new Cari Kod: 120062319604 (suffix: 604, timestamp: 1748961719)
2025-06-03 17:41:59.790 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120062319604, Unvan: Mustafa Tacettin Ayrancı
2025-06-03 17:41:59.793 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:41:59.795 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:41:59.799 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062319604","CARI_ISIM":"Mustafa Tacettin Ayranc\u0131","CARI_TIP":"A","ADRES1":"Bah\u00E7elievler Mah 5135. Sokak No:13/4 Demir32 Apt.","ADRES2":"","IL":"Bal\u0131kesir","ILCE":"Alt\u0131eyl\u00FCl","ULKE_KODU":"TR","POSTA_KODU":"10000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120062319604","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:41:59.924 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062319604","ULKE_KODU":"TR","CARI_ISIM":"Mustafa Tacettin Ayrancı","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120062319604","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:41:59.932 +03:00 [INF] Successfully created Netsis cari. CariKod: 120062319604 (attempt 1)
2025-06-03 17:41:59.937 +03:00 [INF] ✅ Successfully created cari: 120062319604 for customer: Mustafa Tacettin Ayrancı
2025-06-03 17:41:59.939 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120062319604
2025-06-03 17:41:59.941 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21296 with CariKod: 120062319604
2025-06-03 17:41:59.946 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:41:59.948 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:41:59.949 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:41:59.951 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:42:00.913 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:06:25","FIYATTARIHI":"2025-06-03 10:06:25","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C060","STra_GCMIK":1,"STra_NF":603.99,"STra_BF":603.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:06:25","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21296-1"}]}
2025-06-03 17:42:00.925 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 17:42:00.931 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 17:42:00.934 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 17:42:00.935 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 17:42:00.937 +03:00 [INF] 🔍 Content-Length: 503
2025-06-03 17:42:00.938 +03:00 [INF] 🔍 JSON Payload Length: 503
2025-06-03 17:42:00.939 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 17:42:00.941 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 17:42:00.944 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:06:25","FIYATTARIHI":"2025-06-03 10:06:25","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C060","STra_GCMIK":1,"STra_NF":603.99,"STra_BF":603.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:06:25","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21296-1"}]}
2025-06-03 17:42:00.946 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 17:42:00.948 +03:00 [INF] 🔍 Content length check: 503 chars
2025-06-03 17:42:00.954 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 17:42:00.956 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 17:42:00.958 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:06:25","FIYATTARIHI":"2025-06-03 10:06:25","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C060","STra_GCMIK":1,"STra_NF":603.99,"STra_BF":603.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:06:25","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21296-1"}]}
2025-06-03 17:42:02.310 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 17:42:02.314 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 17:42:02.316 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 17:42:02.318 +03:00 [INF] 🔍 Response Content-Length: 4455
2025-06-03 17:42:02.321 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 17:42:02.323 +03:00 [INF] 🔍 Response Body Length: 4455
2025-06-03 17:42:02.325 +03:00 [INF] 🔍 Response Body: {"Meta":{"Href":"http://localhost:7070/api/v2/ItemSlips?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"Seri":null,"FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","FATIRS_NO":"F00002025000834","Tarih":"2025-06-03 00:00:00","Tip":7,"KOD1":"\u0000","YEDEK":"\u0000","KOD2":"\u0000","TIPI":1,"BRUTTUTAR":549.08,"KDV":54.91,"DovizTut":0.0,"SAT_ISKT":0.0,"GENELTOPLAM":600.0,"YUVARLAMA":-3.99,"MFAZ_ISKT":0.0,"GEN_ISK1O":0.0,"GEN_ISK2O":0.0,"GEN_ISK3O":0.0,"FAT_ALTM1":0.0,"FAT_ALTM2":0.0,"KS_KODU":"C-00","ODEMEGUNU":0,"ODEMETARIHI":"2025-06-03 00:00:00","ENTEGRE_TRH":"2025-06-03 00:00:00","KDV_DAHILMI":true,"PLA_KODU":"19","SIRANO":0,"DOVIZTIP":0,"FIYATTARIHI":"2025-06-03 10:06:25","GENISK1TIP":0,"GENISK2TIP":0,"GENISK3TIP":0,"EXPORTTYPE":0,"AMBHARTUR":4,"OnayTipi":"A","OnayNum":0,"GCKOD_GIRIS":0,"GCKOD_CIKIS":0,"GEN_ISK1T":0.0,"GEN_ISK2T":0.0,"GEN_ISK3T":0.0,"CikisYeri":4,"Degissin":0,"TopGirDepo":0,"TopDepo":0,"KOSVADEGUNU":0,"BrMaliyet":0.0,"BOLGE_FARKI_ISK":0.0,"KDV1T":0.0,"KDV1O":0.0,"KDV2O":0.0,"KDV2T":0.0,"KDV3O":0.0,"KDV3T":0.0,"KDV4O":0.0,"KDV4T":0.0,"KDV5O":0.0,"KDV5T":0.0,"EfaturaCarisiMi":true,"BaglantiNo":0,"EFatOzelKod":0,"OTV":0.0,"EIrsaliye":false,"HalFaturasi":0,"FAT_ALTM3":0.0,"DovBazTarihi":"2025-06-03 00:00:00","OTVTevTutar":0.0,"Konaklama":false},"EIrsEkBilgi":{},"HalFaturaMasraflari":{},"Use64BitService":false,"TransactSupport":true,"MuhasebelesmisBelge":false,"KalemAdedi":1,"FaturaTip":7,"SonNumaraYazilsin":true,"OtoIskontoGetir":true,"KosulluHesapla":false,"InternalObjectAddress":100398384,"SeriliHesapla":true,"FiyatSistemineGoreHesapla":false,"StokKartinaGoreHesapla":false,"OtoVadeGunGetir":true,"OtomatikIslemTipiGetir":false,"OtomatikOdemeKoduGetir":false,"MaliyetTipineGoreHesapla":false,"OtomatikCevrimYapilsin":false,"KayitliNumaraOtomatikGuncellensin":false,"Siralama":"\u0000","EPostaGonderilsin":true,"OtoNakliyeKatSayisiGetir":true,"OtoBolgeFarkIskGetir":true,"RiskKontrol":true,"TahsilatKalemAdedi":0,"TahsilatKayitKullan":false,"AcikBelgeTahsilat":false,"BaglantiKontrol":true,"FaturaOtvTevkifatHesaplansin":false,"YuklemeGunuHesapla":false,"Kalems":[{"DovizAdi":"USD","STra_DovizAdi":"TL","KalemSeri":[],"Asorti":null,"KosulMalFazlasiIsle":false,"StokKodu":"1276C060","Sira":1,"STra_FATIRSNO":"F00002025000834","STra_GCMIK":1.0,"STra_GCMIK2":0.0,"CEVRIM":0.0,"STra_TAR":"2025-06-03 00:00:00","STra_NF":549.08181818,"STra_BF":603.99,"STra_IAF":0.0,"STra_KDV":10.0,"STra_SatIsk":0.0,"STra_SatIsk2":0.0,"STra_MALFISK":0.0,"STra_HTUR":"H","STra_DOVTIP":0,"PROMASYON_KODU":0,"STra_DOVFIAT":0.0,"STra_ODEGUN":0,"STra_KOD1":"\u0000","STra_KOD2":"\u0000","STra_SIP_TURU":"S","Plasiyer_Kodu":"19","Ekalanneden":"\u0000","Stra_Otv":0.0,"Redneden":0,"STra_SIPKONT":1,"Firmadovtip":0,"Firmadovtut":0.0,"Firmadovmal":0.0,"Update_Kodu":"\u0000","Ecza_fat_tip":0,"Olcubr":1,"Vadetar":"2025-06-03 00:00:00","BaglantiNo":0,"BrCevrim1":1.0,"BrCevrim2":1.0,"Yed_Bf":603.99,"STra_BGTIP":"I","ReferansKodu":"21296-1","C_Yedek6":" ","STra_FTIRSIP":"6","STra_CARI_KOD":"12000104118","STra_GC":"C","DEPO_KODU":203,"Gir_Depo_Kodu":0,"STra_ACIK":"12000104118","Stra_OnayTipi":"A","Stra_OnayNum":0,"Stra_SubeKodu":203,"Stok_IsletmeKod":-1,"Stok_SubeKod":-1,"Stra_Exporttype":0,"IncKeyNo":73340,"IncKeyNo2":0,"TesMik":0.0,"TesMFMik":0.0,"MALADI":"Gravois Bucket Hat","STOK_GRKOD":"1002","STMUHDKOD":153,"SONGIRBFIAT":0.0,"OBR1":"AD","SabitDepKod":203,"DOVTIP":1,"DOVIZ_TURU":0,"Fiyatlar1":0.0,"Fiyatlar2":0.0,"Fiyatlar3":0.0,"Fiyatlar4":0.0,"Fiyatlar5":0.0,"Fiyatlar6":0.0,"Fiyatlar7":0.0,"Kilit":"\u0000","SatisKDVOran":10.0,"AlisKDVOran":10.0,"Isk_Flag":0,"SipTesKont":0,"Mamulmu":"H","SeriTakibi":"H","Stra_Exportmik":0.0,"STra_SatIsk3":0.0,"Stra_FiyatTar":"2025-06-03 00:00:00","Kul_Mik":0.0,"Fiat_birimi":1,"Sat_IskTipleri1":0,"Sat_IskTipleri2":0,"Sat_IskTipleri3":0,"Koli_Inc":0,"KoliStok":false,"Tur":"D","Stra_FiiliTar":"1900-01-01 00:00:00","BirimPuan":0,"PuanDeger":0.0,"KalemGenIskOran1":0.0,"KalemGenIskOran2":0.0,"KalemGenIskOran3":0.0,"OtvFlag":0,"Otvtut":0.0,"STra_SatIsk4":0.0,"STra_SatIsk5":0.0,"STra_SatIsk6":0.0,"Kull1S":"8680797906956","KKMalF":0.0,"Stra_FiyatBirimi":1,"Stra_IrsKont":0,"SatisKilit":"H","Payda_1":1.0,"D_YEDEK10":"1900-01-01 00:00:00","Sat_IskTipleri4":0,"Sat_IskTipleri5":0,"Sat_IskTipleri6":0,"EsnekMi":false,"SeriSayisi":0,"Stra_OTVFiat":0.0,"BolgeFark":0.0,"GEKAPTutar":0.0,"GEKAPAmbTutar":0.0,"KalemStopajOran":0.0}]}}
2025-06-03 17:42:02.351 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: true, ErrorCode: null, ErrorDesc: null
2025-06-03 17:42:02.354 +03:00 [INF] ✅ RestAPI SUCCESS for 21296
2025-06-03 17:42:02.356 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RestAPI_OK:21296
2025-06-03 17:42:02.360 +03:00 [INF] OrderNumber 21296 (Client ID: 955) successfully processed by ERP. Result: RestAPI_OK:21296
2025-06-03 17:42:02.365 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:42:02.367 +03:00 [INF] Loaded last cari kod suffix from file: 604
2025-06-03 17:42:02.371 +03:00 [INF] Processing OrderNumber: 21295 (Client ID: 954) with ERPService.
2025-06-03 17:42:02.378 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:42:02.382 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21295
2025-06-03 17:42:02.385 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21295 for Netsis integration.
2025-06-03 17:42:02.388 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:42:02.393 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:42:02.396 +03:00 [INF] Creating cari with data - Name: kadriyle Aytek, Email: NULL, Phone: , City: Muğla, Address: Turgutreis Mah Karatoprak caddesi, Nağme Sitesi, 1 Ada, No: 10 Turgutreis, Bodrum , Muğla
2025-06-03 17:42:02.403 +03:00 [DBG] Saved last cari kod suffix to file: 605
2025-06-03 17:42:02.407 +03:00 [INF] Generated new Cari Kod: 120062322605 (suffix: 605, timestamp: 1748961722)
2025-06-03 17:42:02.409 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120062322605, Unvan: kadriyle Aytek
2025-06-03 17:42:02.411 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:02.416 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:02.417 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062322605","CARI_ISIM":"kadriyle Aytek","CARI_TIP":"A","ADRES1":"Turgutreis Mah Karatoprak caddesi, Na\u011Fme Sitesi, 1 Ada, No: 10 Turgutreis, Bodrum , Mu\u011Fla","ADRES2":"","IL":"Mu\u011Fla","ILCE":"Bodrum","ULKE_KODU":"TR","POSTA_KODU":"48000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120062322605","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:42:02.525 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062322605","ULKE_KODU":"TR","CARI_ISIM":"kadriyle Aytek","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120062322605","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:42:02.538 +03:00 [INF] Successfully created Netsis cari. CariKod: 120062322605 (attempt 1)
2025-06-03 17:42:02.541 +03:00 [INF] ✅ Successfully created cari: 120062322605 for customer: kadriyle Aytek
2025-06-03 17:42:02.544 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120062322605
2025-06-03 17:42:02.546 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21295 with CariKod: 120062322605
2025-06-03 17:42:02.550 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:42:02.552 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:02.553 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:02.555 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:42:03.990 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 09:53:55","FIYATTARIHI":"2025-06-03 09:53:55","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1280C024","STra_GCMIK":1,"STra_NF":695.7,"STra_BF":695.7,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 09:53:55","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21295-1"}]}
2025-06-03 17:42:03.999 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 17:42:04.002 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 17:42:04.004 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 17:42:04.007 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 17:42:04.009 +03:00 [INF] 🔍 Content-Length: 501
2025-06-03 17:42:04.012 +03:00 [INF] 🔍 JSON Payload Length: 501
2025-06-03 17:42:04.013 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 17:42:04.015 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 17:42:04.017 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 09:53:55","FIYATTARIHI":"2025-06-03 09:53:55","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1280C024","STra_GCMIK":1,"STra_NF":695.7,"STra_BF":695.7,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 09:53:55","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21295-1"}]}
2025-06-03 17:42:04.023 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 17:42:04.025 +03:00 [INF] 🔍 Content length check: 501 chars
2025-06-03 17:42:04.027 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 17:42:04.029 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 17:42:04.031 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 09:53:55","FIYATTARIHI":"2025-06-03 09:53:55","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1280C024","STra_GCMIK":1,"STra_NF":695.7,"STra_BF":695.7,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 09:53:55","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21295-1"}]}
2025-06-03 17:42:05.490 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 17:42:05.493 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 17:42:05.496 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 17:42:05.500 +03:00 [INF] 🔍 Response Content-Length: 4449
2025-06-03 17:42:05.503 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 17:42:05.505 +03:00 [INF] 🔍 Response Body Length: 4449
2025-06-03 17:42:05.508 +03:00 [INF] 🔍 Response Body: {"Meta":{"Href":"http://localhost:7070/api/v2/ItemSlips?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"Seri":null,"FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","FATIRS_NO":"F00002025000835","Tarih":"2025-06-03 00:00:00","Tip":7,"KOD1":"\u0000","YEDEK":"\u0000","KOD2":"\u0000","TIPI":1,"BRUTTUTAR":632.45,"KDV":63.25,"DovizTut":0.0,"SAT_ISKT":0.0,"GENELTOPLAM":700.0,"YUVARLAMA":4.3,"MFAZ_ISKT":0.0,"GEN_ISK1O":0.0,"GEN_ISK2O":0.0,"GEN_ISK3O":0.0,"FAT_ALTM1":0.0,"FAT_ALTM2":0.0,"KS_KODU":"C-00","ODEMEGUNU":0,"ODEMETARIHI":"2025-06-03 00:00:00","ENTEGRE_TRH":"2025-06-03 00:00:00","KDV_DAHILMI":true,"PLA_KODU":"19","SIRANO":0,"DOVIZTIP":0,"FIYATTARIHI":"2025-06-03 09:53:55","GENISK1TIP":0,"GENISK2TIP":0,"GENISK3TIP":0,"EXPORTTYPE":0,"AMBHARTUR":4,"OnayTipi":"A","OnayNum":0,"GCKOD_GIRIS":0,"GCKOD_CIKIS":0,"GEN_ISK1T":0.0,"GEN_ISK2T":0.0,"GEN_ISK3T":0.0,"CikisYeri":4,"Degissin":0,"TopGirDepo":0,"TopDepo":0,"KOSVADEGUNU":0,"BrMaliyet":0.0,"BOLGE_FARKI_ISK":0.0,"KDV1T":0.0,"KDV1O":0.0,"KDV2O":0.0,"KDV2T":0.0,"KDV3O":0.0,"KDV3T":0.0,"KDV4O":0.0,"KDV4T":0.0,"KDV5O":0.0,"KDV5T":0.0,"EfaturaCarisiMi":true,"BaglantiNo":0,"EFatOzelKod":0,"OTV":0.0,"EIrsaliye":false,"HalFaturasi":0,"FAT_ALTM3":0.0,"DovBazTarihi":"2025-06-03 00:00:00","OTVTevTutar":0.0,"Konaklama":false},"EIrsEkBilgi":{},"HalFaturaMasraflari":{},"Use64BitService":false,"TransactSupport":true,"MuhasebelesmisBelge":false,"KalemAdedi":1,"FaturaTip":7,"SonNumaraYazilsin":true,"OtoIskontoGetir":true,"KosulluHesapla":false,"InternalObjectAddress":122797936,"SeriliHesapla":true,"FiyatSistemineGoreHesapla":false,"StokKartinaGoreHesapla":false,"OtoVadeGunGetir":true,"OtomatikIslemTipiGetir":false,"OtomatikOdemeKoduGetir":false,"MaliyetTipineGoreHesapla":false,"OtomatikCevrimYapilsin":false,"KayitliNumaraOtomatikGuncellensin":false,"Siralama":"\u0000","EPostaGonderilsin":true,"OtoNakliyeKatSayisiGetir":true,"OtoBolgeFarkIskGetir":true,"RiskKontrol":true,"TahsilatKalemAdedi":0,"TahsilatKayitKullan":false,"AcikBelgeTahsilat":false,"BaglantiKontrol":true,"FaturaOtvTevkifatHesaplansin":false,"YuklemeGunuHesapla":false,"Kalems":[{"DovizAdi":"USD","STra_DovizAdi":"TL","KalemSeri":[],"Asorti":null,"KosulMalFazlasiIsle":false,"StokKodu":"1280C024","Sira":1,"STra_FATIRSNO":"F00002025000835","STra_GCMIK":1.0,"STra_GCMIK2":0.0,"CEVRIM":0.0,"STra_TAR":"2025-06-03 00:00:00","STra_NF":632.45454545,"STra_BF":695.7,"STra_IAF":0.0,"STra_KDV":10.0,"STra_SatIsk":0.0,"STra_SatIsk2":0.0,"STra_MALFISK":0.0,"STra_HTUR":"H","STra_DOVTIP":0,"PROMASYON_KODU":0,"STra_DOVFIAT":0.0,"STra_ODEGUN":0,"STra_KOD1":"\u0000","STra_KOD2":"\u0000","STra_SIP_TURU":"S","Plasiyer_Kodu":"19","Ekalanneden":"\u0000","Stra_Otv":0.0,"Redneden":0,"STra_SIPKONT":1,"Firmadovtip":0,"Firmadovtut":0.0,"Firmadovmal":0.0,"Update_Kodu":"\u0000","Ecza_fat_tip":0,"Olcubr":1,"Vadetar":"2025-06-03 00:00:00","BaglantiNo":0,"BrCevrim1":1.0,"BrCevrim2":1.0,"Yed_Bf":695.7,"STra_BGTIP":"I","ReferansKodu":"21295-1","C_Yedek6":" ","STra_FTIRSIP":"6","STra_CARI_KOD":"12000104118","STra_GC":"C","DEPO_KODU":203,"Gir_Depo_Kodu":0,"STra_ACIK":"12000104118","Stra_OnayTipi":"A","Stra_OnayNum":0,"Stra_SubeKodu":203,"Stok_IsletmeKod":-1,"Stok_SubeKod":-1,"Stra_Exporttype":0,"IncKeyNo":73341,"IncKeyNo2":0,"TesMik":0.0,"TesMFMik":0.0,"MALADI":"Kalyy Bucket Hat","STOK_GRKOD":"1001","STMUHDKOD":152,"SONGIRBFIAT":0.0,"OBR1":"AD","SabitDepKod":203,"DOVTIP":1,"DOVIZ_TURU":0,"Fiyatlar1":0.0,"Fiyatlar2":0.0,"Fiyatlar3":0.0,"Fiyatlar4":0.0,"Fiyatlar5":0.0,"Fiyatlar6":0.0,"Fiyatlar7":0.0,"Kilit":"\u0000","SatisKDVOran":10.0,"AlisKDVOran":10.0,"Isk_Flag":0,"SipTesKont":0,"Mamulmu":"H","SeriTakibi":"H","Stra_Exportmik":0.0,"STra_SatIsk3":0.0,"Stra_FiyatTar":"2025-06-03 00:00:00","Kul_Mik":0.0,"Fiat_birimi":1,"Sat_IskTipleri1":0,"Sat_IskTipleri2":0,"Sat_IskTipleri3":0,"Koli_Inc":0,"KoliStok":false,"Tur":"D","Stra_FiiliTar":"1900-01-01 00:00:00","BirimPuan":0,"PuanDeger":0.0,"KalemGenIskOran1":0.0,"KalemGenIskOran2":0.0,"KalemGenIskOran3":0.0,"OtvFlag":0,"Otvtut":0.0,"STra_SatIsk4":0.0,"STra_SatIsk5":0.0,"STra_SatIsk6":0.0,"Kull1S":"8680797915859","KKMalF":0.0,"Stra_FiyatBirimi":1,"Stra_IrsKont":0,"SatisKilit":"H","Payda_1":1.0,"D_YEDEK10":"1900-01-01 00:00:00","Sat_IskTipleri4":0,"Sat_IskTipleri5":0,"Sat_IskTipleri6":0,"EsnekMi":false,"SeriSayisi":0,"Stra_OTVFiat":0.0,"BolgeFark":0.0,"GEKAPTutar":0.0,"GEKAPAmbTutar":0.0,"KalemStopajOran":0.0}]}}
2025-06-03 17:42:05.529 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: true, ErrorCode: null, ErrorDesc: null
2025-06-03 17:42:05.531 +03:00 [INF] ✅ RestAPI SUCCESS for 21295
2025-06-03 17:42:05.533 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RestAPI_OK:21295
2025-06-03 17:42:05.538 +03:00 [INF] OrderNumber 21295 (Client ID: 954) successfully processed by ERP. Result: RestAPI_OK:21295
2025-06-03 17:42:05.541 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:42:05.543 +03:00 [INF] Loaded last cari kod suffix from file: 605
2025-06-03 17:42:05.544 +03:00 [INF] Processing OrderNumber: 21294 (Client ID: 953) with ERPService.
2025-06-03 17:42:05.551 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:42:05.553 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21294
2025-06-03 17:42:05.557 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21294 for Netsis integration.
2025-06-03 17:42:05.559 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:42:05.561 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:42:05.562 +03:00 [INF] Creating cari with data - Name: kadriyle Aytek, Email: NULL, Phone: , City: Muğla, Address: Turgutreis Mah Karatoprak caddesi, Nağme Sitesi, 1 Ada, No: 10 Turgutreis, Bodrum , Muğla
2025-06-03 17:42:05.570 +03:00 [DBG] Saved last cari kod suffix to file: 606
2025-06-03 17:42:05.572 +03:00 [INF] Generated new Cari Kod: 120062325606 (suffix: 606, timestamp: 1748961725)
2025-06-03 17:42:05.579 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120062325606, Unvan: kadriyle Aytek
2025-06-03 17:42:05.581 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:05.583 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:05.585 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062325606","CARI_ISIM":"kadriyle Aytek","CARI_TIP":"A","ADRES1":"Turgutreis Mah Karatoprak caddesi, Na\u011Fme Sitesi, 1 Ada, No: 10 Turgutreis, Bodrum , Mu\u011Fla","ADRES2":"","IL":"Mu\u011Fla","ILCE":"Bodrum","ULKE_KODU":"TR","POSTA_KODU":"48000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120062325606","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:42:05.736 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062325606","ULKE_KODU":"TR","CARI_ISIM":"kadriyle Aytek","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120062325606","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:42:05.742 +03:00 [INF] Successfully created Netsis cari. CariKod: 120062325606 (attempt 1)
2025-06-03 17:42:05.744 +03:00 [INF] ✅ Successfully created cari: 120062325606 for customer: kadriyle Aytek
2025-06-03 17:42:05.748 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120062325606
2025-06-03 17:42:05.750 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21294 with CariKod: 120062325606
2025-06-03 17:42:05.753 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:42:05.758 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:05.761 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:05.764 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:42:06.444 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 09:23:03","FIYATTARIHI":"2025-06-03 09:23:03","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1274C171","STra_GCMIK":1,"STra_NF":505.99,"STra_BF":505.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 09:23:03","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21294-1"}]}
2025-06-03 17:42:06.450 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 17:42:06.453 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 17:42:06.457 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 17:42:06.458 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 17:42:06.460 +03:00 [INF] 🔍 Content-Length: 503
2025-06-03 17:42:06.461 +03:00 [INF] 🔍 JSON Payload Length: 503
2025-06-03 17:42:06.463 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 17:42:06.464 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 17:42:06.468 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 09:23:03","FIYATTARIHI":"2025-06-03 09:23:03","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1274C171","STra_GCMIK":1,"STra_NF":505.99,"STra_BF":505.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 09:23:03","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21294-1"}]}
2025-06-03 17:42:06.471 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 17:42:06.473 +03:00 [INF] 🔍 Content length check: 503 chars
2025-06-03 17:42:06.475 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 17:42:06.479 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 17:42:06.481 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 09:23:03","FIYATTARIHI":"2025-06-03 09:23:03","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1274C171","STra_GCMIK":1,"STra_NF":505.99,"STra_BF":505.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 09:23:03","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21294-1"}]}
2025-06-03 17:42:07.868 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-06-03 17:42:07.870 +03:00 [INF] Received encrypted data length: 11692
2025-06-03 17:42:07.874 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-06-03 17:42:07.876 +03:00 [INF] Decryption successful. JSON length: 8762. First 100 chars: [
  {
    "Id": 957,
    "OrderNumber": "21298",
    "OrderedAt": "2025-06-03T10:46:35.523",
  
2025-06-03 17:42:07.881 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 8
2025-06-03 17:42:07.883 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 8
2025-06-03 17:42:07.884 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 8 ORDERS ============
2025-06-03 17:42:07.887 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:42:07.888 +03:00 [INF] Loaded last cari kod suffix from file: 606
2025-06-03 17:42:07.890 +03:00 [INF] Processing OrderNumber: 21298 (Client ID: 957) with ERPService.
2025-06-03 17:42:07.894 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:42:07.895 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21298
2025-06-03 17:42:07.897 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21298 for Netsis integration.
2025-06-03 17:42:07.901 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:42:07.902 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:42:07.903 +03:00 [INF] Creating cari with data - Name: Didem Düzenci, Email: NULL, Phone: , City: Ankara, Address: Kazım Özalp Mah Hafta sokak no23 kat 4 daire 6
2025-06-03 17:42:07.909 +03:00 [DBG] Saved last cari kod suffix to file: 607
2025-06-03 17:42:07.910 +03:00 [INF] Generated new Cari Kod: 120062327607 (suffix: 607, timestamp: 1748961727)
2025-06-03 17:42:07.912 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120062327607, Unvan: Didem Düzenci
2025-06-03 17:42:07.915 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:07.917 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:07.920 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062327607","CARI_ISIM":"Didem D\u00FCzenci","CARI_TIP":"A","ADRES1":"Kaz\u0131m \u00D6zalp Mah Hafta sokak no23 kat 4 daire 6","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120062327607","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:42:07.925 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 17:42:07.928 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 17:42:07.931 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 17:42:07.934 +03:00 [INF] 🔍 Response Content-Length: 4445
2025-06-03 17:42:07.935 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 17:42:07.937 +03:00 [INF] 🔍 Response Body Length: 4445
2025-06-03 17:42:07.938 +03:00 [INF] 🔍 Response Body: {"Meta":{"Href":"http://localhost:7070/api/v2/ItemSlips?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"Seri":null,"FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","FATIRS_NO":"F00002025000836","Tarih":"2025-06-03 00:00:00","Tip":7,"KOD1":"\u0000","YEDEK":"\u0000","KOD2":"\u0000","TIPI":1,"BRUTTUTAR":459.99,"KDV":46.0,"DovizTut":0.0,"SAT_ISKT":0.0,"GENELTOPLAM":510.0,"YUVARLAMA":4.01,"MFAZ_ISKT":0.0,"GEN_ISK1O":0.0,"GEN_ISK2O":0.0,"GEN_ISK3O":0.0,"FAT_ALTM1":0.0,"FAT_ALTM2":0.0,"KS_KODU":"C-00","ODEMEGUNU":0,"ODEMETARIHI":"2025-06-03 00:00:00","ENTEGRE_TRH":"2025-06-03 00:00:00","KDV_DAHILMI":true,"PLA_KODU":"19","SIRANO":0,"DOVIZTIP":0,"FIYATTARIHI":"2025-06-03 09:23:03","GENISK1TIP":0,"GENISK2TIP":0,"GENISK3TIP":0,"EXPORTTYPE":0,"AMBHARTUR":4,"OnayTipi":"A","OnayNum":0,"GCKOD_GIRIS":0,"GCKOD_CIKIS":0,"GEN_ISK1T":0.0,"GEN_ISK2T":0.0,"GEN_ISK3T":0.0,"CikisYeri":4,"Degissin":0,"TopGirDepo":0,"TopDepo":0,"KOSVADEGUNU":0,"BrMaliyet":0.0,"BOLGE_FARKI_ISK":0.0,"KDV1T":0.0,"KDV1O":0.0,"KDV2O":0.0,"KDV2T":0.0,"KDV3O":0.0,"KDV3T":0.0,"KDV4O":0.0,"KDV4T":0.0,"KDV5O":0.0,"KDV5T":0.0,"EfaturaCarisiMi":true,"BaglantiNo":0,"EFatOzelKod":0,"OTV":0.0,"EIrsaliye":false,"HalFaturasi":0,"FAT_ALTM3":0.0,"DovBazTarihi":"2025-06-03 00:00:00","OTVTevTutar":0.0,"Konaklama":false},"EIrsEkBilgi":{},"HalFaturaMasraflari":{},"Use64BitService":false,"TransactSupport":true,"MuhasebelesmisBelge":false,"KalemAdedi":1,"FaturaTip":7,"SonNumaraYazilsin":true,"OtoIskontoGetir":true,"KosulluHesapla":false,"InternalObjectAddress":123816272,"SeriliHesapla":true,"FiyatSistemineGoreHesapla":false,"StokKartinaGoreHesapla":false,"OtoVadeGunGetir":true,"OtomatikIslemTipiGetir":false,"OtomatikOdemeKoduGetir":false,"MaliyetTipineGoreHesapla":false,"OtomatikCevrimYapilsin":false,"KayitliNumaraOtomatikGuncellensin":false,"Siralama":"\u0000","EPostaGonderilsin":true,"OtoNakliyeKatSayisiGetir":true,"OtoBolgeFarkIskGetir":true,"RiskKontrol":true,"TahsilatKalemAdedi":0,"TahsilatKayitKullan":false,"AcikBelgeTahsilat":false,"BaglantiKontrol":true,"FaturaOtvTevkifatHesaplansin":false,"YuklemeGunuHesapla":false,"Kalems":[{"DovizAdi":"USD","STra_DovizAdi":"TL","KalemSeri":[],"Asorti":null,"KosulMalFazlasiIsle":false,"StokKodu":"1274C171","Sira":1,"STra_FATIRSNO":"F00002025000836","STra_GCMIK":1.0,"STra_GCMIK2":0.0,"CEVRIM":0.0,"STra_TAR":"2025-06-03 00:00:00","STra_NF":459.99090909,"STra_BF":505.99,"STra_IAF":0.0,"STra_KDV":10.0,"STra_SatIsk":0.0,"STra_SatIsk2":0.0,"STra_MALFISK":0.0,"STra_HTUR":"H","STra_DOVTIP":0,"PROMASYON_KODU":0,"STra_DOVFIAT":0.0,"STra_ODEGUN":0,"STra_KOD1":"\u0000","STra_KOD2":"\u0000","STra_SIP_TURU":"S","Plasiyer_Kodu":"19","Ekalanneden":"\u0000","Stra_Otv":0.0,"Redneden":0,"STra_SIPKONT":1,"Firmadovtip":0,"Firmadovtut":0.0,"Firmadovmal":0.0,"Update_Kodu":"\u0000","Ecza_fat_tip":0,"Olcubr":1,"Vadetar":"2025-06-03 00:00:00","BaglantiNo":0,"BrCevrim1":1.0,"BrCevrim2":1.0,"Yed_Bf":505.99,"STra_BGTIP":"I","ReferansKodu":"21294-1","C_Yedek6":" ","STra_FTIRSIP":"6","STra_CARI_KOD":"12000104118","STra_GC":"C","DEPO_KODU":203,"Gir_Depo_Kodu":0,"STra_ACIK":"12000104118","Stra_OnayTipi":"A","Stra_OnayNum":0,"Stra_SubeKodu":203,"Stok_IsletmeKod":-1,"Stok_SubeKod":-1,"Stra_Exporttype":0,"IncKeyNo":73342,"IncKeyNo2":0,"TesMik":0.0,"TesMFMik":0.0,"MALADI":"Veneto Cap","STOK_GRKOD":"1001","STMUHDKOD":152,"SONGIRBFIAT":0.0,"OBR1":"AD","SabitDepKod":203,"DOVTIP":1,"DOVIZ_TURU":0,"Fiyatlar1":0.0,"Fiyatlar2":0.0,"Fiyatlar3":0.0,"Fiyatlar4":0.0,"Fiyatlar5":0.0,"Fiyatlar6":0.0,"Fiyatlar7":0.0,"Kilit":"\u0000","SatisKDVOran":10.0,"AlisKDVOran":10.0,"Isk_Flag":0,"SipTesKont":0,"Mamulmu":"H","SeriTakibi":"H","Stra_Exportmik":0.0,"STra_SatIsk3":0.0,"Stra_FiyatTar":"2025-06-03 00:00:00","Kul_Mik":0.0,"Fiat_birimi":1,"Sat_IskTipleri1":0,"Sat_IskTipleri2":0,"Sat_IskTipleri3":0,"Koli_Inc":0,"KoliStok":false,"Tur":"D","Stra_FiiliTar":"1900-01-01 00:00:00","BirimPuan":0,"PuanDeger":0.0,"KalemGenIskOran1":0.0,"KalemGenIskOran2":0.0,"KalemGenIskOran3":0.0,"OtvFlag":0,"Otvtut":0.0,"STra_SatIsk4":0.0,"STra_SatIsk5":0.0,"STra_SatIsk6":0.0,"Kull1S":"8680797908684","KKMalF":0.0,"Stra_FiyatBirimi":1,"Stra_IrsKont":0,"SatisKilit":"H","Payda_1":1.0,"D_YEDEK10":"1900-01-01 00:00:00","Sat_IskTipleri4":0,"Sat_IskTipleri5":0,"Sat_IskTipleri6":0,"EsnekMi":false,"SeriSayisi":0,"Stra_OTVFiat":0.0,"BolgeFark":0.0,"GEKAPTutar":0.0,"GEKAPAmbTutar":0.0,"KalemStopajOran":0.0}]}}
2025-06-03 17:42:07.958 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: true, ErrorCode: null, ErrorDesc: null
2025-06-03 17:42:07.960 +03:00 [INF] ✅ RestAPI SUCCESS for 21294
2025-06-03 17:42:07.964 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RestAPI_OK:21294
2025-06-03 17:42:07.965 +03:00 [INF] OrderNumber 21294 (Client ID: 953) successfully processed by ERP. Result: RestAPI_OK:21294
2025-06-03 17:42:07.968 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:42:07.970 +03:00 [INF] Loaded last cari kod suffix from file: 607
2025-06-03 17:42:07.970 +03:00 [INF] Processing OrderNumber: 21293 (Client ID: 952) with ERPService.
2025-06-03 17:42:07.975 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:42:07.976 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21293
2025-06-03 17:42:07.978 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21293 for Netsis integration.
2025-06-03 17:42:07.980 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:42:07.983 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:42:07.984 +03:00 [INF] Creating cari with data - Name: Efe Değirmenci, Email: NULL, Phone: +905393612148, City: İstanbul, Address: Aşık Veysel Mah. 3001. Cad.
2025-06-03 17:42:07.990 +03:00 [DBG] Saved last cari kod suffix to file: 608
2025-06-03 17:42:07.993 +03:00 [INF] Generated new Cari Kod: 120062327608 (suffix: 608, timestamp: 1748961727)
2025-06-03 17:42:07.997 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120062327608, Unvan: Efe Değirmenci
2025-06-03 17:42:08.000 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:08.003 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:08.005 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062327608","CARI_ISIM":"Efe De\u011Firmenci","CARI_TIP":"A","ADRES1":"A\u015F\u0131k Veysel Mah. 3001. Cad.","ADRES2":"Y\u0131lmaz Apt. No: 142/6","IL":"\u0130stanbul","ILCE":"Ata\u015Fehir","ULKE_KODU":"TR","POSTA_KODU":"34707","TELEFON1":"\u002B905393612148","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120062327608","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:42:08.046 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062327607","ULKE_KODU":"TR","CARI_ISIM":"Didem Düzenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120062327607","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:42:08.054 +03:00 [INF] Successfully created Netsis cari. CariKod: 120062327607 (attempt 1)
2025-06-03 17:42:08.059 +03:00 [INF] ✅ Successfully created cari: 120062327607 for customer: Didem Düzenci
2025-06-03 17:42:08.062 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120062327607
2025-06-03 17:42:08.064 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21298 with CariKod: 120062327607
2025-06-03 17:42:08.070 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:42:08.072 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:08.075 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:08.077 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:42:08.617 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1"}]}
2025-06-03 17:42:08.617 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062327608","ULKE_KODU":"TR","CARI_ISIM":"Efe Değirmenci","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120062327608","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:42:08.622 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 17:42:08.633 +03:00 [INF] Successfully created Netsis cari. CariKod: 120062327608 (attempt 1)
2025-06-03 17:42:08.635 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 17:42:08.639 +03:00 [INF] ✅ Successfully created cari: 120062327608 for customer: Efe Değirmenci
2025-06-03 17:42:08.640 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 17:42:08.643 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120062327608
2025-06-03 17:42:08.644 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 17:42:08.649 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21293 with CariKod: 120062327608
2025-06-03 17:42:08.650 +03:00 [INF] 🔍 Content-Length: 503
2025-06-03 17:42:08.654 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:42:08.656 +03:00 [INF] 🔍 JSON Payload Length: 503
2025-06-03 17:42:08.659 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:08.661 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 17:42:08.662 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:08.664 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 17:42:08.669 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:42:08.671 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1"}]}
2025-06-03 17:42:08.851 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 08:52:43","FIYATTARIHI":"2025-06-03 08:52:43","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"8207C001","STra_GCMIK":1,"STra_NF":3541.32,"STra_BF":3541.32,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 08:52:43","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21293-1"}]}
2025-06-03 17:42:08.855 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 17:42:08.860 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 17:42:08.864 +03:00 [INF] 🔍 Content length check: 503 chars
2025-06-03 17:42:08.866 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 17:42:08.867 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 17:42:08.869 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 17:42:08.870 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 17:42:08.874 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 17:42:08.878 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:46:35","FIYATTARIHI":"2025-06-03 10:46:35","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1235C017","STra_GCMIK":1,"STra_NF":803.99,"STra_BF":803.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:46:35","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21298-1"}]}
2025-06-03 17:42:08.881 +03:00 [INF] 🔍 Content-Length: 505
2025-06-03 17:42:08.889 +03:00 [INF] 🔍 JSON Payload Length: 505
2025-06-03 17:42:08.890 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 17:42:08.892 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 17:42:08.896 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 08:52:43","FIYATTARIHI":"2025-06-03 08:52:43","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"8207C001","STra_GCMIK":1,"STra_NF":3541.32,"STra_BF":3541.32,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 08:52:43","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21293-1"}]}
2025-06-03 17:42:08.901 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 17:42:08.903 +03:00 [INF] 🔍 Content length check: 505 chars
2025-06-03 17:42:08.906 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 17:42:08.908 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 17:42:08.910 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 08:52:43","FIYATTARIHI":"2025-06-03 08:52:43","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"8207C001","STra_GCMIK":1,"STra_NF":3541.32,"STra_BF":3541.32,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 08:52:43","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21293-1"}]}
2025-06-03 17:42:10.236 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 17:42:10.238 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 17:42:10.241 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 17:42:10.245 +03:00 [INF] 🔍 Response Content-Length: 4440
2025-06-03 17:42:10.248 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 17:42:10.250 +03:00 [INF] 🔍 Response Body Length: 4440
2025-06-03 17:42:10.252 +03:00 [INF] 🔍 Response Body: {"Meta":{"Href":"http://localhost:7070/api/v2/ItemSlips?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"Seri":null,"FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","FATIRS_NO":"F00002025000837","Tarih":"2025-06-03 00:00:00","Tip":7,"KOD1":"\u0000","YEDEK":"\u0000","KOD2":"\u0000","TIPI":1,"BRUTTUTAR":730.9,"KDV":73.09,"DovizTut":0.0,"SAT_ISKT":0.0,"GENELTOPLAM":800.0,"YUVARLAMA":-3.99,"MFAZ_ISKT":0.0,"GEN_ISK1O":0.0,"GEN_ISK2O":0.0,"GEN_ISK3O":0.0,"FAT_ALTM1":0.0,"FAT_ALTM2":0.0,"KS_KODU":"C-00","ODEMEGUNU":0,"ODEMETARIHI":"2025-06-03 00:00:00","ENTEGRE_TRH":"2025-06-03 00:00:00","KDV_DAHILMI":true,"PLA_KODU":"19","SIRANO":0,"DOVIZTIP":0,"FIYATTARIHI":"2025-06-03 10:46:35","GENISK1TIP":0,"GENISK2TIP":0,"GENISK3TIP":0,"EXPORTTYPE":0,"AMBHARTUR":4,"OnayTipi":"A","OnayNum":0,"GCKOD_GIRIS":0,"GCKOD_CIKIS":0,"GEN_ISK1T":0.0,"GEN_ISK2T":0.0,"GEN_ISK3T":0.0,"CikisYeri":4,"Degissin":0,"TopGirDepo":0,"TopDepo":0,"KOSVADEGUNU":0,"BrMaliyet":0.0,"BOLGE_FARKI_ISK":0.0,"KDV1T":0.0,"KDV1O":0.0,"KDV2O":0.0,"KDV2T":0.0,"KDV3O":0.0,"KDV3T":0.0,"KDV4O":0.0,"KDV4T":0.0,"KDV5O":0.0,"KDV5T":0.0,"EfaturaCarisiMi":true,"BaglantiNo":0,"EFatOzelKod":0,"OTV":0.0,"EIrsaliye":false,"HalFaturasi":0,"FAT_ALTM3":0.0,"DovBazTarihi":"2025-06-03 00:00:00","OTVTevTutar":0.0,"Konaklama":false},"EIrsEkBilgi":{},"HalFaturaMasraflari":{},"Use64BitService":false,"TransactSupport":true,"MuhasebelesmisBelge":false,"KalemAdedi":1,"FaturaTip":7,"SonNumaraYazilsin":true,"OtoIskontoGetir":true,"KosulluHesapla":false,"InternalObjectAddress":123816272,"SeriliHesapla":true,"FiyatSistemineGoreHesapla":false,"StokKartinaGoreHesapla":false,"OtoVadeGunGetir":true,"OtomatikIslemTipiGetir":false,"OtomatikOdemeKoduGetir":false,"MaliyetTipineGoreHesapla":false,"OtomatikCevrimYapilsin":false,"KayitliNumaraOtomatikGuncellensin":false,"Siralama":"\u0000","EPostaGonderilsin":true,"OtoNakliyeKatSayisiGetir":true,"OtoBolgeFarkIskGetir":true,"RiskKontrol":true,"TahsilatKalemAdedi":0,"TahsilatKayitKullan":false,"AcikBelgeTahsilat":false,"BaglantiKontrol":true,"FaturaOtvTevkifatHesaplansin":false,"YuklemeGunuHesapla":false,"Kalems":[{"DovizAdi":"USD","STra_DovizAdi":"TL","KalemSeri":[],"Asorti":null,"KosulMalFazlasiIsle":false,"StokKodu":"1235C017","Sira":1,"STra_FATIRSNO":"F00002025000837","STra_GCMIK":1.0,"STra_GCMIK2":0.0,"CEVRIM":0.0,"STra_TAR":"2025-06-03 00:00:00","STra_NF":730.9,"STra_BF":803.99,"STra_IAF":0.0,"STra_KDV":10.0,"STra_SatIsk":0.0,"STra_SatIsk2":0.0,"STra_MALFISK":0.0,"STra_HTUR":"H","STra_DOVTIP":0,"PROMASYON_KODU":0,"STra_DOVFIAT":0.0,"STra_ODEGUN":0,"STra_KOD1":"\u0000","STra_KOD2":"\u0000","STra_SIP_TURU":"S","Plasiyer_Kodu":"19","Ekalanneden":"\u0000","Stra_Otv":0.0,"Redneden":0,"STra_SIPKONT":1,"Firmadovtip":0,"Firmadovtut":0.0,"Firmadovmal":0.0,"Update_Kodu":"\u0000","Ecza_fat_tip":0,"Olcubr":1,"Vadetar":"2025-06-03 00:00:00","BaglantiNo":0,"BrCevrim1":1.0,"BrCevrim2":1.0,"Yed_Bf":803.99,"STra_BGTIP":"I","ReferansKodu":"21298-1","C_Yedek6":" ","STra_FTIRSIP":"6","STra_CARI_KOD":"12000104118","STra_GC":"C","DEPO_KODU":203,"Gir_Depo_Kodu":0,"STra_ACIK":"12000104118","Stra_OnayTipi":"A","Stra_OnayNum":0,"Stra_SubeKodu":203,"Stok_IsletmeKod":-1,"Stok_SubeKod":-1,"Stra_Exporttype":0,"IncKeyNo":73343,"IncKeyNo2":0,"TesMik":0.0,"TesMFMik":0.0,"MALADI":"Wayla Scarf","STOK_GRKOD":"1001","STMUHDKOD":153,"SONGIRBFIAT":0.0,"OBR1":"AD","SabitDepKod":203,"DOVTIP":1,"DOVIZ_TURU":0,"Fiyatlar1":0.0,"Fiyatlar2":0.0,"Fiyatlar3":0.0,"Fiyatlar4":0.0,"Fiyatlar5":0.0,"Fiyatlar6":0.0,"Fiyatlar7":0.0,"Kilit":"\u0000","SatisKDVOran":10.0,"AlisKDVOran":10.0,"Isk_Flag":0,"SipTesKont":0,"Mamulmu":"H","SeriTakibi":"H","Stra_Exportmik":0.0,"STra_SatIsk3":0.0,"Stra_FiyatTar":"2025-06-03 00:00:00","Kul_Mik":0.0,"Fiat_birimi":1,"Sat_IskTipleri1":0,"Sat_IskTipleri2":0,"Sat_IskTipleri3":0,"Koli_Inc":0,"KoliStok":false,"Tur":"D","Stra_FiiliTar":"1900-01-01 00:00:00","BirimPuan":0,"PuanDeger":0.0,"KalemGenIskOran1":0.0,"KalemGenIskOran2":0.0,"KalemGenIskOran3":0.0,"OtvFlag":0,"Otvtut":0.0,"STra_SatIsk4":0.0,"STra_SatIsk5":0.0,"STra_SatIsk6":0.0,"Kull1S":"8680797209217","KKMalF":0.0,"Stra_FiyatBirimi":1,"Stra_IrsKont":0,"SatisKilit":"H","Payda_1":1.0,"D_YEDEK10":"1900-01-01 00:00:00","Sat_IskTipleri4":0,"Sat_IskTipleri5":0,"Sat_IskTipleri6":0,"EsnekMi":false,"SeriSayisi":0,"Stra_OTVFiat":0.0,"BolgeFark":0.0,"GEKAPTutar":0.0,"GEKAPAmbTutar":0.0,"KalemStopajOran":0.0}]}}
2025-06-03 17:42:10.276 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: true, ErrorCode: null, ErrorDesc: null
2025-06-03 17:42:10.279 +03:00 [INF] ✅ RestAPI SUCCESS for 21298
2025-06-03 17:42:10.281 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RestAPI_OK:21298
2025-06-03 17:42:10.284 +03:00 [INF] OrderNumber 21298 (Client ID: 957) successfully processed by ERP. Result: RestAPI_OK:21298
2025-06-03 17:42:10.288 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:42:10.290 +03:00 [INF] Loaded last cari kod suffix from file: 608
2025-06-03 17:42:10.291 +03:00 [INF] Processing OrderNumber: 21297 (Client ID: 956) with ERPService.
2025-06-03 17:42:10.296 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:42:10.298 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21297
2025-06-03 17:42:10.299 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21297 for Netsis integration.
2025-06-03 17:42:10.301 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:42:10.304 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:42:10.305 +03:00 [INF] Creating cari with data - Name: şerife arıkan, Email: NULL, Phone: , City: İstanbul, Address: Yunus Emre Mah (AYŞE ÇARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / İSTANBUL
2025-06-03 17:42:10.311 +03:00 [DBG] Saved last cari kod suffix to file: 609
2025-06-03 17:42:10.313 +03:00 [INF] Generated new Cari Kod: 120062330609 (suffix: 609, timestamp: 1748961730)
2025-06-03 17:42:10.315 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120062330609, Unvan: şerife arıkan
2025-06-03 17:42:10.318 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:10.320 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:10.323 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062330609","CARI_ISIM":"\u015Ferife ar\u0131kan","CARI_TIP":"A","ADRES1":"Yunus Emre Mah (AY\u015EE \u00C7ARMIKLI ORTAOKULU ) Yunus Emre Mahallesi Veysel Karani Caddesi Metehan Sokak No 6 Sancaktepe / \u0130STANBUL","ADRES2":"","IL":"\u0130stanbul","ILCE":"Sancaktepe","ULKE_KODU":"TR","POSTA_KODU":"34000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120062330609","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:42:12.005 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062330609","ULKE_KODU":"TR","CARI_ISIM":"şerife arıkan","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120062330609","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:42:12.015 +03:00 [INF] Successfully created Netsis cari. CariKod: 120062330609 (attempt 1)
2025-06-03 17:42:12.020 +03:00 [INF] ✅ Successfully created cari: 120062330609 for customer: şerife arıkan
2025-06-03 17:42:12.023 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120062330609
2025-06-03 17:42:12.025 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21297 with CariKod: 120062330609
2025-06-03 17:42:12.029 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:42:12.030 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:12.031 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:12.032 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
2025-06-03 17:42:13.687 +03:00 [DBG] 🎯 RestAPI JSON: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:40:08","FIYATTARIHI":"2025-06-03 10:40:08","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C060","STra_GCMIK":1,"STra_NF":603.99,"STra_BF":603.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:40:08","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21297-1"}]}
2025-06-03 17:42:13.687 +03:00 [INF] 🚨 HTTP POST REQUEST COMPLETED. Status: "OK"
2025-06-03 17:42:13.693 +03:00 [INF] 🔍 =================== HTTP REQUEST DETAILS ===================
2025-06-03 17:42:13.700 +03:00 [INF] 🔍 Request URL: http://localhost:7070/api/v2/ItemSlips
2025-06-03 17:42:13.695 +03:00 [INF] 🔍 =================== RESPONSE DETAILS ===================
2025-06-03 17:42:13.702 +03:00 [INF] 🔍 Request Method: POST
2025-06-03 17:42:13.705 +03:00 [INF] 🔍 Response Status: "OK" (OK)
2025-06-03 17:42:13.707 +03:00 [INF] 🔍 Content-Type: application/json; charset=utf-8
2025-06-03 17:42:13.711 +03:00 [INF] 🔍 Response Content-Length: 4452
2025-06-03 17:42:13.713 +03:00 [INF] 🔍 Content-Length: 503
2025-06-03 17:42:13.715 +03:00 [INF] 🔍 Response Content-Type: application/json; charset=utf-8
2025-06-03 17:42:13.717 +03:00 [INF] 🔍 JSON Payload Length: 503
2025-06-03 17:42:13.719 +03:00 [INF] 🔍 Response Body Length: 4452
2025-06-03 17:42:13.722 +03:00 [INF] 🔍 Authorization Header: Bearer AQAAANCMnd8BFdERjHoA...
2025-06-03 17:42:13.724 +03:00 [INF] 🔍 Response Body: {"Meta":{"Href":"http://localhost:7070/api/v2/ItemSlips?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"Seri":null,"FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","FATIRS_NO":"F00002025000838","Tarih":"2025-06-03 00:00:00","Tip":7,"KOD1":"\u0000","YEDEK":"\u0000","KOD2":"\u0000","TIPI":1,"BRUTTUTAR":3219.38,"KDV":321.94,"DovizTut":0.0,"SAT_ISKT":0.0,"GENELTOPLAM":3540.0,"YUVARLAMA":-1.32,"MFAZ_ISKT":0.0,"GEN_ISK1O":0.0,"GEN_ISK2O":0.0,"GEN_ISK3O":0.0,"FAT_ALTM1":0.0,"FAT_ALTM2":0.0,"KS_KODU":"C-00","ODEMEGUNU":0,"ODEMETARIHI":"2025-06-03 00:00:00","ENTEGRE_TRH":"2025-06-03 00:00:00","KDV_DAHILMI":true,"PLA_KODU":"19","SIRANO":0,"DOVIZTIP":0,"FIYATTARIHI":"2025-06-03 08:52:43","GENISK1TIP":0,"GENISK2TIP":0,"GENISK3TIP":0,"EXPORTTYPE":0,"AMBHARTUR":4,"OnayTipi":"A","OnayNum":0,"GCKOD_GIRIS":0,"GCKOD_CIKIS":0,"GEN_ISK1T":0.0,"GEN_ISK2T":0.0,"GEN_ISK3T":0.0,"CikisYeri":4,"Degissin":0,"TopGirDepo":0,"TopDepo":0,"KOSVADEGUNU":0,"BrMaliyet":0.0,"BOLGE_FARKI_ISK":0.0,"KDV1T":0.0,"KDV1O":0.0,"KDV2O":0.0,"KDV2T":0.0,"KDV3O":0.0,"KDV3T":0.0,"KDV4O":0.0,"KDV4T":0.0,"KDV5O":0.0,"KDV5T":0.0,"EfaturaCarisiMi":true,"BaglantiNo":0,"EFatOzelKod":0,"OTV":0.0,"EIrsaliye":false,"HalFaturasi":0,"FAT_ALTM3":0.0,"DovBazTarihi":"2025-06-03 00:00:00","OTVTevTutar":0.0,"Konaklama":false},"EIrsEkBilgi":{},"HalFaturaMasraflari":{},"Use64BitService":false,"TransactSupport":true,"MuhasebelesmisBelge":false,"KalemAdedi":1,"FaturaTip":7,"SonNumaraYazilsin":true,"OtoIskontoGetir":true,"KosulluHesapla":false,"InternalObjectAddress":123849184,"SeriliHesapla":true,"FiyatSistemineGoreHesapla":false,"StokKartinaGoreHesapla":false,"OtoVadeGunGetir":true,"OtomatikIslemTipiGetir":false,"OtomatikOdemeKoduGetir":false,"MaliyetTipineGoreHesapla":false,"OtomatikCevrimYapilsin":false,"KayitliNumaraOtomatikGuncellensin":false,"Siralama":"\u0000","EPostaGonderilsin":true,"OtoNakliyeKatSayisiGetir":true,"OtoBolgeFarkIskGetir":true,"RiskKontrol":true,"TahsilatKalemAdedi":0,"TahsilatKayitKullan":false,"AcikBelgeTahsilat":false,"BaglantiKontrol":true,"FaturaOtvTevkifatHesaplansin":false,"YuklemeGunuHesapla":false,"Kalems":[{"DovizAdi":"USD","STra_DovizAdi":"TL","KalemSeri":[],"Asorti":null,"KosulMalFazlasiIsle":false,"StokKodu":"8207C001","Sira":1,"STra_FATIRSNO":"F00002025000838","STra_GCMIK":1.0,"STra_GCMIK2":0.0,"CEVRIM":0.0,"STra_TAR":"2025-06-03 00:00:00","STra_NF":3219.38181818,"STra_BF":3541.32,"STra_IAF":0.0,"STra_KDV":10.0,"STra_SatIsk":0.0,"STra_SatIsk2":0.0,"STra_MALFISK":0.0,"STra_HTUR":"H","STra_DOVTIP":0,"PROMASYON_KODU":0,"STra_DOVFIAT":0.0,"STra_ODEGUN":0,"STra_KOD1":"\u0000","STra_KOD2":"\u0000","STra_SIP_TURU":"S","Plasiyer_Kodu":"19","Ekalanneden":"\u0000","Stra_Otv":0.0,"Redneden":0,"STra_SIPKONT":1,"Firmadovtip":0,"Firmadovtut":0.0,"Firmadovmal":0.0,"Update_Kodu":"\u0000","Ecza_fat_tip":0,"Olcubr":1,"Vadetar":"2025-06-03 00:00:00","BaglantiNo":0,"BrCevrim1":1.0,"BrCevrim2":1.0,"Yed_Bf":3541.32,"STra_BGTIP":"I","ReferansKodu":"21293-1","C_Yedek6":" ","STra_FTIRSIP":"6","STra_CARI_KOD":"12000104118","STra_GC":"C","DEPO_KODU":203,"Gir_Depo_Kodu":0,"STra_ACIK":"12000104118","Stra_OnayTipi":"A","Stra_OnayNum":0,"Stra_SubeKodu":203,"Stok_IsletmeKod":-1,"Stok_SubeKod":-1,"Stra_Exporttype":0,"IncKeyNo":73344,"IncKeyNo2":0,"TesMik":0.0,"TesMFMik":0.0,"MALADI":"Solna Bag","STOK_GRKOD":"1000","STMUHDKOD":153,"SONGIRBFIAT":0.0,"OBR1":"AD","SabitDepKod":203,"DOVTIP":1,"DOVIZ_TURU":0,"Fiyatlar1":0.0,"Fiyatlar2":0.0,"Fiyatlar3":0.0,"Fiyatlar4":0.0,"Fiyatlar5":0.0,"Fiyatlar6":0.0,"Fiyatlar7":0.0,"Kilit":"\u0000","SatisKDVOran":10.0,"AlisKDVOran":10.0,"Isk_Flag":0,"SipTesKont":0,"Mamulmu":"H","SeriTakibi":"H","Stra_Exportmik":0.0,"STra_SatIsk3":0.0,"Stra_FiyatTar":"2025-06-03 00:00:00","Kul_Mik":0.0,"Fiat_birimi":1,"Sat_IskTipleri1":0,"Sat_IskTipleri2":0,"Sat_IskTipleri3":0,"Koli_Inc":0,"KoliStok":false,"Tur":"D","Stra_FiiliTar":"1900-01-01 00:00:00","BirimPuan":0,"PuanDeger":0.0,"KalemGenIskOran1":0.0,"KalemGenIskOran2":0.0,"KalemGenIskOran3":0.0,"OtvFlag":0,"Otvtut":0.0,"STra_SatIsk4":0.0,"STra_SatIsk5":0.0,"STra_SatIsk6":0.0,"Kull1S":"8680797137497","KKMalF":0.0,"Stra_FiyatBirimi":1,"Stra_IrsKont":0,"SatisKilit":"H","Payda_1":1.0,"D_YEDEK10":"1900-01-01 00:00:00","Sat_IskTipleri4":0,"Sat_IskTipleri5":0,"Sat_IskTipleri6":0,"EsnekMi":false,"SeriSayisi":0,"Stra_OTVFiat":0.0,"BolgeFark":0.0,"GEKAPTutar":0.0,"GEKAPAmbTutar":0.0,"KalemStopajOran":0.0}]}}
2025-06-03 17:42:13.726 +03:00 [INF] 🔍 Accept Headers: application/json
2025-06-03 17:42:13.748 +03:00 [INF] 🔍 Parsed API Response - IsSuccessful: true, ErrorCode: null, ErrorDesc: null
2025-06-03 17:42:13.752 +03:00 [INF] 🔍 ACTUAL CONTENT BEING SENT: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:40:08","FIYATTARIHI":"2025-06-03 10:40:08","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C060","STra_GCMIK":1,"STra_NF":603.99,"STra_BF":603.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:40:08","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21297-1"}]}
2025-06-03 17:42:13.755 +03:00 [INF] ✅ RestAPI SUCCESS for 21293
2025-06-03 17:42:13.758 +03:00 [INF] 🔍 Content is empty: false
2025-06-03 17:42:13.761 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync returned: RestAPI_OK:21293
2025-06-03 17:42:13.763 +03:00 [INF] 🔍 Content length check: 503 chars
2025-06-03 17:42:13.765 +03:00 [INF] OrderNumber 21293 (Client ID: 952) successfully processed by ERP. Result: RestAPI_OK:21293
2025-06-03 17:42:13.767 +03:00 [INF] 🔍 Content ready for actual request
2025-06-03 17:42:13.769 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-06-03 17:42:13.771 +03:00 [INF] Loaded last cari kod suffix from file: 609
2025-06-03 17:42:13.773 +03:00 [INF] 🚨 =================== SENDING HTTP POST REQUEST ===================
2025-06-03 17:42:13.774 +03:00 [INF] Processing OrderNumber: 21292 (Client ID: 951) with ERPService.
2025-06-03 17:42:13.777 +03:00 [INF] 🚨 CONTENT BODY: {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"12000104118","Tarih":"2025-06-03 10:40:08","FIYATTARIHI":"2025-06-03 10:40:08","Tip":7,"TIPI":1,"DEPO_KODU":203,"KDV_DAHILMI":true,"YUVARLAMA":0},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C060","STra_GCMIK":1,"STra_NF":603.99,"STra_BF":603.99,"STra_KOD1":"Y","STra_KDV":10,"STra_TAR":"2025-06-03 10:40:08","STra_ACIKLAMA":"Online Order Item","DEPO_KODU":203,"Olcubr":1,"CEVRIM":1,"ReferansKodu":"21297-1"}]}
2025-06-03 17:42:13.778 +03:00 [INF] 🚨 DEBUG: About to call CreateNetsisOrderAsync...
2025-06-03 17:42:13.785 +03:00 [INF] 🚨 DEBUG: CreateNetsisOrderAsync STARTED for order 21292
2025-06-03 17:42:13.787 +03:00 [INF] 🎯 API EXAMPLE TEST Processing order 21292 for Netsis integration.
2025-06-03 17:42:13.789 +03:00 [INF] 🚨 DEBUG: Starting cari search/create process...
2025-06-03 17:42:13.791 +03:00 [INF] 🚨 DEBUG: Creating new cari...
2025-06-03 17:42:13.795 +03:00 [INF] Creating cari with data - Name: Tuğba Bilgeç, Email: NULL, Phone: , City: Konya, Address: Şeker Mah Şeker mahallesi İstiklal sokak No:1 Kat:1 Daire:1 Selçuklu/Konya
2025-06-03 17:42:13.801 +03:00 [DBG] Saved last cari kod suffix to file: 610
2025-06-03 17:42:13.805 +03:00 [INF] Generated new Cari Kod: 120062333610 (suffix: 610, timestamp: 1748961733)
2025-06-03 17:42:13.808 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120062333610, Unvan: Tuğba Bilgeç
2025-06-03 17:42:13.810 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:13.812 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:13.813 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062333610","CARI_ISIM":"Tu\u011Fba Bilge\u00E7","CARI_TIP":"A","ADRES1":"\u015Eeker Mah \u015Eeker mahallesi \u0130stiklal sokak No:1 Kat:1 Daire:1 Sel\u00E7uklu/Konya","ADRES2":"","IL":"Konya","ILCE":"Sel\u00E7uklu","ULKE_KODU":"TR","POSTA_KODU":"42000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120062333610","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-06-03 17:42:15.194 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120062333610","ULKE_KODU":"TR","CARI_ISIM":"Tuğba Bilgeç","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120062333610","KayitTarihi":"2025-06-03 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-06-03 17:42:15.203 +03:00 [INF] Successfully created Netsis cari. CariKod: 120062333610 (attempt 1)
2025-06-03 17:42:15.207 +03:00 [INF] ✅ Successfully created cari: 120062333610 for customer: Tuğba Bilgeç
2025-06-03 17:42:15.209 +03:00 [INF] 🚨 DEBUG: Cari process completed. CariKod: 120062333610
2025-06-03 17:42:15.210 +03:00 [INF] 🎯 Attempting API EXAMPLE Netsis order for OrderNumber: 21292 with CariKod: 120062333610
2025-06-03 17:42:15.212 +03:00 [INF] 🚨 DEBUG: Creating authenticated HTTP client...
2025-06-03 17:42:15.214 +03:00 [DBG] Using cached Netsis token.
2025-06-03 17:42:15.218 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-06-03 17:42:15.220 +03:00 [INF] 🚨 DEBUG: HTTP client created successfully
