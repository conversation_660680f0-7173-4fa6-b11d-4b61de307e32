{"format": 1, "restore": {"C:\\Qix\\XECOM Main-LocalServer\\XECOM Main-LocalServer.csproj": {}}, "projects": {"C:\\Qix\\XECOM Main-LocalServer\\XECOM Main-LocalServer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Qix\\XECOM Main-LocalServer\\XECOM Main-LocalServer.csproj", "projectName": "XECOM Main-LocalServer", "projectPath": "C:\\Qix\\XECOM Main-LocalServer\\XECOM Main-LocalServer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Qix\\XECOM Main-LocalServer\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.10, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.3, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}