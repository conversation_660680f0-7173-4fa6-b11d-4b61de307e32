2024-11-25 03:28:02.287 +03:00 [INF] Uygulama başlatılıyor
2024-11-25 03:28:02.417 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:28:03.697 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2024-11-25 03:28:03.731 +03:00 [ERR] An error occurred using the connection to database 'AKAL2024-04' on server '.'.
2024-11-25 03:28:03.740 +03:00 [ERR] Veri senkronizasyonu sırasında hata oluştu
Microsoft.Data.SqlClient.SqlException (0x80131904): <PERSON><PERSON> failed for user 'sa'.
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.CompleteLogin(Boolean enlistOK)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.AttemptOneLogin(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, Boolean ignoreSniOpenTimeout, TimeoutTimer timeout, Boolean withFailover)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.LoginNoFailover(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString connectionOptions, SqlCredential credential, TimeoutTimer timeout)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.OpenLoginEnlist(TimeoutTimer timeout, SqlConnectionString connectionOptions, SqlCredential credential, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken, DbConnectionPool pool)
   at Microsoft.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.WaitForPendingOpen()
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQueryAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.ExecuteSqlRawAsync(DatabaseFacade databaseFacade, String sql, IEnumerable`1 parameters, CancellationToken cancellationToken)
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteStoredProcedure() in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 81
   at XECOM_Main_LocalServer.Services.DataSyncService.ExecuteAsync(CancellationToken stoppingToken) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Services\DataSyncService.cs:line 43
ClientConnectionId:b00598e6-c24f-48b9-8b37-52273f326788
Error Number:18456,State:1,Class:14
2024-11-25 03:29:00.214 +03:00 [INF] Uygulama başlatılıyor
2024-11-25 03:29:00.282 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:29:01.196 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2024-11-25 03:29:01.320 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:29:01.321 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:29:02.634 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:29:02.768 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:30:02.772 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:30:02.848 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:30:02.850 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:30:03.611 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:30:03.654 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:31:03.659 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:31:03.700 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:31:03.702 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:31:04.380 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:31:04.418 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:32:04.421 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:32:04.441 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:32:04.441 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:32:05.081 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:32:05.104 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:33:05.120 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:33:05.141 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:33:05.142 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:33:05.810 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:33:05.848 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:34:05.849 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:34:05.877 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:34:05.878 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:34:06.522 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:34:06.541 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:35:06.548 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:35:06.571 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:35:06.572 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:35:17.826 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:35:17.856 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:36:17.860 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:36:17.896 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:36:17.897 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:36:18.593 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:36:18.608 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:37:18.619 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:37:18.638 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:37:18.639 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:37:27.994 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:37:28.022 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:38:28.036 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:38:28.050 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:38:28.051 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:38:28.708 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:38:28.729 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:42:32.571 +03:00 [INF] Uygulama başlatılıyor
2024-11-25 03:42:32.625 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:42:33.564 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2024-11-25 03:42:33.664 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:42:33.666 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:42:34.785 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:42:34.888 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2024-11-25 03:43:34.898 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2024-11-25 03:43:34.963 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başarıyla çalıştırıldı
2024-11-25 03:43:34.964 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2024-11-25 03:43:35.627 +03:00 [INF] Toplam ürün sayısı: 687
2024-11-25 03:43:35.663 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
