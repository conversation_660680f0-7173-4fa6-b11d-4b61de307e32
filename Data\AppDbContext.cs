﻿// XECOM_Main_LocalServer\Data\AppDbContext.cs
using Microsoft.EntityFrameworkCore;
using XECOM_Main_LocalServer.Models;

namespace XECOM_Main_LocalServer.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options)
            : base(options)
        {
        }

        public DbSet<Product> Products { get; set; }
        // ProductChange için: Eğer SQL sorgusundan dönen bir DTO ise DbSet'e gerek yok.
        // public DbSet<ProductChange> ProductChanges { get; set; }

        public DbSet<XmainOrder> XmainOrders { get; set; }
        public DbSet<XmainOrderLineItem> XmainOrderLineItems { get; set; }
        public DbSet<XmainAddress> XmainAddresses { get; set; }
        public DbSet<XmainOrderPackage> XmainOrderPackages { get; set; } // DbSet eklendi

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Product>(entity =>
            {
                entity.ToTable("AlkProductList2"); // Veya "Products" eğer farklı bir tablo adı ise
                // Eğer bu tabloyu migration'lar yönetmiyorsa ve sadece okunuyorsa:
                entity.Metadata.SetIsTableExcludedFromMigrations(true);
            });

            // XmainOrder konfigürasyonu
            modelBuilder.Entity<XmainOrder>(entity =>
            {
                entity.ToTable("XmainOrders"); // Tablo adını belirleyin

                // OrderNumber için index (opsiyonel ama sorgularda performansı artırabilir)
                entity.HasIndex(o => o.OrderNumber).IsUnique(false); // Eğer OrderNumber unique değilse false

                // XmainOrder -> XmainOrderLineItem ilişkisi (Bire Çok)
                entity.HasMany(o => o.OrderLineItems)
                      .WithOne(li => li.Order)
                      .HasForeignKey(li => li.OrderId)
                      .OnDelete(DeleteBehavior.Cascade); // Bir sipariş silinince kalemleri de silinsin

                // XmainOrder -> XmainOrderPackage ilişkisi (Bire Çok)
                entity.HasMany(o => o.OrderPackages)
                      .WithOne(op => op.Order)
                      .HasForeignKey(op => op.OrderId)
                      .OnDelete(DeleteBehavior.Cascade);

                // XmainOrder -> BillingAddress (XmainAddress) ilişkisi (Bire Bir veya Bire Çok)
                entity.HasOne(o => o.BillingAddress)
                      .WithMany(a => a.OrdersAsBillingAddress) // XmainAddress'e geri navigasyon eklendi
                      .HasForeignKey(o => o.BillingAddressId)
                      .OnDelete(DeleteBehavior.SetNull); // Adres silinirse siparişteki ID null olsun (veya Restrict)

                // XmainOrder -> ShippingAddress (XmainAddress) ilişkisi
                entity.HasOne(o => o.ShippingAddress)
                      .WithMany(a => a.OrdersAsShippingAddress) // XmainAddress'e geri navigasyon eklendi
                      .HasForeignKey(o => o.ShippingAddressId)
                      .OnDelete(DeleteBehavior.SetNull); // Adres silinirse siparişteki ID null olsun (veya Restrict)
            });

            // XmainAddress konfigürasyonu
            modelBuilder.Entity<XmainAddress>(entity =>
            {
                entity.ToTable("XmainAddresses"); // Tablo adını belirleyin
            });

            // XmainOrderLineItem konfigürasyonu
            modelBuilder.Entity<XmainOrderLineItem>(entity =>
            {
                entity.ToTable("XmainOrderLineItems"); // Tablo adını belirleyin
                // Fiyat alanları için modelde [Column(TypeName = "decimal(18,4)")] kullandık, burada tekrar gerek yok.
            });

            // XmainOrderPackage konfigürasyonu
            modelBuilder.Entity<XmainOrderPackage>(entity =>
            {
                entity.ToTable("XmainOrderPackages"); // Tablo adını belirleyin
            });

            // ProductChange için özel konfigürasyon (eğer bir entity ise ve DbSet'e eklendiyse)
            // modelBuilder.Entity<ProductChange>(entity =>
            // {
            //    entity.HasNoKey(); // Eğer birincil anahtarı yoksa ve sadece sorgu sonucuysa
            //    entity.ToView("vw_ProductChanges"); // Eğer bir view ise
            // });
        }

        // OnConfiguring metodunuzu olduğu gibi bırakıyorum,
        // ancak connection string'in appsettings.json'dan okunması daha iyi bir pratiktir (Program.cs'de yapıldığı gibi).
        /*
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder
                    .UseSqlServer("Server=.;Database=AKAL2024-04;User Id=sa;Password=********;TrustServerCertificate=True;")
                    .EnableSensitiveDataLogging()
                    .EnableDetailedErrors();
            }
        }
        */
    }
}