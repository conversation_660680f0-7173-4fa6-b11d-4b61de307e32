﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using XECOM_Main_LocalServer.Data;

#nullable disable

namespace XECOM_Main_LocalServer.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("XECOM_Main_LocalServer.Models.Product", b =>
                {
                    b.Property<long?>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long?>("ID"));

                    b.Property<string>("Assortment_type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Brand")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Color_Code_Primary")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Discount_Price")
                        .HasColumnType("decimal(18,8)");

                    b.Property<string>("EAN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Material_Upper_Material_Clothing1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Material_Upper_Material_Clothing1_Ratio")
                        .HasColumnType("int");

                    b.Property<string>("Material_Upper_Material_Clothing2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Material_Upper_Material_Clothing2_Ratio")
                        .HasColumnType("int");

                    b.Property<string>("Material_Upper_Material_Clothing3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Material_Upper_Material_Clothing3_Ratio")
                        .HasColumnType("int");

                    b.Property<string>("Material_Upper_Material_Clothing4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Material_Upper_Material_Clothing4_Ratio")
                        .HasColumnType("int");

                    b.Property<string>("Material_Upper_Material_Clothing5")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Material_Upper_Material_Clothing5_Ratio")
                        .HasColumnType("int");

                    b.Property<string>("Media1_URL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Media2_URL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Media3_URL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Media4_URL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Media5_URL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model_ID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Regular_Price")
                        .HasColumnType("decimal(18,8)");

                    b.Property<string>("SKU_ID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Season")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Size")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Size1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Stock_Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("Stock_Last_Update")
                        .HasColumnType("datetime2")
                        .HasColumnName("stok_last_update");

                    b.Property<decimal?>("Stock_Quantity")
                        .HasColumnType("decimal(18,8)");

                    b.Property<string>("Supplier_Color")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Target_Age_Groups")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Target_Genders")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("AlkProductList2", null, t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("XECOM_Main_LocalServer.Models.XmainAddress", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AddressLine1")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine2")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Company")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DistrictName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IdentityNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StateName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TaxNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TaxOffice")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("xmain_Addresses");
                });

            modelBuilder.Entity("XECOM_Main_LocalServer.Models.XmainOrder", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long?>("BillingAddressId")
                        .HasColumnType("bigint");

                    b.Property<string>("CancelReason")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CancelledAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ClientIp")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("GiftPackageNote")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Host")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IkasId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("InsertedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsGiftPackage")
                        .HasColumnType("bit");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderPackageStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderPaymentStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("OrderSequence")
                        .HasColumnType("int");

                    b.Property<DateTime?>("OrderedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("RecordDate")
                        .HasColumnType("datetime2");

                    b.Property<long?>("ShippingAddressId")
                        .HasColumnType("bigint");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TotalFinalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BillingAddressId");

                    b.HasIndex("ShippingAddressId");

                    b.ToTable("xmain_Orders");
                });

            modelBuilder.Entity("XECOM_Main_LocalServer.Models.XmainOrderLineItem", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<decimal?>("FinalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("IkasId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("VariantName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VariantSku")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("xmain_OrderLineItems");
                });

            modelBuilder.Entity("XECOM_Main_LocalServer.Models.XmainOrderPackage", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IkasId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("OrderLineItemIds")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderPackageFulfillStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderPackageNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockLocationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TrackingBarcode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TrackingCargoCompany")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TrackingLink")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TrackingNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.ToTable("xmain_OrderPackages");
                });

            modelBuilder.Entity("XECOM_Main_LocalServer.Models.XmainOrder", b =>
                {
                    b.HasOne("XECOM_Main_LocalServer.Models.XmainAddress", "BillingAddress")
                        .WithMany()
                        .HasForeignKey("BillingAddressId");

                    b.HasOne("XECOM_Main_LocalServer.Models.XmainAddress", "ShippingAddress")
                        .WithMany()
                        .HasForeignKey("ShippingAddressId");

                    b.Navigation("BillingAddress");

                    b.Navigation("ShippingAddress");
                });

            modelBuilder.Entity("XECOM_Main_LocalServer.Models.XmainOrderLineItem", b =>
                {
                    b.HasOne("XECOM_Main_LocalServer.Models.XmainOrder", "Order")
                        .WithMany("OrderLineItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("XECOM_Main_LocalServer.Models.XmainOrderPackage", b =>
                {
                    b.HasOne("XECOM_Main_LocalServer.Models.XmainOrder", "Order")
                        .WithMany("OrderPackages")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("XECOM_Main_LocalServer.Models.XmainOrder", b =>
                {
                    b.Navigation("OrderLineItems");

                    b.Navigation("OrderPackages");
                });
#pragma warning restore 612, 618
        }
    }
}
