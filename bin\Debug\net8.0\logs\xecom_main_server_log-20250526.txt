2025-05-26 22:13:42.041 +03:00 [INF] Application built successfully.
2025-05-26 22:13:42.073 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-05-26 22:13:42.092 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-05-26 22:13:42.348 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-26 22:13:42.350 +03:00 [INF] HTTP request pipeline configuration complete.
2025-05-26 22:13:42.351 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-05-26 22:13:42.353 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-05-26 22:13:42.357 +03:00 [INF] Pre-run checks complete.
2025-05-26 22:13:42.358 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-05-26 22:13:42.509 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-26 22:13:46.194 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-05-26 22:13:46.198 +03:00 [INF] Received encrypted data length: 14296
2025-05-26 22:13:46.206 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-05-26 22:13:46.208 +03:00 [INF] Decryption successful. JSON length: 10712. First 100 chars: [
  {
    "Id": 839,
    "OrderNumber": "21180",
    "OrderedAt": "2025-05-26T19:12:54.769",
  
2025-05-26 22:13:46.248 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 9
2025-05-26 22:13:46.257 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 9
2025-05-26 22:13:46.261 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 9 ORDERS ============
2025-05-26 22:13:46.266 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:13:46.271 +03:00 [INF] Processing OrderNumber: 21180 (Client ID: 839) with ERPService.
2025-05-26 22:13:46.279 +03:00 [INF] Loaded last cari kod suffix from file: 486
2025-05-26 22:13:46.280 +03:00 [INF] Processing order 21180 for Netsis integration.
2025-05-26 22:13:46.285 +03:00 [WRN] Data quality issues found for Order 21180. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: Ender Gelir, Email: NULL, Phone: +905326050999, Address: Atatürk Mah Mithat Vardar Cad 224.Sokak B Blok Kat : 6 D : 14
2025-05-26 22:13:46.291 +03:00 [INF] Email is empty for order 21180. Will create new cari without email search.
2025-05-26 22:13:46.293 +03:00 [INF] Creating new cari for order 21180 (Email: null)
2025-05-26 22:13:46.297 +03:00 [INF] Creating cari with data - Name: Ender Gelir, Email: NULL, Phone: +905326050999, City: Edirne, Address: Atatürk Mah Mithat Vardar Cad 224.Sokak B Blok Kat : 6 D : 14
2025-05-26 22:13:46.305 +03:00 [DBG] Saved last cari kod suffix to file: 487
2025-05-26 22:13:46.310 +03:00 [INF] Generated new Cari Kod: 120087426487 (suffix: 487, timestamp: 1748286826)
2025-05-26 22:13:46.314 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120087426487, Unvan: Ender Gelir
2025-05-26 22:13:46.321 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-05-26 22:13:47.459 +03:00 [INF] New Netsis token acquired. Expires at: "2025-05-26T19:32:46.4590706Z"
2025-05-26 22:13:47.466 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:13:47.487 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120087426487","CARI_ISIM":"Ender Gelir","CARI_TIP":"A","ADRES1":"Atat\u00FCrk Mah Mithat Vardar Cad 224.Sokak B Blok Kat : 6 D : 14","ADRES2":"Ebru Ya\u015Fam Evleri","IL":"Edirne","ILCE":"Merkez","ULKE_KODU":"TR","POSTA_KODU":"22100","TELEFON1":"\u002B905326050999","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120087426487","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:13:47.655 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120087426487","ULKE_KODU":"TR","CARI_ISIM":"Ender Gelir","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120087426487","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:13:47.675 +03:00 [INF] Successfully created Netsis cari. CariKod: 120087426487 (attempt 1)
2025-05-26 22:13:47.681 +03:00 [INF] ✅ Successfully created cari: 120087426487 for customer: Ender Gelir
2025-05-26 22:13:47.683 +03:00 [INF] Successfully created cari. CariKod: 120087426487 for OrderNumber: 21180
2025-05-26 22:13:47.685 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21180 with CariKod: 120087426487
2025-05-26 22:13:47.687 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:13:47.689 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:13:47.719 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"FatUst":{"Sube_Kodu":203,"CariKod":"120087426487","Tarih":"2025-05-26 19:12:54","FIYATTARIHI":"2025-05-26 19:12:54","Tip":0,"TIPI":2,"Proje_Kodu":"b"},"Kalems":[{"StokKodu":"1271C004","STra_GCMIK":1,"STra_NF":585,"STra_BF":585,"STra_ACIKLAMA":"Hagen Kep \u015Eapka","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"}]}
2025-05-26 22:13:47.996 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:13:48.010 +03:00 [ERR] ❌ Failed to create Netsis order for 21180. Customer: Ender Gelir, CariKod: 120087426487, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:13:48.020 +03:00 [ERR] Failed to process OrderNumber 21180 (Client ID: 839) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:13:48.024 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:13:48.026 +03:00 [INF] Loaded last cari kod suffix from file: 487
2025-05-26 22:13:48.034 +03:00 [INF] Processing OrderNumber: 21179 (Client ID: 838) with ERPService.
2025-05-26 22:13:48.073 +03:00 [INF] Processing order 21179 for Netsis integration.
2025-05-26 22:13:48.076 +03:00 [WRN] Data quality issues found for Order 21179. Issues: Email is empty, Phone number is empty, Both TaxNumber and IdentityNumber are empty. Customer: şerife önal, Email: NULL, Phone: , Address: Süleymanşah Mah İstasyon caddesi 13/A 
2025-05-26 22:13:48.091 +03:00 [INF] Email is empty for order 21179. Will create new cari without email search.
2025-05-26 22:13:48.110 +03:00 [INF] Creating new cari for order 21179 (Email: null)
2025-05-26 22:13:48.112 +03:00 [INF] Creating cari with data - Name: şerife önal, Email: NULL, Phone: , City: Şanlıurfa, Address: Süleymanşah Mah İstasyon caddesi 13/A 
2025-05-26 22:13:48.130 +03:00 [DBG] Saved last cari kod suffix to file: 488
2025-05-26 22:13:48.141 +03:00 [INF] Generated new Cari Kod: 120087428488 (suffix: 488, timestamp: 1748286828)
2025-05-26 22:13:48.144 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120087428488, Unvan: şerife önal
2025-05-26 22:13:48.146 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:13:48.147 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:13:48.157 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120087428488","CARI_ISIM":"\u015Ferife \u00F6nal","CARI_TIP":"A","ADRES1":"S\u00FCleyman\u015Fah Mah \u0130stasyon caddesi 13/A ","ADRES2":"","IL":"\u015Eanl\u0131urfa","ILCE":"Ak\u00E7akale","ULKE_KODU":"TR","POSTA_KODU":"63000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120087428488","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:13:48.270 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120087428488","ULKE_KODU":"TR","CARI_ISIM":"şerife önal","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120087428488","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:13:48.286 +03:00 [INF] Successfully created Netsis cari. CariKod: 120087428488 (attempt 1)
2025-05-26 22:13:48.289 +03:00 [INF] ✅ Successfully created cari: 120087428488 for customer: şerife önal
2025-05-26 22:13:48.292 +03:00 [INF] Successfully created cari. CariKod: 120087428488 for OrderNumber: 21179
2025-05-26 22:13:48.296 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21179 with CariKod: 120087428488
2025-05-26 22:13:48.299 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:13:48.301 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:13:48.303 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"FatUst":{"Sube_Kodu":203,"CariKod":"120087428488","Tarih":"2025-05-26 17:05:14","FIYATTARIHI":"2025-05-26 17:05:14","Tip":0,"TIPI":2,"Proje_Kodu":"b"},"Kalems":[{"StokKodu":"1276C032","STra_GCMIK":1,"STra_NF":851.41,"STra_BF":851.41,"STra_ACIKLAMA":"Gravois Kova \u015Eapka","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"}]}
2025-05-26 22:13:48.451 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:13:48.454 +03:00 [ERR] ❌ Failed to create Netsis order for 21179. Customer: şerife önal, CariKod: 120087428488, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:13:48.460 +03:00 [ERR] Failed to process OrderNumber 21179 (Client ID: 838) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:13:48.469 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:13:48.472 +03:00 [INF] Loaded last cari kod suffix from file: 488
2025-05-26 22:13:48.475 +03:00 [INF] Processing OrderNumber: 21178 (Client ID: 837) with ERPService.
2025-05-26 22:13:48.479 +03:00 [INF] Processing order 21178 for Netsis integration.
2025-05-26 22:13:48.482 +03:00 [WRN] Data quality issues found for Order 21178. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: Umur Basaran, Email: NULL, Phone: +905333008271, Address: Çiftlikönü mh 412 sk No 11 
2025-05-26 22:13:48.492 +03:00 [INF] Email is empty for order 21178. Will create new cari without email search.
2025-05-26 22:13:48.496 +03:00 [INF] Creating new cari for order 21178 (Email: null)
2025-05-26 22:13:48.500 +03:00 [INF] Creating cari with data - Name: Umur Basaran, Email: NULL, Phone: +905333008271, City: Tekirdağ, Address: Çiftlikönü mh 412 sk No 11 
2025-05-26 22:13:48.510 +03:00 [DBG] Saved last cari kod suffix to file: 489
2025-05-26 22:13:48.512 +03:00 [INF] Generated new Cari Kod: 120087428489 (suffix: 489, timestamp: 1748286828)
2025-05-26 22:13:48.517 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120087428489, Unvan: Umur Basaran
2025-05-26 22:13:48.519 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:13:48.521 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:13:48.523 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120087428489","CARI_ISIM":"Umur Basaran","CARI_TIP":"A","ADRES1":"\u00C7iftlik\u00F6n\u00FC mh 412 sk No 11 ","ADRES2":"Merit Life 3 Lat 5 Daire 18","IL":"Tekirda\u011F","ILCE":"S\u00FCleymanpa\u015Fa","ULKE_KODU":"TR","POSTA_KODU":"59030","TELEFON1":"\u002B905333008271","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120087428489","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:13:48.648 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120087428489","ULKE_KODU":"TR","CARI_ISIM":"Umur Basaran","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120087428489","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:13:48.661 +03:00 [INF] Successfully created Netsis cari. CariKod: 120087428489 (attempt 1)
2025-05-26 22:13:48.664 +03:00 [INF] ✅ Successfully created cari: 120087428489 for customer: Umur Basaran
2025-05-26 22:13:48.667 +03:00 [INF] Successfully created cari. CariKod: 120087428489 for OrderNumber: 21178
2025-05-26 22:13:48.672 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21178 with CariKod: 120087428489
2025-05-26 22:13:48.674 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:13:48.677 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:13:48.678 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"FatUst":{"Sube_Kodu":203,"CariKod":"120087428489","Tarih":"2025-05-26 16:14:46","FIYATTARIHI":"2025-05-26 16:14:46","Tip":0,"TIPI":2,"Proje_Kodu":"b"},"Kalems":[{"StokKodu":"2219C007","STra_GCMIK":1,"STra_NF":270,"STra_BF":270,"STra_ACIKLAMA":"Kramer T-shirt","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"},{"StokKodu":"2218C007","STra_GCMIK":1,"STra_NF":248.4,"STra_BF":248.4,"STra_ACIKLAMA":"Hohep T-Shirt","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"},{"StokKodu":"2218C001","STra_GCMIK":1,"STra_NF":248.4,"STra_BF":248.4,"STra_ACIKLAMA":"Hohep T-Shirt","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"},{"StokKodu":"1287C118","STra_GCMIK":1,"STra_NF":540,"STra_BF":540,"STra_ACIKLAMA":"Nolasco Kova \u015Eapka","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"},{"StokKodu":"2217C183","STra_GCMIK":1,"STra_NF":599.4,"STra_BF":599.4,"STra_ACIKLAMA":"Erfurt Sweatshirt","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"},{"StokKodu":"2220C017","STra_GCMIK":1,"STra_NF":540,"STra_BF":540,"STra_ACIKLAMA":"Mund Sweatshirt","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"}]}
2025-05-26 22:13:48.818 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:13:48.822 +03:00 [ERR] ❌ Failed to create Netsis order for 21178. Customer: Umur Basaran, CariKod: 120087428489, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:13:48.830 +03:00 [ERR] Failed to process OrderNumber 21178 (Client ID: 837) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:13:48.834 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:13:48.837 +03:00 [INF] Loaded last cari kod suffix from file: 489
2025-05-26 22:13:48.837 +03:00 [INF] Processing OrderNumber: 21177 (Client ID: 836) with ERPService.
2025-05-26 22:13:48.842 +03:00 [INF] Processing order 21177 for Netsis integration.
2025-05-26 22:13:48.844 +03:00 [WRN] Data quality issues found for Order 21177. Issues: Email is empty, Phone number is empty, Both TaxNumber and IdentityNumber are empty. Customer: Meriç Yılmaz, Email: NULL, Phone: , Address: Üniversiteler Mah 1598. Cad. Bilkent 2 Park Sitesi D2/17
2025-05-26 22:13:48.849 +03:00 [INF] Email is empty for order 21177. Will create new cari without email search.
2025-05-26 22:13:48.851 +03:00 [INF] Creating new cari for order 21177 (Email: null)
2025-05-26 22:13:48.853 +03:00 [INF] Creating cari with data - Name: Meriç Yılmaz, Email: NULL, Phone: , City: Ankara, Address: Üniversiteler Mah 1598. Cad. Bilkent 2 Park Sitesi D2/17
2025-05-26 22:13:48.858 +03:00 [DBG] Saved last cari kod suffix to file: 490
2025-05-26 22:13:48.861 +03:00 [INF] Generated new Cari Kod: 120087428490 (suffix: 490, timestamp: 1748286828)
2025-05-26 22:13:48.863 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120087428490, Unvan: Meriç Yılmaz
2025-05-26 22:13:48.865 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:13:48.866 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:13:48.870 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120087428490","CARI_ISIM":"Meri\u00E7 Y\u0131lmaz","CARI_TIP":"A","ADRES1":"\u00DCniversiteler Mah 1598. Cad. Bilkent 2 Park Sitesi D2/17","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120087428490","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:14:52.220 +03:00 [INF] Application built successfully.
2025-05-26 22:14:52.252 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-05-26 22:14:52.273 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-05-26 22:14:52.533 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-26 22:14:52.535 +03:00 [INF] HTTP request pipeline configuration complete.
2025-05-26 22:14:52.536 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-05-26 22:14:52.537 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-05-26 22:14:52.539 +03:00 [INF] Pre-run checks complete.
2025-05-26 22:14:52.540 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-05-26 22:14:52.699 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-26 22:15:47.568 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-05-26 22:15:47.574 +03:00 [INF] Received encrypted data length: 15596
2025-05-26 22:15:47.588 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-05-26 22:15:47.590 +03:00 [INF] Decryption successful. JSON length: 11688. First 100 chars: [
  {
    "Id": 840,
    "OrderNumber": "21181",
    "OrderedAt": "2025-05-26T19:17:44.961",
  
2025-05-26 22:15:47.638 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 10
2025-05-26 22:15:47.649 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 10
2025-05-26 22:15:47.652 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 10 ORDERS ============
2025-05-26 22:15:47.658 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:15:47.662 +03:00 [INF] Processing OrderNumber: 21181 (Client ID: 840) with ERPService.
2025-05-26 22:15:47.675 +03:00 [INF] Processing order 21181 for Netsis integration.
2025-05-26 22:15:47.681 +03:00 [INF] Loaded last cari kod suffix from file: 490
2025-05-26 22:15:47.689 +03:00 [WRN] Data quality issues found for Order 21181. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: ALİ CEM OĞUZ, Email: NULL, Phone: +905368171574, Address: FEYZULLAH MAHALLESİ. YUNUS EMRE CADDESİ NO 9/1 MALTEPE/İSTANBUL
2025-05-26 22:15:47.694 +03:00 [INF] Email is empty for order 21181. Will create new cari without email search.
2025-05-26 22:15:47.696 +03:00 [INF] Creating new cari for order 21181 (Email: null)
2025-05-26 22:15:47.699 +03:00 [INF] Creating cari with data - Name: ALİ CEM OĞUZ, Email: NULL, Phone: +905368171574, City: İstanbul, Address: FEYZULLAH MAHALLESİ. YUNUS EMRE CADDESİ NO 9/1 MALTEPE/İSTANBUL
2025-05-26 22:15:47.711 +03:00 [DBG] Saved last cari kod suffix to file: 491
2025-05-26 22:15:47.716 +03:00 [INF] Generated new Cari Kod: 120087547491 (suffix: 491, timestamp: 1748286947)
2025-05-26 22:15:47.719 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120087547491, Unvan: ALİ CEM OĞUZ
2025-05-26 22:15:47.727 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-05-26 22:15:48.826 +03:00 [INF] New Netsis token acquired. Expires at: "2025-05-26T19:34:47.8256420Z"
2025-05-26 22:15:48.829 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:15:48.855 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120087547491","CARI_ISIM":"AL\u0130 CEM O\u011EUZ","CARI_TIP":"A","ADRES1":"FEYZULLAH MAHALLES\u0130. YUNUS EMRE CADDES\u0130 NO 9/1 MALTEPE/\u0130STANBUL","ADRES2":"","IL":"\u0130stanbul","ILCE":"Maltepe","ULKE_KODU":"TR","POSTA_KODU":"34843","TELEFON1":"\u002B905368171574","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120087547491","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:15:49.001 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120087547491","ULKE_KODU":"TR","CARI_ISIM":"ALİ CEM OĞUZ","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120087547491","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:15:49.019 +03:00 [INF] Successfully created Netsis cari. CariKod: 120087547491 (attempt 1)
2025-05-26 22:15:49.022 +03:00 [INF] ✅ Successfully created cari: 120087547491 for customer: ALİ CEM OĞUZ
2025-05-26 22:15:49.026 +03:00 [INF] Successfully created cari. CariKod: 120087547491 for OrderNumber: 21181
2025-05-26 22:15:49.028 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21181 with CariKod: 120087547491
2025-05-26 22:15:49.031 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:15:49.032 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:15:49.069 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"FatUst":{"Sube_Kodu":203,"CariKod":"120087547491","Tarih":"2025-05-26 19:17:44","FIYATTARIHI":"2025-05-26 19:17:44","Tip":0,"TIPI":2,"Proje_Kodu":"b"},"Kalems":[{"StokKodu":"1288C014","STra_GCMIK":1,"STra_NF":585,"STra_BF":585,"STra_ACIKLAMA":"Bethnal Kep \u015Eapka","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"}]}
2025-05-26 22:28:50.786 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:28:50.797 +03:00 [ERR] ❌ Failed to create Netsis order for 21181. Customer: ALİ CEM OĞUZ, CariKod: 120087547491, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:28:50.803 +03:00 [ERR] Failed to process OrderNumber 21181 (Client ID: 840) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:28:50.809 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:28:50.812 +03:00 [INF] Loaded last cari kod suffix from file: 491
2025-05-26 22:28:50.812 +03:00 [INF] Processing OrderNumber: 21180 (Client ID: 839) with ERPService.
2025-05-26 22:28:50.820 +03:00 [INF] Processing order 21180 for Netsis integration.
2025-05-26 22:28:50.822 +03:00 [WRN] Data quality issues found for Order 21180. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: Ender Gelir, Email: NULL, Phone: +905326050999, Address: Atatürk Mah Mithat Vardar Cad 224.Sokak B Blok Kat : 6 D : 14
2025-05-26 22:28:50.828 +03:00 [INF] Email is empty for order 21180. Will create new cari without email search.
2025-05-26 22:28:50.830 +03:00 [INF] Creating new cari for order 21180 (Email: null)
2025-05-26 22:28:50.832 +03:00 [INF] Creating cari with data - Name: Ender Gelir, Email: NULL, Phone: +905326050999, City: Edirne, Address: Atatürk Mah Mithat Vardar Cad 224.Sokak B Blok Kat : 6 D : 14
2025-05-26 22:28:50.837 +03:00 [DBG] Saved last cari kod suffix to file: 492
2025-05-26 22:28:50.840 +03:00 [INF] Generated new Cari Kod: 120088330492 (suffix: 492, timestamp: 1748287730)
2025-05-26 22:28:50.842 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088330492, Unvan: Ender Gelir
2025-05-26 22:28:50.844 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:28:50.846 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:28:50.849 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088330492","CARI_ISIM":"Ender Gelir","CARI_TIP":"A","ADRES1":"Atat\u00FCrk Mah Mithat Vardar Cad 224.Sokak B Blok Kat : 6 D : 14","ADRES2":"Ebru Ya\u015Fam Evleri","IL":"Edirne","ILCE":"Merkez","ULKE_KODU":"TR","POSTA_KODU":"22100","TELEFON1":"\u002B905326050999","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088330492","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:28:51.000 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088330492","ULKE_KODU":"TR","CARI_ISIM":"Ender Gelir","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088330492","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:28:51.017 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088330492 (attempt 1)
2025-05-26 22:28:51.022 +03:00 [INF] ✅ Successfully created cari: 120088330492 for customer: Ender Gelir
2025-05-26 22:28:51.024 +03:00 [INF] Successfully created cari. CariKod: 120088330492 for OrderNumber: 21180
2025-05-26 22:28:51.026 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21180 with CariKod: 120088330492
2025-05-26 22:28:51.028 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:28:51.032 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:28:51.034 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"FatUst":{"Sube_Kodu":203,"CariKod":"120088330492","Tarih":"2025-05-26 19:12:54","FIYATTARIHI":"2025-05-26 19:12:54","Tip":0,"TIPI":2,"Proje_Kodu":"b"},"Kalems":[{"StokKodu":"1271C004","STra_GCMIK":1,"STra_NF":585,"STra_BF":585,"STra_ACIKLAMA":"Hagen Kep \u015Eapka","DEPO_KODU":1,"ReferansKodu":"0342","ProjeKodu":"b"}]}
2025-05-26 22:29:19.112 +03:00 [INF] Application built successfully.
2025-05-26 22:29:19.142 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-05-26 22:29:19.161 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-05-26 22:29:19.403 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-26 22:29:19.404 +03:00 [INF] HTTP request pipeline configuration complete.
2025-05-26 22:29:19.405 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-05-26 22:29:19.406 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-05-26 22:29:19.408 +03:00 [INF] Pre-run checks complete.
2025-05-26 22:29:19.409 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-05-26 22:29:19.589 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-26 22:32:45.034 +03:00 [INF] Application built successfully.
2025-05-26 22:32:45.070 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-05-26 22:32:45.089 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-05-26 22:32:45.335 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-26 22:32:45.336 +03:00 [INF] HTTP request pipeline configuration complete.
2025-05-26 22:32:45.337 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-05-26 22:32:45.338 +03:00 [WRN] Netsis API configuration is INCOMPLETE. Missing keys: Netsis:DbPassword. Please check appsettings.json.
2025-05-26 22:32:45.340 +03:00 [INF] Pre-run checks complete.
2025-05-26 22:32:45.341 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-05-26 22:32:45.503 +03:00 [WRN] Overriding address(es) 'http://*:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-26 22:33:31.461 +03:00 [INF] ============ ReceiveOrders (Main Server) STARTED ============
2025-05-26 22:33:31.466 +03:00 [INF] Received encrypted data length: 16920
2025-05-26 22:33:31.475 +03:00 [INF] UTF-8 BOM detected at the beginning of the decrypted JSON. Removing it.
2025-05-26 22:33:31.477 +03:00 [INF] Decryption successful. JSON length: 12683. First 100 chars: [
  {
    "Id": 841,
    "OrderNumber": "21182",
    "OrderedAt": "2025-05-26T19:20:00.918",
  
2025-05-26 22:33:31.529 +03:00 [INF] Deserialization to ClientDTO successful. Number of orders: 11
2025-05-26 22:33:31.538 +03:00 [INF] Mapping to XmainOrder successful. Orders to process: 11
2025-05-26 22:33:31.541 +03:00 [INF] ============ ERP SYNC (Main Server) STARTED FOR 11 ORDERS ============
2025-05-26 22:33:31.546 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:31.549 +03:00 [INF] Processing OrderNumber: 21182 (Client ID: 841) with ERPService.
2025-05-26 22:33:31.559 +03:00 [INF] Processing order 21182 for Netsis integration.
2025-05-26 22:33:31.571 +03:00 [INF] Loaded last cari kod suffix from file: 492
2025-05-26 22:33:31.573 +03:00 [WRN] Data quality issues found for Order 21182. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: İlkkan Aydın, Email: NULL, Phone: +905378977405, Address: HOBYAR MAHALLESİ HAMİDİYE TÜRBESİ SOKAK NO:1/2
2025-05-26 22:33:31.578 +03:00 [INF] Email is empty for order 21182. Will create new cari without email search.
2025-05-26 22:33:31.580 +03:00 [INF] Creating new cari for order 21182 (Email: null)
2025-05-26 22:33:31.583 +03:00 [INF] Creating cari with data - Name: İlkkan Aydın, Email: NULL, Phone: +905378977405, City: İstanbul, Address: HOBYAR MAHALLESİ HAMİDİYE TÜRBESİ SOKAK NO:1/2
2025-05-26 22:33:31.589 +03:00 [DBG] Saved last cari kod suffix to file: 493
2025-05-26 22:33:31.590 +03:00 [INF] Generated new Cari Kod: 120088611493 (suffix: 493, timestamp: 1748288011)
2025-05-26 22:33:31.592 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088611493, Unvan: İlkkan Aydın
2025-05-26 22:33:31.599 +03:00 [INF] Requesting new Netsis token from http://localhost:7070/api/v2/token
2025-05-26 22:33:32.703 +03:00 [INF] New Netsis token acquired. Expires at: "2025-05-26T19:52:31.7031619Z"
2025-05-26 22:33:32.706 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:32.729 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088611493","CARI_ISIM":"\u0130lkkan Ayd\u0131n","CARI_TIP":"A","ADRES1":"HOBYAR MAHALLES\u0130 HAM\u0130D\u0130YE T\u00DCRBES\u0130 SOKAK NO:1/2","ADRES2":"\u0130mparator kokore\u00E7","IL":"\u0130stanbul","ILCE":"Fatih","ULKE_KODU":"TR","POSTA_KODU":"34112","TELEFON1":"\u002B905378977405","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088611493","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:32.906 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088611493","ULKE_KODU":"TR","CARI_ISIM":"İlkkan Aydın","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088611493","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:32.915 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088611493 (attempt 1)
2025-05-26 22:33:32.920 +03:00 [INF] ✅ Successfully created cari: 120088611493 for customer: İlkkan Aydın
2025-05-26 22:33:32.922 +03:00 [INF] Successfully created cari. CariKod: 120088611493 for OrderNumber: 21182
2025-05-26 22:33:32.924 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21182 with CariKod: 120088611493
2025-05-26 22:33:32.928 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:32.929 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:32.959 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088611493","Tarih":"2025-05-26 19:20:00","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C004","STra_GCMIK":1,"STra_NF":585,"STra_BF":585,"DEPO_KODU":1}]}
2025-05-26 22:33:33.248 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:33.255 +03:00 [ERR] ❌ Failed to create Netsis order for 21182. Customer: İlkkan Aydın, CariKod: 120088611493, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:33.262 +03:00 [ERR] Failed to process OrderNumber 21182 (Client ID: 841) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:33.267 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:33.269 +03:00 [INF] Loaded last cari kod suffix from file: 493
2025-05-26 22:33:33.271 +03:00 [INF] Processing OrderNumber: 21181 (Client ID: 840) with ERPService.
2025-05-26 22:33:33.275 +03:00 [INF] Processing order 21181 for Netsis integration.
2025-05-26 22:33:33.277 +03:00 [WRN] Data quality issues found for Order 21181. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: ALİ CEM OĞUZ, Email: NULL, Phone: +905368171574, Address: FEYZULLAH MAHALLESİ. YUNUS EMRE CADDESİ NO 9/1 MALTEPE/İSTANBUL
2025-05-26 22:33:33.284 +03:00 [INF] Email is empty for order 21181. Will create new cari without email search.
2025-05-26 22:33:33.286 +03:00 [INF] Creating new cari for order 21181 (Email: null)
2025-05-26 22:33:33.288 +03:00 [INF] Creating cari with data - Name: ALİ CEM OĞUZ, Email: NULL, Phone: +905368171574, City: İstanbul, Address: FEYZULLAH MAHALLESİ. YUNUS EMRE CADDESİ NO 9/1 MALTEPE/İSTANBUL
2025-05-26 22:33:33.300 +03:00 [DBG] Saved last cari kod suffix to file: 494
2025-05-26 22:33:33.302 +03:00 [INF] Generated new Cari Kod: 120088613494 (suffix: 494, timestamp: 1748288013)
2025-05-26 22:33:33.307 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088613494, Unvan: ALİ CEM OĞUZ
2025-05-26 22:33:33.309 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:33.310 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:33.313 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088613494","CARI_ISIM":"AL\u0130 CEM O\u011EUZ","CARI_TIP":"A","ADRES1":"FEYZULLAH MAHALLES\u0130. YUNUS EMRE CADDES\u0130 NO 9/1 MALTEPE/\u0130STANBUL","ADRES2":"","IL":"\u0130stanbul","ILCE":"Maltepe","ULKE_KODU":"TR","POSTA_KODU":"34843","TELEFON1":"\u002B905368171574","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088613494","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:33.435 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088613494","ULKE_KODU":"TR","CARI_ISIM":"ALİ CEM OĞUZ","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088613494","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:33.451 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088613494 (attempt 1)
2025-05-26 22:33:33.454 +03:00 [INF] ✅ Successfully created cari: 120088613494 for customer: ALİ CEM OĞUZ
2025-05-26 22:33:33.459 +03:00 [INF] Successfully created cari. CariKod: 120088613494 for OrderNumber: 21181
2025-05-26 22:33:33.462 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21181 with CariKod: 120088613494
2025-05-26 22:33:33.464 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:33.465 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:33.470 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088613494","Tarih":"2025-05-26 19:17:44","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1288C014","STra_GCMIK":1,"STra_NF":585,"STra_BF":585,"DEPO_KODU":1}]}
2025-05-26 22:33:33.615 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:33.620 +03:00 [ERR] ❌ Failed to create Netsis order for 21181. Customer: ALİ CEM OĞUZ, CariKod: 120088613494, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:33.630 +03:00 [ERR] Failed to process OrderNumber 21181 (Client ID: 840) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:33.636 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:33.639 +03:00 [INF] Loaded last cari kod suffix from file: 494
2025-05-26 22:33:33.641 +03:00 [INF] Processing OrderNumber: 21180 (Client ID: 839) with ERPService.
2025-05-26 22:33:33.645 +03:00 [INF] Processing order 21180 for Netsis integration.
2025-05-26 22:33:33.647 +03:00 [WRN] Data quality issues found for Order 21180. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: Ender Gelir, Email: NULL, Phone: +905326050999, Address: Atatürk Mah Mithat Vardar Cad 224.Sokak B Blok Kat : 6 D : 14
2025-05-26 22:33:33.654 +03:00 [INF] Email is empty for order 21180. Will create new cari without email search.
2025-05-26 22:33:33.657 +03:00 [INF] Creating new cari for order 21180 (Email: null)
2025-05-26 22:33:33.663 +03:00 [INF] Creating cari with data - Name: Ender Gelir, Email: NULL, Phone: +905326050999, City: Edirne, Address: Atatürk Mah Mithat Vardar Cad 224.Sokak B Blok Kat : 6 D : 14
2025-05-26 22:33:33.670 +03:00 [DBG] Saved last cari kod suffix to file: 495
2025-05-26 22:33:33.675 +03:00 [INF] Generated new Cari Kod: 120088613495 (suffix: 495, timestamp: 1748288013)
2025-05-26 22:33:33.677 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088613495, Unvan: Ender Gelir
2025-05-26 22:33:33.680 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:33.681 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:33.683 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088613495","CARI_ISIM":"Ender Gelir","CARI_TIP":"A","ADRES1":"Atat\u00FCrk Mah Mithat Vardar Cad 224.Sokak B Blok Kat : 6 D : 14","ADRES2":"Ebru Ya\u015Fam Evleri","IL":"Edirne","ILCE":"Merkez","ULKE_KODU":"TR","POSTA_KODU":"22100","TELEFON1":"\u002B905326050999","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088613495","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:33.793 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088613495","ULKE_KODU":"TR","CARI_ISIM":"Ender Gelir","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088613495","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:33.808 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088613495 (attempt 1)
2025-05-26 22:33:33.812 +03:00 [INF] ✅ Successfully created cari: 120088613495 for customer: Ender Gelir
2025-05-26 22:33:33.815 +03:00 [INF] Successfully created cari. CariKod: 120088613495 for OrderNumber: 21180
2025-05-26 22:33:33.819 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21180 with CariKod: 120088613495
2025-05-26 22:33:33.822 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:33.824 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:33.829 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088613495","Tarih":"2025-05-26 19:12:54","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1271C004","STra_GCMIK":1,"STra_NF":585,"STra_BF":585,"DEPO_KODU":1}]}
2025-05-26 22:33:33.954 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:33.958 +03:00 [ERR] ❌ Failed to create Netsis order for 21180. Customer: Ender Gelir, CariKod: 120088613495, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:33.966 +03:00 [ERR] Failed to process OrderNumber 21180 (Client ID: 839) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:33.972 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:33.974 +03:00 [INF] Loaded last cari kod suffix from file: 495
2025-05-26 22:33:33.977 +03:00 [INF] Processing OrderNumber: 21179 (Client ID: 838) with ERPService.
2025-05-26 22:33:33.982 +03:00 [INF] Processing order 21179 for Netsis integration.
2025-05-26 22:33:33.984 +03:00 [WRN] Data quality issues found for Order 21179. Issues: Email is empty, Phone number is empty, Both TaxNumber and IdentityNumber are empty. Customer: şerife önal, Email: NULL, Phone: , Address: Süleymanşah Mah İstasyon caddesi 13/A 
2025-05-26 22:33:33.990 +03:00 [INF] Email is empty for order 21179. Will create new cari without email search.
2025-05-26 22:33:33.992 +03:00 [INF] Creating new cari for order 21179 (Email: null)
2025-05-26 22:33:33.994 +03:00 [INF] Creating cari with data - Name: şerife önal, Email: NULL, Phone: , City: Şanlıurfa, Address: Süleymanşah Mah İstasyon caddesi 13/A 
2025-05-26 22:33:34.003 +03:00 [DBG] Saved last cari kod suffix to file: 496
2025-05-26 22:33:34.005 +03:00 [INF] Generated new Cari Kod: 120088614496 (suffix: 496, timestamp: 1748288014)
2025-05-26 22:33:34.010 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088614496, Unvan: şerife önal
2025-05-26 22:33:34.012 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:34.014 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:34.015 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088614496","CARI_ISIM":"\u015Ferife \u00F6nal","CARI_TIP":"A","ADRES1":"S\u00FCleyman\u015Fah Mah \u0130stasyon caddesi 13/A ","ADRES2":"","IL":"\u015Eanl\u0131urfa","ILCE":"Ak\u00E7akale","ULKE_KODU":"TR","POSTA_KODU":"63000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088614496","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:34.137 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088614496","ULKE_KODU":"TR","CARI_ISIM":"şerife önal","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088614496","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:34.151 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088614496 (attempt 1)
2025-05-26 22:33:34.154 +03:00 [INF] ✅ Successfully created cari: 120088614496 for customer: şerife önal
2025-05-26 22:33:34.157 +03:00 [INF] Successfully created cari. CariKod: 120088614496 for OrderNumber: 21179
2025-05-26 22:33:34.162 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21179 with CariKod: 120088614496
2025-05-26 22:33:34.164 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:34.166 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:34.168 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088614496","Tarih":"2025-05-26 17:05:14","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1276C032","STra_GCMIK":1,"STra_NF":851.41,"STra_BF":851.41,"DEPO_KODU":1}]}
2025-05-26 22:33:34.301 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:34.308 +03:00 [ERR] ❌ Failed to create Netsis order for 21179. Customer: şerife önal, CariKod: 120088614496, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:34.316 +03:00 [ERR] Failed to process OrderNumber 21179 (Client ID: 838) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:34.320 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:34.321 +03:00 [INF] Loaded last cari kod suffix from file: 496
2025-05-26 22:33:34.322 +03:00 [INF] Processing OrderNumber: 21178 (Client ID: 837) with ERPService.
2025-05-26 22:33:34.327 +03:00 [INF] Processing order 21178 for Netsis integration.
2025-05-26 22:33:34.329 +03:00 [WRN] Data quality issues found for Order 21178. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: Umur Basaran, Email: NULL, Phone: +905333008271, Address: Çiftlikönü mh 412 sk No 11 
2025-05-26 22:33:34.337 +03:00 [INF] Email is empty for order 21178. Will create new cari without email search.
2025-05-26 22:33:34.339 +03:00 [INF] Creating new cari for order 21178 (Email: null)
2025-05-26 22:33:34.341 +03:00 [INF] Creating cari with data - Name: Umur Basaran, Email: NULL, Phone: +905333008271, City: Tekirdağ, Address: Çiftlikönü mh 412 sk No 11 
2025-05-26 22:33:34.351 +03:00 [DBG] Saved last cari kod suffix to file: 497
2025-05-26 22:33:34.353 +03:00 [INF] Generated new Cari Kod: 120088614497 (suffix: 497, timestamp: 1748288014)
2025-05-26 22:33:34.356 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088614497, Unvan: Umur Basaran
2025-05-26 22:33:34.361 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:34.364 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:34.366 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088614497","CARI_ISIM":"Umur Basaran","CARI_TIP":"A","ADRES1":"\u00C7iftlik\u00F6n\u00FC mh 412 sk No 11 ","ADRES2":"Merit Life 3 Lat 5 Daire 18","IL":"Tekirda\u011F","ILCE":"S\u00FCleymanpa\u015Fa","ULKE_KODU":"TR","POSTA_KODU":"59030","TELEFON1":"\u002B905333008271","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088614497","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:34.484 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088614497","ULKE_KODU":"TR","CARI_ISIM":"Umur Basaran","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088614497","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:34.493 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088614497 (attempt 1)
2025-05-26 22:33:34.503 +03:00 [INF] ✅ Successfully created cari: 120088614497 for customer: Umur Basaran
2025-05-26 22:33:34.506 +03:00 [INF] Successfully created cari. CariKod: 120088614497 for OrderNumber: 21178
2025-05-26 22:33:34.513 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21178 with CariKod: 120088614497
2025-05-26 22:33:34.517 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:34.519 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:34.525 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088614497","Tarih":"2025-05-26 16:14:46","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"2219C007","STra_GCMIK":1,"STra_NF":270,"STra_BF":270,"DEPO_KODU":1},{"StokKodu":"2218C007","STra_GCMIK":1,"STra_NF":248.4,"STra_BF":248.4,"DEPO_KODU":1},{"StokKodu":"2218C001","STra_GCMIK":1,"STra_NF":248.4,"STra_BF":248.4,"DEPO_KODU":1},{"StokKodu":"1287C118","STra_GCMIK":1,"STra_NF":540,"STra_BF":540,"DEPO_KODU":1},{"StokKodu":"2217C183","STra_GCMIK":1,"STra_NF":599.4,"STra_BF":599.4,"DEPO_KODU":1},{"StokKodu":"2220C017","STra_GCMIK":1,"STra_NF":540,"STra_BF":540,"DEPO_KODU":1}]}
2025-05-26 22:33:34.658 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:34.661 +03:00 [ERR] ❌ Failed to create Netsis order for 21178. Customer: Umur Basaran, CariKod: 120088614497, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:34.666 +03:00 [ERR] Failed to process OrderNumber 21178 (Client ID: 837) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:34.673 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:34.676 +03:00 [INF] Processing OrderNumber: 21177 (Client ID: 836) with ERPService.
2025-05-26 22:33:34.677 +03:00 [INF] Loaded last cari kod suffix from file: 497
2025-05-26 22:33:34.678 +03:00 [INF] Processing order 21177 for Netsis integration.
2025-05-26 22:33:34.684 +03:00 [WRN] Data quality issues found for Order 21177. Issues: Email is empty, Phone number is empty, Both TaxNumber and IdentityNumber are empty. Customer: Meriç Yılmaz, Email: NULL, Phone: , Address: Üniversiteler Mah 1598. Cad. Bilkent 2 Park Sitesi D2/17
2025-05-26 22:33:34.689 +03:00 [INF] Email is empty for order 21177. Will create new cari without email search.
2025-05-26 22:33:34.692 +03:00 [INF] Creating new cari for order 21177 (Email: null)
2025-05-26 22:33:34.694 +03:00 [INF] Creating cari with data - Name: Meriç Yılmaz, Email: NULL, Phone: , City: Ankara, Address: Üniversiteler Mah 1598. Cad. Bilkent 2 Park Sitesi D2/17
2025-05-26 22:33:34.703 +03:00 [DBG] Saved last cari kod suffix to file: 498
2025-05-26 22:33:34.706 +03:00 [INF] Generated new Cari Kod: 120088614498 (suffix: 498, timestamp: 1748288014)
2025-05-26 22:33:34.709 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088614498, Unvan: Meriç Yılmaz
2025-05-26 22:33:34.713 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:34.715 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:34.717 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088614498","CARI_ISIM":"Meri\u00E7 Y\u0131lmaz","CARI_TIP":"A","ADRES1":"\u00DCniversiteler Mah 1598. Cad. Bilkent 2 Park Sitesi D2/17","ADRES2":"","IL":"Ankara","ILCE":"\u00C7ankaya","ULKE_KODU":"TR","POSTA_KODU":"06000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088614498","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:34.849 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088614498","ULKE_KODU":"TR","CARI_ISIM":"Meriç Yılmaz","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088614498","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:34.864 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088614498 (attempt 1)
2025-05-26 22:33:34.866 +03:00 [INF] ✅ Successfully created cari: 120088614498 for customer: Meriç Yılmaz
2025-05-26 22:33:34.869 +03:00 [INF] Successfully created cari. CariKod: 120088614498 for OrderNumber: 21177
2025-05-26 22:33:34.872 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21177 with CariKod: 120088614498
2025-05-26 22:33:34.874 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:34.876 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:34.877 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088614498","Tarih":"2025-05-26 15:59:27","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1639C002","STra_GCMIK":1,"STra_NF":225.05,"STra_BF":225.05,"DEPO_KODU":1},{"StokKodu":"8210C049","STra_GCMIK":1,"STra_NF":1278.7,"STra_BF":1278.7,"DEPO_KODU":1}]}
2025-05-26 22:33:35.018 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:35.024 +03:00 [ERR] ❌ Failed to create Netsis order for 21177. Customer: Meriç Yılmaz, CariKod: 120088614498, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:35.035 +03:00 [ERR] Failed to process OrderNumber 21177 (Client ID: 836) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:35.043 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:35.045 +03:00 [INF] Loaded last cari kod suffix from file: 498
2025-05-26 22:33:35.046 +03:00 [INF] Processing OrderNumber: 21176 (Client ID: 835) with ERPService.
2025-05-26 22:33:35.054 +03:00 [INF] Processing order 21176 for Netsis integration.
2025-05-26 22:33:35.056 +03:00 [WRN] Data quality issues found for Order 21176. Issues: Email is empty, Phone number is empty, Both TaxNumber and IdentityNumber are empty. Customer: Efe Demir, Email: NULL, Phone: , Address: 31 Ağustos Mah Şehit İsa Türkmen Sokak, Acarkent Apt. Kat:1 Daire:1 No:5 Banaz/UŞAK
2025-05-26 22:33:35.065 +03:00 [INF] Email is empty for order 21176. Will create new cari without email search.
2025-05-26 22:33:35.069 +03:00 [INF] Creating new cari for order 21176 (Email: null)
2025-05-26 22:33:35.072 +03:00 [INF] Creating cari with data - Name: Efe Demir, Email: NULL, Phone: , City: Uşak, Address: 31 Ağustos Mah Şehit İsa Türkmen Sokak, Acarkent Apt. Kat:1 Daire:1 No:5 Banaz/UŞAK
2025-05-26 22:33:35.082 +03:00 [DBG] Saved last cari kod suffix to file: 499
2025-05-26 22:33:35.085 +03:00 [INF] Generated new Cari Kod: 120088615499 (suffix: 499, timestamp: 1748288015)
2025-05-26 22:33:35.089 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088615499, Unvan: Efe Demir
2025-05-26 22:33:35.091 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:35.093 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:35.097 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088615499","CARI_ISIM":"Efe Demir","CARI_TIP":"A","ADRES1":"31 A\u011Fustos Mah \u015Eehit \u0130sa T\u00FCrkmen Sokak, Acarkent Apt. Kat:1 Daire:1 No:5 Banaz/U\u015EAK","ADRES2":"","IL":"U\u015Fak","ILCE":"Banaz","ULKE_KODU":"TR","POSTA_KODU":"64000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088615499","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:35.204 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088615499","ULKE_KODU":"TR","CARI_ISIM":"Efe Demir","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088615499","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:35.214 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088615499 (attempt 1)
2025-05-26 22:33:35.219 +03:00 [INF] ✅ Successfully created cari: 120088615499 for customer: Efe Demir
2025-05-26 22:33:35.222 +03:00 [INF] Successfully created cari. CariKod: 120088615499 for OrderNumber: 21176
2025-05-26 22:33:35.225 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21176 with CariKod: 120088615499
2025-05-26 22:33:35.229 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:35.231 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:35.233 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088615499","Tarih":"2025-05-26 15:44:38","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1286C007","STra_GCMIK":1,"STra_NF":979.99,"STra_BF":979.99,"DEPO_KODU":1}]}
2025-05-26 22:33:35.366 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:35.370 +03:00 [ERR] ❌ Failed to create Netsis order for 21176. Customer: Efe Demir, CariKod: 120088615499, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:35.375 +03:00 [ERR] Failed to process OrderNumber 21176 (Client ID: 835) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:35.383 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:35.386 +03:00 [INF] Loaded last cari kod suffix from file: 499
2025-05-26 22:33:35.386 +03:00 [INF] Processing OrderNumber: 21175 (Client ID: 834) with ERPService.
2025-05-26 22:33:35.393 +03:00 [INF] Processing order 21175 for Netsis integration.
2025-05-26 22:33:35.395 +03:00 [WRN] Data quality issues found for Order 21175. Issues: Email is empty, Phone number is empty, Both TaxNumber and IdentityNumber are empty. Customer: beyza köksal, Email: NULL, Phone: , Address: Aydınevler Mah Serçe Sk. 3-1, Aydınevler, 34854 Maltepe/İstanbul Necati-Mehmet Apart daire no:4
2025-05-26 22:33:35.400 +03:00 [INF] Email is empty for order 21175. Will create new cari without email search.
2025-05-26 22:33:35.407 +03:00 [INF] Creating new cari for order 21175 (Email: null)
2025-05-26 22:33:35.409 +03:00 [INF] Creating cari with data - Name: beyza köksal, Email: NULL, Phone: , City: İstanbul, Address: Aydınevler Mah Serçe Sk. 3-1, Aydınevler, 34854 Maltepe/İstanbul Necati-Mehmet Apart daire no:4
2025-05-26 22:33:35.414 +03:00 [DBG] Saved last cari kod suffix to file: 500
2025-05-26 22:33:35.417 +03:00 [INF] Generated new Cari Kod: 120088615500 (suffix: 500, timestamp: 1748288015)
2025-05-26 22:33:35.421 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088615500, Unvan: beyza köksal
2025-05-26 22:33:35.423 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:35.427 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:35.429 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088615500","CARI_ISIM":"beyza k\u00F6ksal","CARI_TIP":"A","ADRES1":"Ayd\u0131nevler Mah Ser\u00E7e Sk. 3-1, Ayd\u0131nevler, 34854 Maltepe/\u0130stanbul Necati-Mehmet Apart daire no:4","ADRES2":"","IL":"\u0130stanbul","ILCE":"Maltepe","ULKE_KODU":"TR","POSTA_KODU":"34000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088615500","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:35.546 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088615500","ULKE_KODU":"TR","CARI_ISIM":"beyza köksal","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088615500","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:35.556 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088615500 (attempt 1)
2025-05-26 22:33:35.562 +03:00 [INF] ✅ Successfully created cari: 120088615500 for customer: beyza köksal
2025-05-26 22:33:35.565 +03:00 [INF] Successfully created cari. CariKod: 120088615500 for OrderNumber: 21175
2025-05-26 22:33:35.567 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21175 with CariKod: 120088615500
2025-05-26 22:33:35.572 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:35.575 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:35.579 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088615500","Tarih":"2025-05-26 14:16:15","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1274C051","STra_GCMIK":1,"STra_NF":471.99,"STra_BF":471.99,"DEPO_KODU":1}]}
2025-05-26 22:33:35.716 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:52.905 +03:00 [ERR] ❌ Failed to create Netsis order for 21175. Customer: beyza köksal, CariKod: 120088615500, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:52.914 +03:00 [ERR] Failed to process OrderNumber 21175 (Client ID: 834) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:52.918 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:52.920 +03:00 [INF] Loaded last cari kod suffix from file: 500
2025-05-26 22:33:52.922 +03:00 [INF] Processing OrderNumber: 21174 (Client ID: 833) with ERPService.
2025-05-26 22:33:52.930 +03:00 [INF] Processing order 21174 for Netsis integration.
2025-05-26 22:33:52.932 +03:00 [WRN] Data quality issues found for Order 21174. Issues: Email is empty, Phone number is empty, Both TaxNumber and IdentityNumber are empty. Customer: görkem özkaymakcı, Email: NULL, Phone: , Address:  116/3 sokak no:4 D:17 çınar apt erzene mah bornova 
2025-05-26 22:33:52.941 +03:00 [INF] Email is empty for order 21174. Will create new cari without email search.
2025-05-26 22:33:52.943 +03:00 [INF] Creating new cari for order 21174 (Email: null)
2025-05-26 22:33:52.946 +03:00 [INF] Creating cari with data - Name: görkem özkaymakcı, Email: NULL, Phone: , City: İzmir, Address:  116/3 sokak no:4 D:17 çınar apt erzene mah bornova 
2025-05-26 22:33:52.951 +03:00 [DBG] Saved last cari kod suffix to file: 501
2025-05-26 22:33:52.953 +03:00 [INF] Generated new Cari Kod: 120088632501 (suffix: 501, timestamp: 1748288032)
2025-05-26 22:33:52.959 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088632501, Unvan: görkem özkaymakcı
2025-05-26 22:33:52.961 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:52.963 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:52.965 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088632501","CARI_ISIM":"g\u00F6rkem \u00F6zkaymakc\u0131","CARI_TIP":"A","ADRES1":" 116/3 sokak no:4 D:17 \u00E7\u0131nar apt erzene mah bornova ","ADRES2":"","IL":"\u0130zmir","ILCE":"Bornova","ULKE_KODU":"TR","POSTA_KODU":"35000","TELEFON1":"","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088632501","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:53.084 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088632501","ULKE_KODU":"TR","CARI_ISIM":"görkem özkaymakcı","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088632501","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:53.098 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088632501 (attempt 1)
2025-05-26 22:33:53.100 +03:00 [INF] ✅ Successfully created cari: 120088632501 for customer: görkem özkaymakcı
2025-05-26 22:33:53.103 +03:00 [INF] Successfully created cari. CariKod: 120088632501 for OrderNumber: 21174
2025-05-26 22:33:53.107 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21174 with CariKod: 120088632501
2025-05-26 22:33:53.109 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:53.111 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:53.113 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088632501","Tarih":"2025-05-26 13:00:31","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"4241C051","STra_GCMIK":1,"STra_NF":745,"STra_BF":745,"DEPO_KODU":1}]}
2025-05-26 22:33:53.239 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:53.244 +03:00 [ERR] ❌ Failed to create Netsis order for 21174. Customer: görkem özkaymakcı, CariKod: 120088632501, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:53.254 +03:00 [ERR] Failed to process OrderNumber 21174 (Client ID: 833) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:53.259 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:53.263 +03:00 [INF] Loaded last cari kod suffix from file: 501
2025-05-26 22:33:53.265 +03:00 [INF] Processing OrderNumber: 21173 (Client ID: 832) with ERPService.
2025-05-26 22:33:53.270 +03:00 [INF] Processing order 21173 for Netsis integration.
2025-05-26 22:33:53.275 +03:00 [WRN] Data quality issues found for Order 21173. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: Emrah Işık, Email: NULL, Phone: 905063910822, Address: Kargo şubesinden teslim alınacak. Düzce/merkez
2025-05-26 22:33:53.284 +03:00 [INF] Email is empty for order 21173. Will create new cari without email search.
2025-05-26 22:33:53.290 +03:00 [INF] Creating new cari for order 21173 (Email: null)
2025-05-26 22:33:53.293 +03:00 [INF] Creating cari with data - Name: Emrah Işık, Email: NULL, Phone: 905063910822, City: Düzce, Address: Kargo şubesinden teslim alınacak. Düzce/merkez
2025-05-26 22:33:53.302 +03:00 [DBG] Saved last cari kod suffix to file: 502
2025-05-26 22:33:53.304 +03:00 [INF] Generated new Cari Kod: 120088633502 (suffix: 502, timestamp: 1748288033)
2025-05-26 22:33:53.309 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088633502, Unvan: Emrah Işık
2025-05-26 22:33:53.313 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:53.314 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:53.318 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088633502","CARI_ISIM":"Emrah I\u015F\u0131k","CARI_TIP":"A","ADRES1":"Kargo \u015Fubesinden teslim al\u0131nacak. D\u00FCzce/merkez","ADRES2":"Kargo \u015Fubesi","IL":"D\u00FCzce","ILCE":"Merkez","ULKE_KODU":"TR","POSTA_KODU":"81010","TELEFON1":"905063910822","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088633502","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:53.436 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088633502","ULKE_KODU":"TR","CARI_ISIM":"Emrah Işık","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088633502","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:53.450 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088633502 (attempt 1)
2025-05-26 22:33:53.453 +03:00 [INF] ✅ Successfully created cari: 120088633502 for customer: Emrah Işık
2025-05-26 22:33:53.456 +03:00 [INF] Successfully created cari. CariKod: 120088633502 for OrderNumber: 21173
2025-05-26 22:33:53.460 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21173 with CariKod: 120088633502
2025-05-26 22:33:53.463 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:53.465 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:53.469 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088633502","Tarih":"2025-05-26 10:01:53","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"4241C051","STra_GCMIK":1,"STra_NF":351,"STra_BF":351,"DEPO_KODU":1}]}
2025-05-26 22:33:53.602 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:53.608 +03:00 [ERR] ❌ Failed to create Netsis order for 21173. Customer: Emrah Işık, CariKod: 120088633502, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:53.617 +03:00 [ERR] Failed to process OrderNumber 21173 (Client ID: 832) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:53.621 +03:00 [INF] ERPService initialized. API Base URL: http://localhost:7070/
2025-05-26 22:33:53.625 +03:00 [INF] Loaded last cari kod suffix from file: 502
2025-05-26 22:33:53.628 +03:00 [INF] Processing OrderNumber: 21172 (Client ID: 831) with ERPService.
2025-05-26 22:33:53.633 +03:00 [INF] Processing order 21172 for Netsis integration.
2025-05-26 22:33:53.639 +03:00 [WRN] Data quality issues found for Order 21172. Issues: Email is empty, Both TaxNumber and IdentityNumber are empty. Customer: Esrin  Aydın, Email: NULL, Phone: +905320511468, Address: Acıbadem mahallesi Bayır sokak 
2025-05-26 22:33:53.644 +03:00 [INF] Email is empty for order 21172. Will create new cari without email search.
2025-05-26 22:33:53.651 +03:00 [INF] Creating new cari for order 21172 (Email: null)
2025-05-26 22:33:53.653 +03:00 [INF] Creating cari with data - Name: Esrin  Aydın, Email: NULL, Phone: +905320511468, City: İstanbul, Address: Acıbadem mahallesi Bayır sokak 
2025-05-26 22:33:53.661 +03:00 [DBG] Saved last cari kod suffix to file: 503
2025-05-26 22:33:53.662 +03:00 [INF] Generated new Cari Kod: 120088633503 (suffix: 503, timestamp: 1748288033)
2025-05-26 22:33:53.666 +03:00 [INF] Attempting to create new cari. Generated CariKod: 120088633503, Unvan: Esrin  Aydın
2025-05-26 22:33:53.669 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:53.672 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:53.675 +03:00 [DBG] TryCreateCariAsync JSON Payload (attempt 1): {"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088633503","CARI_ISIM":"Esrin  Ayd\u0131n","CARI_TIP":"A","ADRES1":"Ac\u0131badem mahallesi Bay\u0131r sokak ","ADRES2":"Neyyire Han\u0131m apartman\u0131 No:4 Daire:2","IL":"\u0130stanbul","ILCE":"\u00DCsk\u00FCdar","ULKE_KODU":"TR","POSTA_KODU":"34674","TELEFON1":"\u002B905320511468","VERGI_DAIRESI":"","VERGI_NUMARASI":"","TCKimlikNo":""},"CariEkBilgi":{"CARI_KOD":"120088633503","TcKimlikNo":""},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}
2025-05-26 22:33:53.792 +03:00 [DBG] TryCreateCariAsync Response - Status: "OK", Content: {"Meta":{"Href":"http://localhost:7070/api/v2/ARPs?limit=10&offset=0","MediaType":"application/json; charset=UTF-8","ApiVersion":"2.0"},"IsSuccessful":true,"Data":{"CariTemelBilgi":{"Sube_Kodu":203,"ISLETME_KODU":1,"CARI_KOD":"120088633503","ULKE_KODU":"TR","CARI_ISIM":"Esrin  Aydın","CARI_TIP":"A","DETAY_KODU":0,"NAKLIYE_KATSAYISI":0.0,"RISK_SINIRI":0.0,"TEMINATI":0.0,"CARISK":0.0,"CCRISK":0.0,"SARISK":0.0,"SCRISK":0.0,"CM_BORCT":0.0,"CM_ALACT":0.0,"CM_RAP_TARIH":"1899-12-30 00:00:00","ISKONTO_ORANI":0.0,"VADE_GUNU":0,"LISTE_FIATI":0,"DOVIZ_TIPI":0,"DOVIZ_TURU":0,"HESAPTUTMASEKLI":"Y","DOVIZLIMI":"H","Update_Kodu":"X","LOKALDEPO":0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"KayitTarihi":"1899-12-30 00:00:00","DuzeltmeTarihi":"1899-12-30 00:00:00","ODEMETIPI":0,"OnayNum":0,"AGIRLIK_ISK":0.0,"Teslimat_Gunu":0,"NAKLIYE_SURESI":0},"CariEkBilgi":{"CARI_KOD":"120088633503","KayitTarihi":"2025-05-26 00:00:00","KayitYapanKul":"NetOpenX","DuzeltmeTarihi":"1899-12-30 00:00:00","Kull1N":0.0,"Kull2N":0.0,"Kull3N":0.0,"Kull4N":0.0,"Kull5N":0.0,"Kull6N":0.0,"Kull7N":0.0,"Kull8N":0.0,"SALES_VOLUME":0.0,"PRIM":0.0,"F_Yedek1":0.0,"F_Yedek2":0.0,"B_Yedek1":0,"I_Yedek1":0,"L_Yedek1":0,"DOVIZ_CEVRIM":0},"SubelerdeOrtak":false,"IsletmelerdeOrtak":false}} (attempt 1)
2025-05-26 22:33:53.799 +03:00 [INF] Successfully created Netsis cari. CariKod: 120088633503 (attempt 1)
2025-05-26 22:33:53.804 +03:00 [INF] ✅ Successfully created cari: 120088633503 for customer: Esrin  Aydın
2025-05-26 22:33:53.807 +03:00 [INF] Successfully created cari. CariKod: 120088633503 for OrderNumber: 21172
2025-05-26 22:33:53.809 +03:00 [INF] Attempting to create Netsis order for OrderNumber: 21172 with CariKod: 120088633503
2025-05-26 22:33:53.813 +03:00 [DBG] Using cached Netsis token.
2025-05-26 22:33:53.816 +03:00 [DBG] HttpClient BaseAddress set to: "http://localhost:7070/"
2025-05-26 22:33:53.819 +03:00 [DBG] CreateNetsisOrderAsync JSON Payload (Order): {"Seri":"F","FatUst":{"Sube_Kodu":203,"CariKod":"120088633503","Tarih":"2025-05-26 08:45:46","Tip":0,"TIPI":2},"KayitliNumaraOtomatikGuncellensin":true,"SeriliHesapla":true,"Kalems":[{"StokKodu":"1287C118","STra_GCMIK":1,"STra_NF":540,"STra_BF":540,"DEPO_KODU":1}]}
2025-05-26 22:33:53.957 +03:00 [DBG] CreateNetsisOrderAsync Response - Status: "OK", Content: {"IsSuccessful":false,"ErrorCode":"101","ErrorDesc":"Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel"}
2025-05-26 22:33:53.961 +03:00 [ERR] ❌ Failed to create Netsis order for 21172. Customer: Esrin  Aydın, CariKod: 120088633503, Status: "OK". ErrorCode: 101, ErrorDesc: Field 'GEC_KDV_SIF_EKMAL' not foundNetOpenX50.Kernel
2025-05-26 22:33:53.970 +03:00 [ERR] Failed to process OrderNumber 21172 (Client ID: 831) in ERP. Result: ERROR_ORDER_CREATE
2025-05-26 22:33:53.973 +03:00 [INF] ERP SYNC (Main Server) FINISHED. Total Client DTOs: 11, Processable Mapped Orders: 11, Success: 0, Failed: 11
2025-05-26 22:33:53.977 +03:00 [INF] ============ ReceiveOrders (Main Server) FINISHED ============
