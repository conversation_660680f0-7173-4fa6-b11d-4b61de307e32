﻿// XECOM_Main_LocalServer\Models\XmainOrderLineItem.cs
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace XECOM_Main_LocalServer.Models
{
    [Table("XmainOrderLineItems")] // Örnek tablo adı
    public class XmainOrderLineItem
    {
        [Key]
        public long Id { get; set; } // Bu satırın kendi birincil anahtarı

        public long ClientLineItemId { get; set; } // İstemcideki orijinal Id
        public string VariantSku { get; set; }
        public string VariantName { get; set; }
        public int Quantity { get; set; }

        [Column(TypeName = "decimal(18, 4)")] // Fiyatlar için daha fazla hassasiyet
        public decimal Price { get; set; }

        [Column(TypeName = "decimal(18, 4)")]
        public decimal FinalPrice { get; set; }

        // Foreign key for XmainOrder
        public long OrderId { get; set; }

        [JsonIgnore]
        [ForeignKey(nameof(OrderId))]
        public virtual XmainOrder Order { get; set; }
    }
}