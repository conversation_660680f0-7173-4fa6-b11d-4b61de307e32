﻿// XECOM_Main_LocalServer\Services\ERPService.cs
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NetOpenX50;
using RestSharp;
using RestSharp.Authenticators.OAuth2;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using XECOM_Main_LocalServer.Models;
using XECOM_Main_LocalServer.Models.NetsisModels;

namespace XECOM_Main_LocalServer.Services
{
    public class ERPService
    {
        private readonly ILogger<ERPService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly string _apiBaseUrl;
        private readonly string _username;
        private readonly string _password;
        private readonly string _dbName;
        private readonly string _dbUser;
        private readonly string _dbPassword;
        private readonly string _dbType;
        private readonly string _branchCode;

        private static string _currentToken;
        private static DateTime _tokenExpiration;
        private static readonly SemaphoreSlim _tokenSemaphore = new SemaphoreSlim(1, 1);

        private static long _nextCariKodSuffix = 0;
        private const string CARI_KOD_PREFIX = "120";
        private const long CARI_KOD_INITIAL_NUMBER = 00541064;
        private const string CARI_KOD_COUNTER_FILE = "netsis_cari_counter.txt";

        private readonly JsonSerializerOptions _jsonSerializerOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            PropertyNamingPolicy = null,
        };

        public ERPService(ILogger<ERPService> logger, IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _logger = logger;
            _httpClientFactory = httpClientFactory;

            _apiBaseUrl = configuration["Netsis:ApiBaseUrl"];
            _username = configuration["Netsis:Username"];
            _password = configuration["Netsis:Password"];
            _dbName = configuration["Netsis:DbName"];
            _dbUser = configuration["Netsis:DbUser"];
            _dbPassword = configuration["Netsis:DbPassword"] ?? "";
            _dbType = configuration["Netsis:DbType"];
            _branchCode = configuration["Netsis:BranchCode"];

            if (string.IsNullOrWhiteSpace(_apiBaseUrl))
                throw new ArgumentNullException(nameof(_apiBaseUrl), "Netsis API Base URL cannot be empty in configuration.");
            if (!Uri.TryCreate(_apiBaseUrl, UriKind.Absolute, out _))
                throw new ArgumentException("Netsis API Base URL is not a valid absolute URI.", nameof(_apiBaseUrl));

            Task.Run(async () => {
                try
                {
                    _nextCariKodSuffix = await LoadLastCariKodSuffixAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during cari kod suffix initialization");
                }
            });

            _logger.LogInformation("ERPService initialized. API Base URL: {ApiBaseUrl}", _apiBaseUrl);
        }

        private async Task<long> LoadLastCariKodSuffixAsync()
        {
            try
            {
                if (File.Exists(CARI_KOD_COUNTER_FILE))
                {
                    var content = await File.ReadAllTextAsync(CARI_KOD_COUNTER_FILE);
                    if (long.TryParse(content.Trim(), out long lastSuffix))
                    {
                        _logger.LogInformation("Loaded last cari kod suffix from file: {LastSuffix}", lastSuffix);
                        return lastSuffix;
                    }
                }
                _logger.LogInformation("No cari kod counter file found, starting from 0");
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading cari kod counter from file, starting from 0");
                return 0;
            }
        }

        private async Task SaveLastCariKodSuffixAsync(long suffix)
        {
            try
            {
                await File.WriteAllTextAsync(CARI_KOD_COUNTER_FILE, suffix.ToString());
                _logger.LogDebug("Saved last cari kod suffix to file: {Suffix}", suffix);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving cari kod counter to file");
            }
        }

        private async Task<string> GetTokenAsync()
        {
            await _tokenSemaphore.WaitAsync();
            try
            {
                if (!string.IsNullOrEmpty(_currentToken) && DateTime.UtcNow < _tokenExpiration)
                {
                    _logger.LogDebug("Using cached Netsis token.");
                    return _currentToken;
                }

                _logger.LogInformation("Requesting new Netsis token from {ApiBaseUrl}api/v2/token", _apiBaseUrl);
                var httpClient = _httpClientFactory.CreateClient("NetsisApiClient");

                var tokenRequestData = new Dictionary<string, string>
                {
                    {"grant_type", "password"},
                    {"username", _username},
                    {"password", _password},
                    {"dbname", _dbName},
                    {"dbuser", _dbUser},
                    {"dbpassword", _dbPassword},
                    {"dbtype", _dbType},
                    {"branchcode", _branchCode}
                };

                using var requestContent = new FormUrlEncodedContent(tokenRequestData);
                var tokenEndpoint = new Uri(new Uri(_apiBaseUrl.TrimEnd('/') + "/"), "api/v2/token");

                var response = await httpClient.PostAsync(tokenEndpoint, requestContent);
                var responseContentString = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContentString, _jsonSerializerOptions);
                    _currentToken = tokenResponse?.AccessToken;
                    _tokenExpiration = DateTime.UtcNow.AddSeconds((tokenResponse?.ExpiresIn ?? 3600) - 60);
                    _logger.LogInformation("New Netsis token acquired. Expires at: {Expiration}", _tokenExpiration);
                    return _currentToken;
                }
                else
                {
                    _logger.LogError("Failed to get Netsis token. Status: {StatusCode}, Response: {ErrorResponse}", response.StatusCode, responseContentString);
                    throw new HttpRequestException($"Netsis token request failed: {response.StatusCode} - {responseContentString}");
                }
            }
            finally
            {
                _tokenSemaphore.Release();
            }
        }

        private async Task<HttpClient> CreateAuthenticatedClientAsync()
        {
            var token = await GetTokenAsync();
            var client = _httpClientFactory.CreateClient("NetsisApiClient");

            if (client.BaseAddress == null)
            {
                client.BaseAddress = new Uri(_apiBaseUrl.TrimEnd('/') + "/");
                _logger.LogDebug("HttpClient BaseAddress set to: {BaseAddress}", client.BaseAddress);
            }

            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            return client;
        }

        private async Task<string> GenerateNewCariKodAsync()
        {
            long currentSuffixNumber = Interlocked.Increment(ref _nextCariKodSuffix);

            long timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            long uniqueNumber = CARI_KOD_INITIAL_NUMBER + (timestamp % 100000) * 1000 + currentSuffixNumber;

            string newKod = $"{CARI_KOD_PREFIX}{uniqueNumber:D9}";

            await SaveLastCariKodSuffixAsync(currentSuffixNumber);

            _logger.LogInformation("Generated new Cari Kod: {CariKod} (suffix: {Suffix}, timestamp: {Timestamp})",
                newKod, currentSuffixNumber, timestamp);
            return newKod;
        }

        public async Task<string> FindCariByEmailAsync(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                _logger.LogWarning("Email is empty for cari search.");
                return null;
            }

            _logger.LogInformation("Searching for Cari with EMAIL: {Email}", email);
            return await SearchCariAsync("EMAIL", email);
        }

        private async Task<string> SearchCariAsync(string fieldName, string fieldValue)
        {
            var httpClient = await CreateAuthenticatedClientAsync();
            var requestUri = $"api/v2/ARPs?{Uri.EscapeDataString(fieldName)}={Uri.EscapeDataString(fieldValue)}";
            _logger.LogDebug("SearchCariAsync Request URI (relative): {RequestUri}", requestUri);

            try
            {
                var response = await httpClient.GetAsync(requestUri);
                var content = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogDebug("SearchCariAsync response content: {Content}", content.Length > 500 ? content.Substring(0, 500) + "..." : content);
                    var cariler = JsonSerializer.Deserialize<List<JsonElement>>(content, _jsonSerializerOptions);
                    if (cariler != null && cariler.Any())
                    {
                        if (cariler[0].TryGetProperty("CariTemelBilgi", out var temelBilgi) &&
                            temelBilgi.TryGetProperty("CARI_KOD", out var cariKodElement))
                        {
                            string foundCariKod = cariKodElement.GetString();
                            _logger.LogInformation("Cari found with {FieldName}. CariKod: {CariKod}", fieldName, foundCariKod);
                            return foundCariKod;
                        }
                        _logger.LogWarning("Cari found for {FieldName}={FieldValue}, but CARI_KOD could not be extracted.", fieldName, fieldValue);
                        return "FOUND_BUT_NO_CODE";
                    }
                    _logger.LogInformation("No cari found for {FieldName}={FieldValue} (empty list or null).", fieldName, fieldValue);
                    return null;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogInformation("No cari found (404) for {FieldName}={FieldValue}.", fieldName, fieldValue);
                    return null;
                }
                else
                {
                    _logger.LogError("Error finding cari. Status: {StatusCode}. Response: {ErrorResponse}", response.StatusCode, content);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception during SearchCariAsync for {FieldName}={FieldValue}", fieldName, fieldValue);
                return null;
            }
        }

        private List<string> ValidateOrderData(XmainOrder order)
        {
            var issues = new List<string>();

            if (order == null)
            {
                issues.Add("Order is NULL");
                return issues;
            }

            if (order.BillingAddress == null)
            {
                issues.Add("BillingAddress is NULL");
                return issues;
            }

            var address = order.BillingAddress;

            // Kritik alanları kontrol et
            if (string.IsNullOrWhiteSpace(address.Email))
                issues.Add("Email is empty");

            if (string.IsNullOrWhiteSpace(address.NetsisUnvan))
                issues.Add("Customer name (NetsisUnvan) is empty");

            if (string.IsNullOrWhiteSpace(address.AddressLine1))
                issues.Add("Address line 1 is empty");

            if (string.IsNullOrWhiteSpace(address.CityName))
                issues.Add("City name is empty");

            if (string.IsNullOrWhiteSpace(address.DistrictName))
                issues.Add("District name is empty");

            // Opsiyonel ama önemli alanlar
            if (string.IsNullOrWhiteSpace(address.Phone))
                issues.Add("Phone number is empty");

            if (string.IsNullOrWhiteSpace(address.PostalCode))
                issues.Add("Postal code is empty");

            // Vergi/Kimlik bilgileri (ikisi de boşsa sorun)
            if (string.IsNullOrWhiteSpace(address.TaxNumber) && string.IsNullOrWhiteSpace(address.IdentityNumber))
                issues.Add("Both TaxNumber and IdentityNumber are empty");

            // Sipariş kalemleri kontrolü
            if (order.OrderLineItems == null || !order.OrderLineItems.Any())
            {
                issues.Add("Order has no line items");
            }
            else
            {
                int itemIndex = 1;
                foreach (var item in order.OrderLineItems)
                {
                    if (string.IsNullOrWhiteSpace(item.VariantSku))
                        issues.Add($"Line item {itemIndex}: SKU is empty");
                    if (item.Quantity <= 0)
                        issues.Add($"Line item {itemIndex}: Invalid quantity ({item.Quantity})");
                    if (item.FinalPrice <= 0)
                        issues.Add($"Line item {itemIndex}: Invalid price ({item.FinalPrice})");
                    itemIndex++;
                }
            }

            return issues;
        }

        public async Task<string> CreateNetsisCariAsync(XmainAddress address, string email)
        {
            _logger.LogInformation("Creating cari with data - Name: {Name}, Email: {Email}, Phone: {Phone}, City: {City}, Address: {Address}",
                address?.NetsisUnvan ?? "NULL",
                email ?? "NULL",
                address?.Phone ?? "NULL",
                address?.CityName ?? "NULL",
                address?.AddressLine1 ?? "NULL");

            string yeniCariKod = await GenerateNewCariKodAsync();
            _logger.LogInformation("Attempting to create new cari. Generated CariKod: {YeniCariKod}, Unvan: {Unvan}", yeniCariKod, address.NetsisUnvan);

            for (int attempt = 1; attempt <= 5; attempt++)
            {
                var result = await TryCreateCariAsync(yeniCariKod, address, email, attempt);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("✅ Successfully created cari: {CariKod} for customer: {CustomerName}", result.CariKod, address?.NetsisUnvan);
                    return result.CariKod;
                }
                else if (result.IsDuplicateKey && attempt < 5)
                {
                    yeniCariKod = await GenerateNewCariKodAsync();
                    _logger.LogWarning("Duplicate cari kod detected. Retrying with new code: {NewCariKod} (attempt {Attempt})", yeniCariKod, attempt + 1);
                    continue;
                }
                else
                {
                    _logger.LogError("❌ Failed to create cari after {MaxAttempts} attempts for customer: {CustomerName}, Email: {Email}",
                        attempt, address?.NetsisUnvan ?? "NULL", email ?? "NULL");
                    return null;
                }
            }

            return null;
        }

        private async Task<(bool IsSuccess, bool IsDuplicateKey, string CariKod)> TryCreateCariAsync(string cariKod, XmainAddress address, string email, int attempt)
        {
            var httpClient = await CreateAuthenticatedClientAsync();

            var cariPayload = new
            {
                CariTemelBilgi = new
                {
                    Sube_Kodu = Convert.ToInt16(_branchCode),
                    ISLETME_KODU = 1,
                    CARI_KOD = cariKod,
                    CARI_ISIM = address.NetsisUnvan.Length > 50 ? address.NetsisUnvan.Substring(0, 50) : address.NetsisUnvan,
                    CARI_TIP = "A",
                    ADRES1 = address.AddressLine1,
                    ADRES2 = address.AddressLine2,
                    IL = address.CityName,
                    ILCE = address.DistrictName,
                    ULKE_KODU = "TR",
                    POSTA_KODU = address.PostalCode,
                    TELEFON1 = address.Phone,
                    VERGI_DAIRESI = address.TaxOffice,
                    VERGI_NUMARASI = address.TaxNumber,
                    TCKimlikNo = address.IdentityNumber,
                    EMAIL = string.IsNullOrWhiteSpace(email) ? address.Email : email
                },
                CariEkBilgi = new
                {
                    CARI_KOD = cariKod,
                    TcKimlikNo = address.IdentityNumber
                },
                SubelerdeOrtak = false,
                IsletmelerdeOrtak = false
            };

            string jsonPayload = JsonSerializer.Serialize(cariPayload, _jsonSerializerOptions);
            _logger.LogDebug("TryCreateCariAsync JSON Payload (attempt {Attempt}): {JsonPayload}", attempt, jsonPayload);
            using var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            try
            {
                var response = await httpClient.PostAsync("api/v2/ARPs", content);
                var responseString = await response.Content.ReadAsStringAsync();

                _logger.LogDebug("TryCreateCariAsync Response - Status: {StatusCode}, Content: {ResponseContent} (attempt {Attempt})",
                    response.StatusCode, responseString, attempt);

                NetsisApiResponse apiResponse = null;
                try
                {
                    apiResponse = JsonSerializer.Deserialize<NetsisApiResponse>(responseString, _jsonSerializerOptions);
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Could not deserialize Netsis API response as NetsisApiResponse. Raw response: {ResponseString}", responseString);
                }

                if (response.IsSuccessStatusCode && (apiResponse?.IsSuccessful != false))
                {
                    _logger.LogInformation("Successfully created Netsis cari. CariKod: {CariKod} (attempt {Attempt})", cariKod, attempt);
                    return (true, false, cariKod);
                }
                else
                {
                    string errorDetails = apiResponse != null
                        ? $"ErrorCode: {apiResponse.ErrorCode}, ErrorDesc: {apiResponse.ErrorDesc}"
                        : "Unable to parse error response";

                    if (apiResponse?.ErrorDesc?.Contains("cari kod önceden tanımlı") == true)
                    {
                        _logger.LogWarning("Cari kod {CariKod} already exists in Netsis (attempt {Attempt}). Will try with new code.", cariKod, attempt);
                        return (false, true, null);
                    }

                    _logger.LogError("Failed to create Netsis cari (attempt {Attempt}). Status: {StatusCode}. {ErrorDetails}. Full Response: {ErrorResponse}",
                        attempt, response.StatusCode, errorDetails, responseString);
                    return (false, false, null);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception during TryCreateCariAsync for CariKod {CariKod} (attempt {Attempt})", cariKod, attempt);
                return (false, false, null);
            }
        }

        // 🎯 API EXAMPLE BİREBİR TEST VERSİYONU - CreateNetsisOrderAsync
        public async Task<string> CreateNetsisOrderAsync(XmainOrder order)
        {
            _logger.LogInformation("🚨 DEBUG: CreateNetsisOrderAsync STARTED for order {OrderNumber}", order?.OrderNumber ?? "NULL");

            try
            {
                _logger.LogInformation("🎯 API EXAMPLE TEST Processing order {OrderNumber} for Netsis integration.", order.OrderNumber);

                // Basit veri doğrulama
                if (order?.BillingAddress == null)
                {
                    _logger.LogError("❌ BillingAddress is null for order {OrderNumber}", order?.OrderNumber);
                    return "ERROR_NO_BILLING_ADDRESS";
                }

                _logger.LogInformation("🚨 DEBUG: Starting cari search/create process...");

                // Cari bul veya oluştur (mevcut kodunuz)
                string cariKod = null;

                if (!string.IsNullOrWhiteSpace(order.BillingAddress.Email))
                {
                    _logger.LogInformation("🚨 DEBUG: Searching for existing cari by email...");
                    cariKod = await FindCariByEmailAsync(order.BillingAddress.Email);
                    if (!string.IsNullOrEmpty(cariKod) && cariKod != "FOUND_BUT_NO_CODE")
                    {
                        _logger.LogInformation("Cari found: {CariKod}", cariKod);
                    }
                    else
                    {
                        cariKod = null;
                    }
                }

                if (string.IsNullOrEmpty(cariKod))
                {
                    _logger.LogInformation("🚨 DEBUG: Creating new cari...");
                    cariKod = await CreateNetsisCariAsync(order.BillingAddress, order.BillingAddress.Email);
                    if (string.IsNullOrEmpty(cariKod))
                    {
                        _logger.LogError("❌ Failed to create cari for order {OrderNumber}", order.OrderNumber);
                        return "ERROR_CARI";
                    }
                }

                _logger.LogInformation("🚨 DEBUG: Cari process completed. CariKod: {CariKod}", cariKod);

                // API EXAMPLE SİPARİŞ OLUŞTURMA
                _logger.LogInformation("🎯 Attempting API EXAMPLE Netsis order for OrderNumber: {OrderNumber} with CariKod: {CariKod}", order.OrderNumber, cariKod);

                _logger.LogInformation("🚨 DEBUG: Creating authenticated HTTP client...");
                var httpClient = await CreateAuthenticatedClientAsync();
                _logger.LogInformation("🚨 DEBUG: HTTP client created successfully");

                // 🎯 ORDER API PAYLOAD
                var orderPayload = new
                {
                    Seri = "F",
                    FatUst = new
                    {
                        Sube_Kodu = Convert.ToInt16(_branchCode),
                        CariKod = cariKod,
                        Tarih = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        FIYATTARIHI = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        Tip = 7,
                        TIPI = 1,
                        DEPO_KODU = 203,
                        KDV_DAHILMI = true,        // ✅ KDV dahil mi - Native'de var
                        YUVARLAMA = 0.0           // ✅ Yuvarlama - Native'de var
                    },
                    KayitliNumaraOtomatikGuncellensin = true,
                    SeriliHesapla = true,
                    Kalems = order.OrderLineItems.Take(1).Select((li, index) => new  // Sadece 1 kalem test
                    {
                        StokKodu = li.VariantSku,
                        STra_GCMIK = (double)li.Quantity,
                        STra_NF = (double)li.FinalPrice,
                        STra_BF = (double)li.FinalPrice,
                        STra_KOD1 = "Y",                       // ✅ Native'de zorunlu
                        STra_KDV = 10.00,                        // ✅ KDV tutarı - Native'de var
                        STra_TAR = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"), // ✅ Tarih - Native'de var
                        STra_ACIKLAMA = "Online Order Item",
                        DEPO_KODU = 203,
                        Olcubr = 1,                           // ✅ Ölçü birimi - Native'de var
                        CEVRIM = 1.0,                         // ✅ Çevrim oranı - Native'de var
                        ReferansKodu = $"{order.OrderNumber}-{index + 1}"
                    }).ToList()
                };

                //var orderPayload = new
                //{
                //    Seri = "F",
                //    FatUst = new
                //    {
                //        Sube_Kodu = Convert.ToInt16(_branchCode),
                //        CariKod = cariKod,
                //        Tarih = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                //        FIYATTARIHI = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                //        Tip = 7,
                //        TIPI = 1,
                //        DEPO_KODU = 203,
                //        KDV_DAHILMI = true,        // ✅ KDV dahil mi - Native'de var
                //        YUVARLAMA = 0.0           // ✅ Yuvarlama - Native'de var
                //    },
                //    KayitliNumaraOtomatikGuncellensin = true,
                //    SeriliHesapla = true,
                //    Kalems = order.OrderLineItems.Take(1).Select((li, index) => new  // Sadece 1 kalem test
                //    {
                //        StokKodu = li.VariantSku,
                //        STra_GCMIK = (double)li.Quantity,
                //        STra_NF = (double)li.FinalPrice,
                //        STra_BF = (double)li.FinalPrice,
                //        STra_KOD1 = "Y",                       // ✅ Native'de zorunlu
                //        STra_KDV = 10.00,                        // ✅ KDV tutarı - Native'de var
                //        STra_TAR = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"), // ✅ Tarih - Native'de var
                //        STra_ACIKLAMA = "Online Order Item",
                //        Olcubr = 1,                           // ✅ Ölçü birimi - Native'de var
                //        CEVRIM = 1.0,                         // ✅ Çevrim oranı - Native'de var
                //        ReferansKodu = $"{order.OrderNumber}-{index + 1}",
                //        ProjeKodu = "b"
                //    }).ToList()
                //};

                string jsonPayload = JsonSerializer.Serialize(orderPayload, _jsonSerializerOptions);
                _logger.LogDebug("🎯 RestAPI JSON: {JsonPayload}", jsonPayload);

                // 🔧 FIX: using variable sorunu için ayrı StringContent'ler kullanıyoruz
                var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // 🚨 DETAYLI HTTP REQUEST LOGGING
                _logger.LogInformation("🔍 =================== HTTP REQUEST DETAILS ===================");
                _logger.LogInformation("🔍 Request URL: {Url}", httpClient.BaseAddress + "api/v2/ItemSlips");
                _logger.LogInformation("🔍 Request Method: POST");
                _logger.LogInformation("🔍 Content-Type: {ContentType}", content.Headers.ContentType);
                _logger.LogInformation("🔍 Content-Length: {ContentLength}", content.Headers.ContentLength);
                _logger.LogInformation("🔍 JSON Payload Length: {PayloadLength}", jsonPayload.Length);

                // Authorization header kontrolü - 🔧 FIX: nullable int problemi
                var authHeader = httpClient.DefaultRequestHeaders.Authorization;
                string authInfo = "NULL";
                if (authHeader != null && !string.IsNullOrEmpty(authHeader.Parameter))
                {
                    int tokenLength = authHeader.Parameter.Length;
                    int showLength = Math.Min(20, tokenLength);
                    authInfo = $"{authHeader.Scheme} {authHeader.Parameter.Substring(0, showLength)}...";
                }
                _logger.LogInformation("🔍 Authorization Header: {AuthHeader}", authInfo);

                // Accept header kontrolü  
                var acceptHeaders = string.Join(", ", httpClient.DefaultRequestHeaders.Accept.Select(a => a.MediaType));
                _logger.LogInformation("🔍 Accept Headers: {AcceptHeaders}", acceptHeaders);

                // Content'i kontrol et - 🔧 FIX: ayrı StringContent kullanıyoruz
                var contentForReading = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
                var contentString = await contentForReading.ReadAsStringAsync();
                _logger.LogInformation("🔍 ACTUAL CONTENT BEING SENT: {ContentString}", contentString);
                _logger.LogInformation("🔍 Content is empty: {IsEmpty}", string.IsNullOrEmpty(contentString));
                _logger.LogInformation("🔍 Content length check: {ActualLength} chars", contentString?.Length ?? 0);
                contentForReading.Dispose(); // Dispose the test content

                _logger.LogInformation("🔍 Content ready for actual request");

                try
                {
                    _logger.LogInformation("🚨 =================== SENDING HTTP POST REQUEST ===================");
                    var contentStringx = await content.ReadAsStringAsync();
                    _logger.LogInformation("🚨 CONTENT BODY: {contentBody}", contentStringx);
                    var response = await httpClient.PostAsync("api/v2/ItemSlips", content);
                    _logger.LogInformation("🚨 HTTP POST REQUEST COMPLETED. Status: {StatusCode}", response.StatusCode);

                    var responseString = await response.Content.ReadAsStringAsync();

                    // Response detayları
                    _logger.LogInformation("🔍 =================== RESPONSE DETAILS ===================");
                    _logger.LogInformation("🔍 Response Status: {StatusCode} ({ReasonPhrase})", response.StatusCode, response.ReasonPhrase);
                    _logger.LogInformation("🔍 Response Content-Length: {ContentLength}", response.Content.Headers.ContentLength);
                    _logger.LogInformation("🔍 Response Content-Type: {ContentType}", response.Content.Headers.ContentType);
                    _logger.LogInformation("🔍 Response Body Length: {BodyLength}", responseString?.Length ?? 0);
                    _logger.LogInformation("🔍 Response Body: {ResponseBody}", responseString);

                    if (response.IsSuccessStatusCode)
                    {
                        NetsisApiResponse apiResponse = null;
                        try
                        {
                            apiResponse = JsonSerializer.Deserialize<NetsisApiResponse>(responseString, _jsonSerializerOptions);
                            _logger.LogInformation("🔍 Parsed API Response - IsSuccessful: {IsSuccessful}, ErrorCode: {ErrorCode}, ErrorDesc: {ErrorDesc}",
                                apiResponse?.IsSuccessful, apiResponse?.ErrorCode, apiResponse?.ErrorDesc);
                        }
                        catch (JsonException ex)
                        {
                            _logger.LogWarning(ex, "🔍 Could not deserialize response as NetsisApiResponse");
                        }

                        if (apiResponse?.IsSuccessful != false)
                        {
                            _logger.LogInformation("✅ RestAPI SUCCESS for {OrderNumber}", order.OrderNumber);
                            return $"RestAPI_OK:{order.OrderNumber}";
                        }
                        else
                        {
                            _logger.LogError("❌ RestAPI API Error: {ErrorCode} - {ErrorDesc}",
                                apiResponse?.ErrorCode, apiResponse?.ErrorDesc);
                            return "RestAPI_API_ERROR";
                        }
                    }
                    else
                    {
                        _logger.LogError("❌ RestAPI HTTP Error: {StatusCode} - {Response}",
                            response.StatusCode, responseString);
                        return "RestAPI_HTTP_ERROR";
                    }
                }
                catch (HttpRequestException httpEx)
                {
                    _logger.LogError(httpEx, "❌ HTTP Request Exception for {OrderNumber}", order.OrderNumber);
                    return "RestAPI_HTTP_EXCEPTION";
                }
                catch (TaskCanceledException tcEx)
                {
                    _logger.LogError(tcEx, "❌ Request Timeout Exception for {OrderNumber}", order.OrderNumber);
                    return "RestAPI_TIMEOUT";
                }
                finally
                {
                    content?.Dispose(); // Content'i dispose et
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ RestAPI GENERAL EXCEPTION for {OrderNumber}", order.OrderNumber);
                return "API_EXAMPLE_EXCEPTION";
            }
        }

        //COM üzerinden Netsis
        public async Task<string> CreateNetsisOrderViaCOM(XmainOrder order)
        {
            try
            {
                var kernelx = new Kernel(); // Eğer bu satır çalışırsa using doğru
                _logger.LogInformation("✅ Kernel created successfully!");
                Marshal.ReleaseComObject(kernelx);
                return "COM_OK";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ COM Test failed: {Message}", ex.Message);
                return "COM_FAILED";
            }


            _logger.LogInformation("🔧 Creating Netsis order via Native SDK for OrderNumber: {OrderNumber}", order.OrderNumber);

            // Önce cari kontrol et/oluştur (mevcut REST metodu kullan)
            string cariKod = null;

            if (!string.IsNullOrWhiteSpace(order.BillingAddress?.Email))
            {
                cariKod = await FindCariByEmailAsync(order.BillingAddress.Email);
            }

            if (string.IsNullOrEmpty(cariKod))
            {
                cariKod = await CreateNetsisCariAsync(order.BillingAddress, order.BillingAddress.Email);
                if (string.IsNullOrEmpty(cariKod))
                {
                    _logger.LogError("❌ Failed to create cari for order {OrderNumber}", order.OrderNumber);
                    return "ERROR_CARI";
                }
            }

            // Native SDK ile fatura oluştur
            Kernel kernel = null;
            Sirket sirket = null;
            Fatura fatura = null;
            FatUst fatUst = null;
            FatKalem fatKalem = null;

            try
            {
                _logger.LogInformation("🔧 Initializing Native SDK...");

                kernel = new Kernel();
                sirket = kernel.yeniSirket(
                    TVTTipi.vtMSSQL,
                    _dbName,
                    "TEMELSET",
                    "",
                    _dbUser,
                    _dbPassword,
                    Convert.ToInt32(_branchCode)
                );

                _logger.LogInformation("🔧 Creating fatura...");
                fatura = kernel.yeniFatura(sirket, TFaturaTip.ftSSip); // Satış Siparişi

                fatUst = fatura.Ust();
                fatUst.CariKod = cariKod;
                fatUst.Tarih = order.OrderedAt;
                fatUst.ENTEGRE_TRH = order.OrderedAt;
                fatUst.YUVARLAMA = 0;
                fatUst.FiiliTarih = order.OrderedAt;
                fatUst.SIPARIS_TEST = order.OrderedAt;
                fatUst.FIYATTARIHI = order.OrderedAt;

                // Yurt içi satış
                fatUst.TIPI = TFaturaTipi.ft_YurtIci;
                fatUst.KOD1 = "Y";
                fatUst.KDV_DAHILMI = true;
                fatUst.TopDepo = 1;

                _logger.LogInformation("🔧 Adding order items...");

                foreach (var lineItem in order.OrderLineItems)
                {
                    fatKalem = fatura.kalemYeni(lineItem.VariantSku);
                    fatKalem.DEPO_KODU = 1;
                    fatKalem.Olcubr = 1;
                    fatKalem.STra_KOD1 = "Y";
                    fatKalem.STra_TAR = order.OrderedAt;
                    fatKalem.STra_NF = (double)lineItem.FinalPrice;
                    fatKalem.STra_BF = (double)lineItem.FinalPrice;
                    fatKalem.STra_GCMIK = (double)lineItem.Quantity;
                }

                _logger.LogInformation("🔧 Performing calculations and saving...");
                fatura.HesaplamalariYap();
                fatura.kayitYeni();

                string belgeNo = fatKalem?.STra_FATIRSNO ?? "UNKNOWN";
                _logger.LogInformation("✅ Native SDK Success - Belge No: {BelgeNo} for OrderNumber: {OrderNumber}", belgeNo, order.OrderNumber);

                return $"NATIVE_OK:{belgeNo}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Native SDK Exception for OrderNumber: {OrderNumber}", order.OrderNumber);
                return "NATIVE_EXCEPTION";
            }
            finally
            {
                // COM nesnelerini temizle
                try
                {
                    if (fatKalem != null) Marshal.ReleaseComObject(fatKalem);
                    if (fatUst != null) Marshal.ReleaseComObject(fatUst);
                    if (fatura != null) Marshal.ReleaseComObject(fatura);
                    if (sirket != null) Marshal.ReleaseComObject(sirket);
                    if (kernel != null)
                    {
                        kernel.FreeNetsisLibrary();
                        Marshal.ReleaseComObject(kernel);
                    }
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogWarning(cleanupEx, "Warning during COM cleanup");
                }
            }
        }

        //RestSharp Netsis
        public async Task<string> CreateNetsisOrderViaRestSharp(XmainOrder order)
        {
            _logger.LogInformation("🔥 Creating Netsis order via RestSharp for OrderNumber: {OrderNumber}", order.OrderNumber);

            try
            {
                // 1. Cari kontrol/oluştur (mevcut metodları kullan)
                string cariKod = await GetOrCreateCariKod(order);
                if (string.IsNullOrEmpty(cariKod))
                {
                    return "ERROR_CARI";
                }

                // 2. RestSharp Token al
                string token = await GetTokenViaRestSharp();
                if (string.IsNullOrEmpty(token))
                {
                    return "ERROR_TOKEN";
                }

                // 3. RestSharp ile sipariş gönder
                return await SendOrderViaRestSharp(order, cariKod, token);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ RestSharp Exception for {OrderNumber}", order.OrderNumber);
                return "RESTSHARP_EXCEPTION";
            }
        }

        private async Task<string> GetOrCreateCariKod(XmainOrder order)
        {
            string cariKod = null;

            if (!string.IsNullOrWhiteSpace(order.BillingAddress?.Email))
            {
                cariKod = await FindCariByEmailAsync(order.BillingAddress.Email);
            }

            if (string.IsNullOrEmpty(cariKod))
            {
                cariKod = await CreateNetsisCariAsync(order.BillingAddress, order.BillingAddress.Email);
            }

            return cariKod;
        }

        private async Task<string> GetTokenViaRestSharp()
        {
            try
            {
                _logger.LogInformation("🔥 Getting token via RestSharp...");

                var tokenUrl = $"{_apiBaseUrl.TrimEnd('/')}/api/v2/token";

                // ✅ Yeni RestSharp API
                var options = new RestClientOptions(tokenUrl);
                var restClient = new RestClient(options);

                var request = new RestRequest("/", Method.Post);

                // ✅ GitHub örneğindeki gibi - Content-Type JSON ama body string!
                var body = $"grant_type=password&branchcode={_branchCode}&password={_password}&username={_username}&dbname={_dbName}&dbuser={_dbUser}&dbpassword={_dbPassword}&dbtype={_dbType}";

                request.AddHeader("Content-Type", "application/json");
                request.AddStringBody(body, ContentType.Json); // ✅ Yeni API

                _logger.LogInformation("🔥 Token request body: {Body}", body);

                var httpResult = await restClient.ExecuteAsync<NetsisTokenResponse>(request);

                _logger.LogInformation("🔥 Token response - Success: {IsSuccessful}, StatusCode: {StatusCode}",
                    httpResult.IsSuccessful, httpResult.StatusCode);
                _logger.LogInformation("🔥 Token response content: {Content}", httpResult.Content);

                if (httpResult.IsSuccessful && httpResult.Data != null && string.IsNullOrEmpty(httpResult.Data.error))
                {
                    _logger.LogInformation("✅ RestSharp token acquired successfully");
                    return httpResult.Data.access_token;
                }
                else
                {
                    _logger.LogError("❌ RestSharp token failed - Error: {Error}, Description: {ErrorDesc}",
                        httpResult.Data?.error, httpResult.Data?.error_description);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ RestSharp token exception");
                return null;
            }
        }

        private async Task<string> SendOrderViaRestSharp(XmainOrder order, string cariKod, string token)
        {
            try
            {
                _logger.LogInformation("🔥 Sending order via RestSharp...");

                var orderUrl = $"{_apiBaseUrl.TrimEnd('/')}/api/v2/ItemSlips";

                // ✅ Yeni RestSharp API - Token ile
                var options = new RestClientOptions(orderUrl)
                {
                    Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(token, "Bearer") // ✅ Options içinde
                };
                var restClient = new RestClient(options);

                // ✅ Netsis model sınıflarını kullan
                var netsisOrder = new NetsisOrder
                {
                    Seri = "F",
                    HesaplamalariYap = true,
                    KayitliNumaraOtomatikGuncellensin = true,
                    SeriliHesapla = true,
                    FatUst = new NetsisOrderHeader
                    {
                        Sube_Kodu = Convert.ToInt32(_branchCode),
                        CariKod = cariKod,
                        Tarih = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        FIYATTARIHI = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        Proje_Kodu = "b",
                        ACIKLAMA = $"Online Order: {order.OrderNumber}",
                        Tip = 0,
                        TIPI = 0,
                        KDV_DAHILMI = true,
                        DOVIZTIP = 1,
                        YUVARLAMA = 0.0,
                        KOD1 = "Y",
                        SIPARIS_NUMARASI = order.OrderNumber,
                        FiiliTarih = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        ENTEGRE_TRH = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        SIPARIS_TEST = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss")
                    },
                    Kalems = order.OrderLineItems.Take(1).Select((li, index) => new NetsisOrderItem
                    {
                        StokKodu = li.VariantSku,
                        STra_GCMIK = (double)li.Quantity,
                        STra_NF = (double)li.FinalPrice,
                        STra_BF = (double)li.FinalPrice,
                        STra_KDV = 0.0,
                        STra_KOD1 = "Y",
                        STra_TAR = order.OrderedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        STra_ACIKLAMA = "Online Order Item",
                        DEPO_KODU = "1",  // ✅ String olarak!
                        ReferansKodu = $"{order.OrderNumber}-{index + 1}",
                        ProjeKodu = "b",
                        Olcubr = 1,
                        CEVRIM = 1.0
                    }).ToList()
                };

                var request = new RestRequest("/", Method.Post);
                request.AddHeader("Content-Type", "application/json");
                request.AddJsonBody(netsisOrder); // ✅ Yeni API

                _logger.LogInformation("🔥 Sending order request...");

                var httpResult = await restClient.ExecuteAsync(request);

                _logger.LogInformation("🔥 Order response - Success: {IsSuccessful}, StatusCode: {StatusCode}",
                    httpResult.IsSuccessful, httpResult.StatusCode);
                _logger.LogInformation("🔥 Order response content: {Content}", httpResult.Content);

                if (httpResult.IsSuccessful)
                {
                    // Response'u parse et
                    var apiResponse = JsonSerializer.Deserialize<NetsisApiResponse>(httpResult.Content, _jsonSerializerOptions);

                    if (apiResponse?.IsSuccessful != false)
                    {
                        _logger.LogInformation("✅ RestSharp SUCCESS for {OrderNumber}", order.OrderNumber);
                        return $"RESTSHARP_OK:{order.OrderNumber}";
                    }
                    else
                    {
                        _logger.LogError("❌ RestSharp API Error: {ErrorCode} - {ErrorDesc}",
                            apiResponse?.ErrorCode, apiResponse?.ErrorDesc);
                        return "RESTSHARP_API_ERROR";
                    }
                }
                else
                {
                    _logger.LogError("❌ RestSharp HTTP Error: {StatusCode} - {Content}",
                        httpResult.StatusCode, httpResult.Content);
                    return "RESTSHARP_HTTP_ERROR";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ RestSharp send order exception");
                return "RESTSHARP_SEND_EXCEPTION";
            }
        }

    }
}