﻿{
  "$schema": "http://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      // IIS Express kullanmıyorsak bu bölüm çok önemli değil
      "applicationUrl": "http://localhost:20677",
      "sslPort": 44370
    }
  },
  "profiles": {
    "http": { // Bu profil genellikle varsayılan olarak kullanılır
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true, // API test için Swagger açılır
      "launchUrl": "swagger",
      "applicationUrl": "http://localhost:5280", // SUNUCUNUN ÇALIŞACAĞI PORT (appsettings.json ile tutarlı)
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "https": { // HTTPS kullanacaksanız
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      // HTTPS için farklı bir port ve HTTP için de 5280 tanımlanabilir
      "applicationUrl": "https://localhost:5281;http://localhost:5280",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "IIS Express": { // IIS Express kullanmıyorsak bu bölüm çok önemli değil
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}












//{
//  "$schema": "http://json.schemastore.org/launchsettings.json",
//  "iisSettings": {
//    "windowsAuthentication": false,
//    "anonymousAuthentication": true,
//    "iisExpress": {
//      "applicationUrl": "http://localhost:20677",
//      "sslPort": 44370
//    }
//  },
//  "profiles": {
//    "http": {
//      "commandName": "Project",
//      "dotnetRunMessages": true,
//      "launchBrowser": true,
//      "launchUrl": "swagger",
//      "applicationUrl": "http://localhost:5280",
//      "environmentVariables": {
//        "ASPNETCORE_ENVIRONMENT": "Development"
//      }
//    },
//    "https": {
//      "commandName": "Project",
//      "dotnetRunMessages": true,
//      "launchBrowser": true,
//      "launchUrl": "swagger",
//      "applicationUrl": "https://localhost:7266;http://localhost:5280",
//      "environmentVariables": {
//        "ASPNETCORE_ENVIRONMENT": "Development"
//      }
//    },
//    "IIS Express": {
//      "commandName": "IISExpress",
//      "launchBrowser": true,
//      "launchUrl": "swagger",
//      "environmentVariables": {
//        "ASPNETCORE_ENVIRONMENT": "Development"
//      }
//    }
//  }
//}
