﻿// XECOM_Main_LocalServer\Models\XmainOrder.cs
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XECOM_Main_LocalServer.Models
{
    [Table("XmainOrders")] // Örnek tablo adı
    public class XmainOrder
    {
        [Key]
        public long Id { get; set; } // Sunucu tarafı DB ID

        public long ClientOrderId { get; set; } // İstemciden gelen siparişin ID'si
        public string IkasId { get; set; } // İstemcideki IksOrder.IkasId (eğer mapleniyorsa)
        [Required]
        [MaxLength(50)] // Veritabanı kısıtlamalarına uygun
        public string OrderNumber { get; set; }
        public DateTime OrderedAt { get; set; }

        [Column(TypeName = "decimal(18, 4)")]
        public decimal? TotalPrice { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? TotalFinalPrice { get; set; }

        // Foreign Keys
        public long? BillingAddressId { get; set; }
        public long? ShippingAddressId { get; set; }

        // Navigation Properties
        [ForeignKey(nameof(BillingAddressId))]
        public virtual XmainAddress BillingAddress { get; set; }

        [ForeignKey(nameof(ShippingAddressId))]
        public virtual XmainAddress ShippingAddress { get; set; }

        public virtual ICollection<XmainOrderLineItem> OrderLineItems { get; set; }
        public virtual ICollection<XmainOrderPackage> OrderPackages { get; set; } // Eklendi

        // Sunucu tarafı takip için
        public DateTime CreatedAtUtc { get; set; }
        public DateTime? UpdatedAtUtc { get; set; }
        // public string Status { get; set; } // Siparişin sunucudaki durumu (örn: PendingNetsis, SentToNetsis, Error)

        public XmainOrder()
        {
            OrderLineItems = new HashSet<XmainOrderLineItem>(); // HashSet kullanmak daha iyi olabilir
            OrderPackages = new HashSet<XmainOrderPackage>();  // HashSet kullanmak daha iyi olabilir
            CreatedAtUtc = DateTime.UtcNow;
        }
    }
}