﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using XECOM_Main_LocalServer.Data;
using XECOM_Main_LocalServer.Models;
using XECOM_Main_LocalServer.Services;

namespace XECOM_Main_LocalServer.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly ILogger<TestController> _logger;
        private readonly ERPService _erpService;
        private readonly AppDbContext _dbContext;

        public TestController(
            ILogger<TestController> logger,
            ERPService erpService,
            AppDbContext dbContext)
        {
            _logger = logger;
            _erpService = erpService;
            _dbContext = dbContext;
        }

        [HttpGet("erp/{orderNumber}")]
        public async Task<IActionResult> TestERP(string orderNumber)
        {
            _logger.LogInformation("============ TEST ERP STARTED ============");
            _logger.LogInformation("Test ERP çağrıldı: {orderNumber}", orderNumber);

            try
            {
                var order = await _dbContext.XmainOrders
                    .Include(o => o.OrderLineItems)
                    .Include(o => o.BillingAddress)
                    .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber);

                if (order == null)
                {
                    _logger.LogWarning("Sipariş bulunamadı: {orderNumber}", orderNumber);
                    return NotFound($"Sipariş bulunamadı: {orderNumber}");
                }

                _logger.LogInformation("Sipariş bulundu, ERP'ye gönderiliyor. OrderId: {id}, OrderNumber: {orderNumber}",
                    order.Id, order.OrderNumber);
                var result = await _erpService.CreateNetsisOrderAsync(order);

                _logger.LogInformation("ERP işlemi tamamlandı. Sonuç: {result}", result);
                _logger.LogInformation("============ TEST ERP FINISHED ============");

                return Ok(new
                {
                    Message = "ERP test tamamlandı",
                    OrderNumber = orderNumber,
                    Result = result,
                    Success = !string.IsNullOrEmpty(result)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Test ERP hatası: {message}", ex.Message);
                _logger.LogInformation("============ TEST ERP FINISHED WITH ERROR ============");
                return StatusCode(500, ex.Message);
            }
        }
    }
}
