﻿namespace XECOM_Main_LocalServer.Models.NetsisModels
{
    public class NetsisOrderItem
    {
        public string StokKodu { get; set; }
        public double STra_GCMIK { get; set; }     // ✅ GitHub'da int, bizde double
        public double STra_NF { get; set; }        // ✅ Net fiyat
        public double STra_BF { get; set; }        // ✅ Brüt fiyat
        public double STra_KDV { get; set; } = 0.0;
        public string STra_KOD1 { get; set; } = "Y";
        public string STra_TAR { get; set; }
        public string STra_ACIKLAMA { get; set; }
        public string MuhasebeKodu { get; set; }
        public string ReferansKodu { get; set; }
        public string ProjeKodu { get; set; } = "b";
        public string <PERSON><PERSON>an { get; set; }
        public string DEPO_KODU { get; set; } = "1";  // ✅ String olarak!
        public string STra_GC { get; set; }
        public int Olcubr { get; set; } = 1;
        public double CEVRIM { get; set; } = 1.0;
    }
}