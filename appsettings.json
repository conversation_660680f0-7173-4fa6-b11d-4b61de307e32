﻿{
  "ConnectionStrings": {
    //"DefaultConnection": "Server=DESKTOP-4PMOKM2;Database=AKAL2024-04;User Id=sa;Password=********;Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;"
    "DefaultConnection": "Server=*************;Database=AKAL2025x05;User Id=ahmet;Password=*********;Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;"
  },
  "Encryption": {
    "Key": "BwmKp3kZjCm2FJkzEmXz/RlV9V7I9mxIvF9ZtbHtFa0=", // İSTEMCİ İLE AYNI OLMALI
    "IV": "Q9G5V/Lm+ZtJ2HVFZVtWFA==" // İSTEMCİ İLE AYNI OLMALI
  },
  "UseHttps": false, // Geliştirme için false, production için true yapın
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information", // Production için Information veya Warning
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "XECOM_Main_LocalServer.Services.ERPService": "Debug" // ERPService için detaylı log
      }
    }
    // WriteTo ayarları Program.cs'den yönetiliyor, burada tekrar gerek yok
    // Eğer Program.cs'de ReadFrom.Configuration(builder.Configuration) kullanılıyorsa
    // ve WriteTo.File burada tanımlıysa, Program.cs'teki WriteTo.File'ı kaldırın.
    // Şimdilik Program.cs'teki daha iyi.
  },
  "Kestrel": { // Kestrel'i doğrudan buradan konfigüre edelim
    "Endpoints": {
      "Http": {
        "Url": "http://*:5280" // SUNUCUNUN DİNLEYECEĞİ PORT
      }
      // HTTPS kullanacaksanız:
      // "Https": {
      //   "Url": "https://localhost:5281", // Farklı bir port
      //   "Certificate": {
      //     "Path": "<path_to_your_pfx_file>",
      //     "Password": "<your_certificate_password>"
      //   }
      // }
    }
  },
  "Netsis": {
    "ApiBaseUrl": "http://localhost:7070/", // Netsis API adresiniz
    "Username": "NETSIS",
    "Password": "AKAL2013",
    "DbName": "AKAL2025x05",
    "DbUser": "TEMELSET",
    "DbPassword": "",
    "DbType": "0", // Veya Netsis'in beklediği sayısal kod
    "BranchCode": "203",
    "TimeoutSeconds": 60 // HttpClient için timeout (saniye)
  },
  "Cors": {
    "AllowedOrigins": [
      // İstemcinizin bir web arayüzü varsa onun origin'i.
      // SignalR için konsol istemcisi doğrudan IP/port üzerinden bağlandığı için
      // bu ayar doğrudan bir engel oluşturmayabilir ama tanımlı olması iyidir.
      // Tarayıcı tabanlı bir test aracı kullanıyorsanız (örn: Swagger UI'dan farklı bir yerden test)
      // o origin'i eklemeniz gerekir.
      "http://localhost" // Geliştirme için genel bir tanım
    ]
  },
  "AllowedHosts": "*",
  "EnableDataSyncService": false // DataSyncService'i çalıştırmak için true yapın
  // "applicationUrl" satırını Kestrel.Endpoints kullandığımız için kaldırabiliriz, kafa karıştırmasın.
}





// OLD Settings
//{
//  "ConnectionStrings": {
//    //"DefaultConnection": "Server=**************;Database=AKAL2024-04;User Id=sa;Password=*******************;Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;"
//    //"DefaultConnection": "Server=*************;Database=AKAL2024-04;User Id=ahmet;Password=*********;Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;"
//    "DefaultConnection": "Server=DESKTOP-4PMOKM2;Database=AKAL2024-04;User Id=sa;Password=********;Connect Timeout=30;Encrypt=True;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;"
//  },
//  "Encryption": {
//    "Key": "BwmKp3kZjCm2FJkzEmXz/RlV9V7I9mxIvF9ZtbHtFa0=",
//    "IV": "Q9G5V/Lm+ZtJ2HVFZVtWFA=="
//  },
//  "UseHttps": false,
//  "UseChangeTracking": false,
//  "Serilog": {
//    "MinimumLevel": {
//      "Default": "Information",
//      "Override": {
//        "Microsoft": "Warning",
//        "System": "Warning"
//      }
//    },
//    "Logging": {
//      "LogLevel": {
//        "Default": "Debug",
//        "Microsoft": "Information",
//        "Microsoft.Hosting.Lifetime": "Information",
//        "XECOM_Main_LocalServer.Services.ERPService": "Debug"
//      }
//    },
//    "WriteTo": [
//      {
//        "Name": "File",
//        "Args": {
//          "path": "logs/log-.txt",
//          "rollingInterval": "Day"
//        }
//      }
//    ]
//  },
//  "Netsis": {
//    "ApiBaseUrl": "http://localhost:7070/",
//    "Username": "NETSIS",
//    "Password": "AKAL2013",
//    "DbName": "AKAL2024-04",
//    "DbUser": "ahmet",
//    "DbPassword": "*********",
//    "DbType": "MSSQL",
//    "BranchCode": "203"
//  },
//  "AllowedHosts": "*",
//  "applicationUrl": "http://localhost:5280"
//}