{"openapi": "3.0.1", "info": {"title": "XECOM Main LocalServer API", "version": "v1"}, "paths": {"/api/Data": {"get": {"tags": ["Data"], "responses": {"200": {"description": "OK"}}}}, "/api/Test/erp/{orderNumber}": {"get": {"tags": ["Test"], "parameters": [{"name": "orderNumber", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {}}