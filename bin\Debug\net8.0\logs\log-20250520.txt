2025-05-20 17:43:07.594 +03:00 [INF] Netsis API yapılandırması: URL=http://localhost:7070/, Username=NETSIS, DbName=AKAL2024-04
2025-05-20 17:43:07.639 +03:00 [INF] Netsis API yapılandırması başarıyla yüklendi.
2025-05-20 17:43:07.640 +03:00 [INF] Uygulama başlatılıyor
2025-05-20 17:43:07.654 +03:00 [INF] ExecuteStoredProcedure metodu çağrıldı
2025-05-20 17:43:08.923 +03:00 [WRN] Overriding address(es) 'http://localhost:5279'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-20 17:43:09.037 +03:00 [INF] Stored procedure NSP_NETSESGUNCELLE başar<PERSON>yla çalıştırıldı
2025-05-20 17:43:09.038 +03:00 [INF] ExecuteStoredProcedure metodu tamamlandı
2025-05-20 17:43:09.815 +03:00 [INF] Toplam ürün sayısı: 687
2025-05-20 17:43:09.901 +03:00 [INF] Tam veri senkronizasyonu kullanılarak 687 ürün client'lara gönderildi
2025-05-20 17:43:20.059 +03:00 [INF] ============ ReceiveOrders STARTED ============
2025-05-20 17:43:20.061 +03:00 [INF] ReceiveOrders is called with encrypted data length: 4736
2025-05-20 17:43:20.065 +03:00 [INF] Decryption and cleaning successful, length: 3536, first 100 chars: [{"Id":1142,"OrderNumber":"21085","OrderedAt":"2025-05-19T16:42:16.295","Deleted":false,"LineItems":
