namespace XECOM_Main_LocalServer.Models.NetsisModels
{
    public class NetsisOrderHeader
    {
        public int Sube_Kodu { get; set; }
        public string CariKod { get; set; }
        public string Tarih { get; set; }
        public string FIYATTARIHI { get; set; }
        public string <PERSON>je_Kodu { get; set; } = "b";
        public string AC<PERSON><PERSON><PERSON><PERSON> { get; set; }
        public string EKACK1 { get; set; }
        public int Tip { get; set; } = 0;
        public int TIPI { get; set; } = 0;
        public bool KDV_DAHILMI { get; set; } = true;
        public int DOVIZTIP { get; set; } = 1;
        public double YUVARLAMA { get; set; } = 0.0;
        public string FATIRS_NO { get; set; }
        public string KOD1 { get; set; } = "Y";
        public string SIPARIS_TEST { get; set; }
        public string SIPARIS_NUMARASI { get; set; }
        public string FiiliTarih { get; set; }
        public string ENTEGRE_TRH { get; set; }
    }
}