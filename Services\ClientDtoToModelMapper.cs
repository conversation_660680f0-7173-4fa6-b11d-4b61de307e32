﻿// XECOM_Main_LocalServer\Services\ClientDtoToModelMapper.cs
using System.Linq;
using XECOM_Main_LocalServer.Models;
using XECOM_Main_LocalServer.Models.DTOsFromClient; // İstemciden gelen DTO'lar için

namespace XECOM_Main_LocalServer.Services
{
    public static class ClientDtoToModelMapper
    {
        public static XmainOrder ToXmainOrder(this OrderClientDto clientDto)
        {
            if (clientDto == null) return null;

            var xmainOrder = new XmainOrder
            {
                ClientOrderId = clientDto.Id,
                OrderNumber = clientDto.OrderNumber,
                OrderedAt = clientDto.OrderedAt ?? System.DateTime.UtcNow, // Null ise varsayılan ata
                TotalPrice = clientDto.TotalPrice,
                TotalFinalPrice = clientDto.TotalFinalPrice,
                BillingAddress = clientDto.BillingAddress?.ToXmainAddress(),
                OrderLineItems = clientDto.LineItems?
                                    .Select(li => li.ToXmainOrderLineItem())
                                    .ToList() ?? new System.Collections.Generic.List<XmainOrderLineItem>()
                // Packages şimdilik maplenmiyor, gerekirse eklenebilir
            };

            return xmainOrder;
        }

        public static XmainAddress ToXmainAddress(this BillingAddressClientDto clientDto)
        {
            if (clientDto == null) return null;

            return new XmainAddress
            {
                FirstName = clientDto.FirstName,
                LastName = clientDto.LastName,
                Company = clientDto.Company,
                Phone = clientDto.Phone,
                // Email = clientDto.Email, // İstemcide yoktu
                AddressLine1 = clientDto.AddressLine1,
                AddressLine2 = clientDto.AddressLine2,
                CityName = clientDto.CityName,
                DistrictName = clientDto.DistrictName,
                StateName = clientDto.StateName,
                PostalCode = clientDto.PostalCode,
                CountryName = clientDto.CountryName,
                TaxOffice = clientDto.TaxOffice,
                TaxNumber = clientDto.TaxNumber,
                IdentityNumber = clientDto.IdentityNumber
            };
        }

        public static XmainOrderLineItem ToXmainOrderLineItem(this OrderLineItemClientDto clientDto)
        {
            if (clientDto == null) return null;

            return new XmainOrderLineItem
            {
                ClientLineItemId = clientDto.Id,
                VariantSku = clientDto.VariantSku,
                VariantName = clientDto.VariantName,
                Quantity = clientDto.Quantity ?? 0, // Null ise 0 ata
                Price = clientDto.Price ?? 0,       // Null ise 0 ata
                FinalPrice = clientDto.FinalPrice ?? 0 // Null ise 0 ata
            };
        }
    }
}