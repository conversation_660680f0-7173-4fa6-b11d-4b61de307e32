2024-10-13 16:00:27.679 +03:00 [WRN] No store type was specified for the decimal property 'Discount_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:00:27.717 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:00:27.720 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:00:27.721 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:00:27.722 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:00:27.723 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:00:27.724 +03:00 [WRN] No store type was specified for the decimal property 'Regular_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:03:34.212 +03:00 [WRN] No store type was specified for the decimal property 'Discount_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:03:34.255 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing1_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:03:34.258 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing2_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:03:34.260 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing3_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:03:34.261 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing4_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:03:34.262 +03:00 [WRN] No store type was specified for the decimal property 'Material_Upper_Material_Clothing5_Ratio' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:03:34.264 +03:00 [WRN] No store type was specified for the decimal property 'Regular_Price' on entity type 'Product'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2024-10-13 16:03:35.631 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Unable to resolve service for type 'Swashbuckle.AspNetCore.Swagger.ISwaggerProvider' while attempting to Invoke middleware 'Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware'.
   at lambda_method19(Closure, Object, HttpContext, IServiceProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2024-10-13 16:03:35.896 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Unable to resolve service for type 'Swashbuckle.AspNetCore.Swagger.ISwaggerProvider' while attempting to Invoke middleware 'Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware'.
   at lambda_method19(Closure, Object, HttpContext, IServiceProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
