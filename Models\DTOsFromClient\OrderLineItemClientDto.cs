﻿// XECOM_Main_LocalServer\Models\DTOsFromClient\OrderLineItemClientDto.cs
namespace XECOM_Main_LocalServer.Models.DTOsFromClient
{
    public class OrderLineItemClientDto
    {
        public long Id { get; set; } // İstemcideki Id
        public string VariantSku { get; set; }
        public string VariantName { get; set; }
        public int? Quantity { get; set; }
        public decimal? Price { get; set; }
        public decimal? FinalPrice { get; set; }
    }
}