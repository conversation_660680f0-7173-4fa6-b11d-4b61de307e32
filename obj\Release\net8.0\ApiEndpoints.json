[{"ContainingType": "XECOM_Main_LocalServer.Controllers.DataController", "Method": "GetProducts", "RelativePath": "api/Data", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "XECOM_Main_LocalServer.Controllers.TestController", "Method": "TestERP", "RelativePath": "api/Test/erp/{orderNumber}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderNumber", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}]