﻿// XECOM_Main_LocalServer\Models\XmainOrderPackage.cs
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace XECOM_Main_LocalServer.Models
{
    [Table("XmainOrderPackages")] // Örnek tablo adı
    public class XmainOrderPackage
    {
        [Key]
        public long Id { get; set; }

        public long ClientPackageId { get; set; } // İstemcideki Package Id'si
        public string OrderPackageNumber { get; set; }
        // Diğer alanlar eklenebilir (TrackingNo, CargoCompany vb.)
        // public bool? Deleted { get; set; } // İstemciden geliyorsa

        // Foreign key for XmainOrder
        public long OrderId { get; set; }

        [JsonIgnore]
        [ForeignKey(nameof(OrderId))]
        public virtual XmainOrder Order { get; set; }
    }
}