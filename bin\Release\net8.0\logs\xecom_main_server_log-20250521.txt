2025-05-21 00:00:56.303 +03:00 [FTL] XECOM Main LocalServer - Application FAILED to start.
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Program.cs:line 165
2025-05-21 00:00:56.327 +03:00 [INF] ============================================================
2025-05-21 00:00:56.327 +03:00 [INF] XECOM Main LocalServer - Application Shutting Down / Stopped.
2025-05-21 00:00:56.328 +03:00 [INF] ============================================================
2025-05-21 16:10:53.585 +03:00 [INF] Application built successfully.
2025-05-21 16:10:53.607 +03:00 [INF] Step 6: Configuring the HTTP request pipeline...
2025-05-21 16:10:53.639 +03:00 [INF] HTTPS redirection is disabled (UseHttps=false in appsettings.json).
2025-05-21 16:10:53.868 +03:00 [INF] SignalR Hub '/dataSyncHub' mapped.
2025-05-21 16:10:53.869 +03:00 [INF] HTTP request pipeline configuration complete.
2025-05-21 16:10:53.870 +03:00 [INF] Step 7: Performing pre-run checks (Netsis config)...
2025-05-21 16:10:53.871 +03:00 [INF] Netsis API configuration check PASSED. ApiBaseUrl: http://localhost:7070/
2025-05-21 16:10:53.872 +03:00 [INF] Pre-run checks complete.
2025-05-21 16:10:53.873 +03:00 [INF] Step 8: Starting the application (app.Run())...
2025-05-21 16:10:54.008 +03:00 [WRN] Overriding address(es) 'http://localhost:5280'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-21 16:11:15.082 +03:00 [FTL] XECOM Main LocalServer - Application FAILED to start.
Microsoft.Extensions.Hosting.HostAbortedException: The host was aborted.
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.ThrowHostAborted()
   at Microsoft.Extensions.Hosting.HostFactoryResolver.HostingListener.OnNext(KeyValuePair`2 value)
   at System.Diagnostics.DiagnosticListener.Write(String name, Object value)
   at Microsoft.Extensions.Hosting.HostBuilder.ResolveHost(IServiceProvider serviceProvider, DiagnosticListener diagnosticListener)
   at Microsoft.Extensions.Hosting.HostApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.WebApplicationBuilder.Build()
   at Program.<Main>$(String[] args) in D:\Qix\NordBroun\XECOM LocalServer\XECOM Main-LocalServer\Program.cs:line 165
2025-05-21 16:11:15.112 +03:00 [INF] ============================================================
2025-05-21 16:11:15.112 +03:00 [INF] XECOM Main LocalServer - Application Shutting Down / Stopped.
2025-05-21 16:11:15.112 +03:00 [INF] ============================================================
