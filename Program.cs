﻿// XECOM_Main_LocalServer\Program.cs
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.RateLimiting;
using XECOM_Main_LocalServer.Data;
using XECOM_Main_LocalServer.Services;

Log.Logger = new LoggerConfiguration()
    .Enrich.FromLogContext()
    .WriteTo.Console() // Başlangıç logları için konsol da açık kalsın
    .CreateBootstrapLogger(); // Uygulama tam başlayana kadar temel loglama

Log.Information("============================================================");
Log.Information("XECOM Main LocalServer - Application Starting...");
Log.Information("============================================================");

try
{
    var builder = WebApplication.CreateBuilder(args);

    // 1) Serilog Tam Yapılandırması (appsettings.json'dan okuyacak şekilde)
    Log.Information("Step 1: Configuring Serilog...");
    var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
    Directory.CreateDirectory(logPath);
    builder.Host.UseSerilog((context, services, loggerConfiguration) => loggerConfiguration
        .ReadFrom.Configuration(context.Configuration) // appsettings.json'daki Serilog bölümünü okur
        .Enrich.FromLogContext()
        .WriteTo.Console(outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
        .WriteTo.File(Path.Combine(logPath, "xecom_main_server_log-.txt"),
            rollingInterval: RollingInterval.Day,
            outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"));
    Log.Information("Serilog configured successfully.");

    // 2) appsettings.json (Zaten builder tarafından otomatik yükleniyor, ek AddJsonFile'a gerek yok)
    Log.Information("Step 2: Configuration sources (appsettings.json, environment variables) loaded by WebApplication.CreateBuilder.");

    // 3) Kestrel Yapılandırması (appsettings.json'dan Kestrel:Endpoints bölümünü kullanacak)
    Log.Information("Step 3: Configuring Kestrel...");
    // builder.Configuration içindeki Kestrel ayarlarını otomatik olarak yükler.
    // Eski manuel port ayarlama kodunu kaldırıyoruz.
    builder.WebHost.UseConfiguration(builder.Configuration);
    var kestrelHttpUrl = builder.Configuration["Kestrel:Endpoints:Http:Url"]; // appsettings.json'dan kontrol
    if (!string.IsNullOrEmpty(kestrelHttpUrl))
    {
        Log.Information("Kestrel will be configured to listen on: {Url} (from Kestrel:Endpoints:Http:Url in appsettings.json)", kestrelHttpUrl);
    }
    else
    {
        // Eğer Kestrel:Endpoints:Http:Url yoksa, eski applicationUrl veya launchSettings.json'a bakabilir
        var appUrlFromConfig = builder.Configuration["applicationUrl"];
        if (!string.IsNullOrEmpty(appUrlFromConfig))
        {
            Log.Warning("Kestrel:Endpoints:Http:Url not found in appsettings.json. Kestrel might use 'applicationUrl': {AppUrl} or settings from launchSettings.json.", appUrlFromConfig);
            // Bu durumda launchSettings.json'daki URL'in doğru (5280) olduğundan emin olun.
            // En iyisi Kestrel:Endpoints kullanmaktır.
        }
        else
        {
            Log.Error("CRITICAL: Kestrel listening URL could not be determined from appsettings.json (Kestrel:Endpoints:Http:Url or applicationUrl). Please check configuration. Falling back to launchSettings or defaults.");
        }
    }


    Log.Information("Step 4: Registering services...");
    // 4) Service kayıtları
    builder.Services.AddSignalR(options =>
    {
        options.EnableDetailedErrors = builder.Environment.IsDevelopment();
        Log.Debug("SignalR configured with EnableDetailedErrors={IsDev}", builder.Environment.IsDevelopment());
    });
    builder.Services.AddControllers();
    Log.Debug("Controllers registered.");

    Log.Debug("Registering AppDbContext with connection string: {ConnectionString}", builder.Configuration.GetConnectionString("DefaultConnection")?.Substring(0, builder.Configuration.GetConnectionString("DefaultConnection")?.IndexOf("Password=") ?? 0) + "PASSWORD_REDACTED");
    builder.Services.AddDbContext<AppDbContext>(options =>
        options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")),
        ServiceLifetime.Scoped);

    // EncryptionService static olduğu için DI'ye kaydetmiyoruz.

    builder.Services.AddHttpClient(); // Genel HttpClientFactory
    Log.Debug("HttpClientFactory registered.");

    var netsisTimeoutSeconds = builder.Configuration.GetValue<int>("Netsis:TimeoutSeconds", 60);
    builder.Services.AddHttpClient("NetsisApiClient", client =>
    {
        client.Timeout = TimeSpan.FromSeconds(netsisTimeoutSeconds);
    });
    Log.Debug("Named HttpClient 'NetsisApiClient' registered with timeout: {Timeout}s", netsisTimeoutSeconds);

    builder.Services.AddScoped<ERPService>();
    Log.Debug("ERPService registered as Scoped.");

    if (builder.Configuration.GetValue<bool>("EnableDataSyncService", false))
    {
        builder.Services.AddHostedService<DataSyncService>();
        Log.Information("DataSyncService is enabled and will be started as HostedService.");
    }
    else
    {
        Log.Information("DataSyncService is disabled (EnableDataSyncService=false in appsettings.json).");
    }

    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo { Title = "XECOM Main LocalServer API", Version = "v1" });
    });
    Log.Debug("Swagger/OpenAPI registered.");

    var rateLimitPermit = builder.Configuration.GetValue<int>("RateLimiting:PermitLimit", 100);
    var rateLimitWindow = builder.Configuration.GetValue<int>("RateLimiting:WindowInSeconds", 60);
    builder.Services.AddRateLimiter(options =>
    {
        var rateLimitConfig = builder.Configuration.GetSection("RateLimiting"); // Bunu yukarı taşımıştım
        options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(context =>
            RateLimitPartition.GetFixedWindowLimiter(
                partitionKey: context.Connection.RemoteIpAddress?.ToString() ?? "DefaultPartition",
                factory: partition => new FixedWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = rateLimitPermit,
                    QueueLimit = rateLimitConfig.GetValue<int>("QueueLimit", 20),
                    Window = TimeSpan.FromSeconds(rateLimitWindow)
                }));
        options.RejectionStatusCode = StatusCodes.Status429TooManyRequests;
    });
    Log.Debug("RateLimiter configured: Permit={Permit}/Window={Window}s", rateLimitPermit, rateLimitWindow);

    var allowedOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>() ?? Array.Empty<string>();
    if (allowedOrigins.Any())
    {
        builder.Services.AddCors(options =>
        {
            options.AddPolicy("AllowSpecificOrigins",
                policy =>
                {
                    policy.WithOrigins(allowedOrigins)
                          .AllowAnyMethod()
                          .AllowAnyHeader()
                          .AllowCredentials();
                });
        });
        Log.Information("CORS policy 'AllowSpecificOrigins' configured for: {Origins}", string.Join(", ", allowedOrigins));
    }
    else
    {
        Log.Warning("CORS AllowedOrigins not configured in appsettings.json. SignalR/API calls from different browser origins might fail.");
    }
    Log.Information("Service registration complete.");


    Log.Information("Step 5: Building the application...");
    var app = builder.Build();
    Log.Information("Application built successfully.");

    Log.Information("Step 6: Configuring the HTTP request pipeline...");
    // 5) Middleware
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "XECOM Main LocalServer API v1"));
        Log.Debug("Swagger UI and Middleware enabled for Development environment.");
        app.UseDeveloperExceptionPage();
        Log.Debug("DeveloperExceptionPage enabled for Development environment.");
    }
    else
    {
        // Production'da genel bir hata sayfası
        app.UseExceptionHandler("/Error"); // Örnek, ErrorController veya Razor Page'iniz olmalı
        app.UseHsts(); // HTTPS kullanıyorsanız
        Log.Information("Production environment: ExceptionHandler and HSTS (if HTTPS) configured.");
    }

    var useHttps = builder.Configuration.GetValue<bool>("UseHttps");
    if (useHttps)
    {
        app.UseHttpsRedirection();
        Log.Information("HTTPS redirection middleware enabled.");
    }
    else
    {
        Log.Information("HTTPS redirection is disabled (UseHttps=false in appsettings.json).");
    }


    if (allowedOrigins.Any())
    {
        app.UseCors("AllowSpecificOrigins");
        Log.Debug("CORS middleware 'AllowSpecificOrigins' enabled.");
    }

    app.UseRouting();
    Log.Debug("Routing middleware enabled.");

    app.UseRateLimiter();
    Log.Debug("RateLimiter middleware enabled.");

    // app.UseAuthentication(); // Kimlik doğrulama kullanılıyorsa
    // app.UseAuthorization();  // Yetkilendirme kullanılıyorsa

    app.MapControllers();
    Log.Debug("Controllers mapped.");
    app.MapHub<DataSyncHub>("/dataSyncHub");
    Log.Information("SignalR Hub '/dataSyncHub' mapped.");

    app.Use(async (context, next) =>
    {
        context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
        context.Response.Headers.Append("X-Frame-Options", "DENY");
        context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
        context.Response.Headers.Append("Referrer-Policy", "strict-origin-when-cross-origin");
        await next();
    });
    Log.Debug("Security headers middleware configured.");
    Log.Information("HTTP request pipeline configuration complete.");

    Log.Information("Step 7: Performing pre-run checks (Netsis config)...");
    try
    {
        var netsisConfig = builder.Configuration.GetSection("Netsis");
        var missingKeys = new List<string>();
        if (string.IsNullOrEmpty(netsisConfig["ApiBaseUrl"])) missingKeys.Add("Netsis:ApiBaseUrl");
        if (string.IsNullOrEmpty(netsisConfig["Username"])) missingKeys.Add("Netsis:Username");
        if (string.IsNullOrEmpty(netsisConfig["Password"])) missingKeys.Add("Netsis:Password");
        if (string.IsNullOrEmpty(netsisConfig["DbName"])) missingKeys.Add("Netsis:DbName");
        if (string.IsNullOrEmpty(netsisConfig["DbUser"])) missingKeys.Add("Netsis:DbUser");
        if (string.IsNullOrEmpty(netsisConfig["DbPassword"])) missingKeys.Add("Netsis:DbPassword");
        if (string.IsNullOrEmpty(netsisConfig["DbType"])) missingKeys.Add("Netsis:DbType");
        if (string.IsNullOrEmpty(netsisConfig["BranchCode"])) missingKeys.Add("Netsis:BranchCode");

        if (missingKeys.Any())
        {
            Log.Warning("Netsis API configuration is INCOMPLETE. Missing keys: {MissingKeys}. Please check appsettings.json.", string.Join(", ", missingKeys));
        }
        else
        {
            Log.Information("Netsis API configuration check PASSED. ApiBaseUrl: {Url}", netsisConfig["ApiBaseUrl"]);
        }
    }
    catch (Exception configEx)
    {
        Log.Error(configEx, "Error during Netsis API configuration check.");
    }
    Log.Information("Pre-run checks complete.");

    Log.Information("Step 8: Starting the application (app.Run())...");
    app.Run(); // Bu satır bloklayıcıdır, uygulama çalışmaya başlar ve burada bekler.
               // Kestrel'in "Now listening on..." mesajı bu satırdan sonra gelir.
}
catch (Exception ex)
{
    Log.Fatal(ex, "XECOM Main LocalServer - Application FAILED to start.");
    throw; // Hatanın daha yukarıya gitmesini sağlar (opsiyonel)
}
finally
{
    Log.Information("============================================================");
    Log.Information("XECOM Main LocalServer - Application Shutting Down / Stopped.");
    Log.Information("============================================================");
    Log.CloseAndFlush(); // Serilog'un tüm logları yazdığından emin ol
}