﻿// XECOM_Main_LocalServer\Services\DataSyncService.cs
using Microsoft.AspNetCore.SignalR; // IHubContext için
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration; // IConfiguration için
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
// Serilog using'i eğer global Log.Information vb. kullanılacaksa.
// ILogger<T> kullanıldığı için doğrudan Serilog namespace'ine gerek yok.
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using XECOM_Main_LocalServer.Data;
using XECOM_Main_LocalServer.Models; // Product, ProductChange (varsa)

// EncryptionService namespace'i zaten XECOM_Main_LocalServer.Services olduğu için ek using'e gerek yok.

namespace XECOM_Main_LocalServer.Services
{
    public class DataSyncService : BackgroundService
    {
        private readonly IServiceProvider _services;
        private readonly ILogger<DataSyncService> _logger;
        private readonly IConfiguration _configuration; // EncryptionService metotları için IConfiguration
        // private readonly EncryptionService _encryptionService; // BU SATIR KALDIRILDI (static olduğu için)

        private long _lastSyncVersion = 0; // SQL Change Tracking için

        // Constructor'dan EncryptionService parametresi KALDIRILDI.
        public DataSyncService(
            IServiceProvider services,
            ILogger<DataSyncService> logger,
            IConfiguration configuration)
        {
            _services = services;
            _logger = logger;
            _configuration = configuration; // IConfiguration atandı
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("DataSyncService (BackgroundService) starting.");

            // Uygulama başlarken bir kerelik stored procedure çalıştırma (opsiyonel, isteniyorsa)
            try
            {
                await ExecuteStoredProcedureInitial();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during initial execution of StoredProcedure in DataSyncService.");
            }


            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("DataSyncService work cycle started at: {time}", DateTimeOffset.Now);
                try
                {
                    // Stored procedure'ü her döngüde çalıştırmak yerine,
                    // belki belirli aralıklarla veya bir koşula bağlı çalıştırmak daha iyi olabilir.
                    // Şimdilik her döngüde çalışıyor.
                    // await ExecuteStoredProcedure(); // Eğer her döngüde çalışması gerekiyorsa

                    if (_configuration.GetValue<bool>("UseChangeTracking"))
                    {
                        await SyncUsingChangeTracking(stoppingToken);
                    }
                    else
                    {
                        await SyncAllData(stoppingToken);
                    }
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("DataSyncService stopping token was signaled. Work cycle terminating.");
                    break; // Döngüden çık
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "An error occurred during data synchronization in DataSyncService.");
                }

                try
                {
                    // Bekleme süresini appsettings'den almak daha esnek olabilir.
                    var delayMinutes = _configuration.GetValue<int>("DataSyncService:DelayMinutes", 1);
                    _logger.LogInformation("DataSyncService work cycle finished. Next run in {DelayMinutes} minutes.", delayMinutes);
                    await Task.Delay(TimeSpan.FromMinutes(delayMinutes), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("DataSyncService stopping token was signaled during delay. Terminating.");
                    break; // Döngüden çık
                }
            }
            _logger.LogInformation("DataSyncService (BackgroundService) stopped.");
        }

        private async Task ExecuteStoredProcedureInitial()
        {
            _logger.LogInformation("Executing Stored Procedure NSP_NETSESGUNCELLE (Initial Run)...");
            using var scope = _services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            try
            {
                // Parametre değerlerini appsettings'den almak daha iyi olabilir.
                await dbContext.Database.ExecuteSqlRawAsync(
                    "EXEC NSP_NETSESGUNCELLE @p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11",
                    parameters: new object[] { 2, "NETSIS", 2, 203, "E", "H", 2, -1, "H", 0, 0, "H" });
                _logger.LogInformation("Stored procedure NSP_NETSESGUNCELLE executed successfully (Initial Run).");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing stored procedure NSP_NETSESGUNCELLE (Initial Run).");
                // Hata durumunda ne yapılacağına karar verilmeli (örn: tekrar deneme, loglama vb.)
            }
        }

        private async Task SyncUsingChangeTracking(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Syncing data using Change Tracking...");
            using var scope = _services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var hubContext = scope.ServiceProvider.GetRequiredService<IHubContext<DataSyncHub>>();

            // ProductChange modelinin sunucu tarafında da tanımlı olması gerekir.
            // Eğer tanımlı değilse, bu sorgu hata verecektir.
            // Varsayım: XECOM_Main_LocalServer.Models altında ProductChange.cs var.
            List<ProductChange> changes = null;
            try
            {
                changes = await dbContext.Database.SqlQueryRaw<ProductChange>( // SqlQueryRaw kullanımı
                    $"SELECT CT.SYS_CHANGE_VERSION, CT.SYS_CHANGE_OPERATION, P.ID, P.Category, P.assortment_type AS AssortmentType, P.Name, P.Brand, P.Model_ID AS ModelID, P.SKU_ID, P.Target_Age_Groups, P.Target_Genders, P.Season, P.Description, P.Material_Upper_Material_Clothing1_Ratio, P.Material_Upper_Material_Clothing1, P.Material_Upper_Material_Clothing2_Ratio, P.Material_Upper_Material_Clothing2, P.Material_Upper_Material_Clothing3_Ratio, P.Material_Upper_Material_Clothing3, P.Material_Upper_Material_Clothing4_Ratio, P.Material_Upper_Material_Clothing4, P.Media1, P.Media2, P.Media3, P.Media4, P.Media5, P.Color_Code_Primary, P.Supplier_Color, P.Size, P.EAN, P.Stock_Quantity, P.regular_price AS RegularPrice, P.Discount, P.Currency, P.LanguageCode, P.CargoDesi, P.DeliveryCosts, P.UpdateDate, P.RecordDate FROM CHANGETABLE(CHANGES AlkProductList2, {_lastSyncVersion}) AS CT JOIN AlkProductList2 P ON P.ID = CT.PK_ID" // PK_ID doğru birincil anahtar adı olmalı
                ).ToListAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying change tracking data.");
                return;
            }


            if (changes != null && changes.Any())
            {
                _lastSyncVersion = changes.Max(c => c.SYS_CHANGE_VERSION);
                var changesArray = changes.ToArray();

                // EncryptionService static çağrısı ve _configuration parametresi
                // Bu metodun sunucu tarafındaki EncryptionService.cs'e eklenmiş olması gerekir.
                try
                {
                    string encryptedChanges = EncryptionService.EncryptChanges<ProductChange>(changesArray, _configuration);
                    await hubContext.Clients.All.SendAsync("ReceiveChanges", encryptedChanges, stoppingToken); // Hub metodunun adı "ReceiveChanges" olmalı
                    _logger.LogInformation("Change Tracking: {Count} product changes sent to clients. Last version: {Version}", changes.Count, _lastSyncVersion);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error encrypting or sending product changes via SignalR.");
                }
            }
            else
            {
                _logger.LogInformation("Change Tracking: No new product changes found since last sync version {Version}.", _lastSyncVersion);
            }
        }

        private async Task SyncAllData(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Syncing all product data...");
            using var scope = _services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var hubContext = scope.ServiceProvider.GetRequiredService<IHubContext<DataSyncHub>>();

            List<Product> products = null;
            try
            {
                // Varsayım: AppDbContext.Products DbSet<Product> olarak tanımlı.
                products = await dbContext.Products.AsNoTracking().ToListAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching all products from database.");
                return;
            }


            if (products != null && products.Any())
            {
                _logger.LogInformation("Total products fetched for sync: {Count}", products.Count);
                var productsArray = products.ToArray();

                // EncryptionService static çağrısı ve _configuration parametresi
                // Bu metodun sunucu tarafındaki EncryptionService.cs'e eklenmiş olması gerekir.
                try
                {
                    string encryptedData = EncryptionService.EncryptData<Product>(productsArray, _configuration);
                    await hubContext.Clients.All.SendAsync("ReceiveData", encryptedData, stoppingToken); // Hub metodunun adı "ReceiveData" olmalı
                    _logger.LogInformation("Full Sync: {Count} products sent to clients.", products.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error encrypting or sending all products via SignalR.");
                }
            }
            else
            {
                _logger.LogWarning("Full Sync: No products found in the database to synchronize.");
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("DataSyncService (BackgroundService) is stopping.");
            await base.StopAsync(stoppingToken);
        }
    }
}